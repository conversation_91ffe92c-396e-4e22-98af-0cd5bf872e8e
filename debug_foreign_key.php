<?php
// debug_foreign_key.php
// এই স্ক্রিপ্টটি duty_assignments টেবিলে ফরেন কী কনস্ট্রেইন্ট সমস্যা সনাক্ত করবে

// ডাটাবেস কানেকশন
$host = 'localhost';
$dbname = 'exmm';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->exec("SET NAMES utf8mb4");
    echo "<p>ডাটাবেস কানেকশন সফল!</p>";
} catch(PDOException $e) {
    die("ডাটাবেস কানেকশন ব্যর্থ: " . $e->getMessage());
}

// সমস্যাযুক্ত রেকর্ড খুঁজে বের করা
echo "<h2>সমস্যাযুক্ত রেকর্ড খুঁজে বের করা</h2>";
$sql = "SELECT da.teacher_id, da.duty_date, da.room_number 
        FROM duty_assignments da 
        LEFT JOIN teachers t ON da.teacher_id = t.id 
        WHERE t.id IS NULL";
$stmt = $pdo->prepare($sql);
$stmt->execute();
$invalidRecords = $stmt->fetchAll();

echo "<p>সমস্যাযুক্ত রেকর্ড সংখ্যা: " . count($invalidRecords) . "</p>";

if (count($invalidRecords) > 0) {
    echo "<h3>সমস্যাযুক্ত রেকর্ডগুলি:</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>শিক্ষক আইডি</th><th>ডিউটি তারিখ</th><th>রুম নম্বর</th></tr>";
    foreach ($invalidRecords as $record) {
        echo "<tr>";
        echo "<td>{$record['teacher_id']}</td>";
        echo "<td>{$record['duty_date']}</td>";
        echo "<td>{$record['room_number']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // সমাধানের জন্য SQL কোয়েরি
    echo "<h3>সমাধানের জন্য SQL কোয়েরি:</h3>";
    echo "<pre>";
    echo "-- অপশন ১: সমস্যাযুক্ত রেকর্ডগুলি মুছে ফেলা\n";
    echo "DELETE FROM duty_assignments \n";
    echo "WHERE teacher_id IN (\n";
    echo "    SELECT da.teacher_id \n";
    echo "    FROM (SELECT * FROM duty_assignments) da \n";
    echo "    LEFT JOIN teachers t ON da.teacher_id = t.id \n";
    echo "    WHERE t.id IS NULL\n";
    echo ");\n\n";
    
    echo "-- অপশন ২: ফরেন কী কনস্ট্রেইন্ট সরিয়ে ফেলা এবং পুনরায় যোগ করা\n";
    echo "ALTER TABLE duty_assignments DROP FOREIGN KEY duty_assignments_ibfk_1;\n";
    echo "ALTER TABLE duty_assignments ADD CONSTRAINT duty_assignments_ibfk_1 \n";
    echo "FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE;\n";
    echo "</pre>";
    
    // সমাধান বাস্তবায়ন
    if (isset($_GET['fix']) && $_GET['fix'] === 'delete') {
        try {
            // সমস্যাযুক্ত রেকর্ডগুলি মুছে ফেলা
            $deleteSql = "DELETE FROM duty_assignments 
                          WHERE teacher_id IN (
                              SELECT da.teacher_id 
                              FROM (SELECT * FROM duty_assignments) da 
                              LEFT JOIN teachers t ON da.teacher_id = t.id 
                              WHERE t.id IS NULL
                          )";
            $stmt = $pdo->prepare($deleteSql);
            $stmt->execute();
            $deletedCount = $stmt->rowCount();
            
            echo "<p style='color:green;'><strong>$deletedCount টি সমস্যাযুক্ত রেকর্ড সফলভাবে মুছে ফেলা হয়েছে।</strong></p>";
        } catch (PDOException $e) {
            echo "<p style='color:red;'><strong>এরর: " . $e->getMessage() . "</strong></p>";
        }
    }
    
    // সমাধান বাটন
    echo "<p><a href='?fix=delete' onclick=\"return confirm('আপনি কি নিশ্চিত যে আপনি সমস্যাযুক্ত রেকর্ডগুলি মুছতে চান?');\" style='background-color:#dc3545;color:white;padding:10px;text-decoration:none;border-radius:5px;'>সমস্যাযুক্ত রেকর্ডগুলি মুছুন</a></p>";
} else {
    echo "<p style='color:green;'><strong>কোন সমস্যাযুক্ত রেকর্ড পাওয়া যায়নি। ফরেন কী কনস্ট্রেইন্ট ঠিক আছে।</strong></p>";
}

// ফরেন কী কনস্ট্রেইন্ট চেক
echo "<h2>ফরেন কী কনস্ট্রেইন্ট চেক</h2>";
$fkQuery = "SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = 'exmm' 
            AND TABLE_NAME = 'duty_assignments'";
$fkResult = $pdo->query($fkQuery)->fetchAll();

if (count($fkResult) > 0) {
    echo "<p>ফরেন কী কনস্ট্রেইন্ট পাওয়া গেছে:</p>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>টেবিল</th><th>কলাম</th><th>কনস্ট্রেইন্ট নাম</th><th>রেফারেন্স টেবিল</th><th>রেফারেন্স কলাম</th></tr>";
    foreach ($fkResult as $fk) {
        echo "<tr>";
        echo "<td>{$fk['TABLE_NAME']}</td>";
        echo "<td>{$fk['COLUMN_NAME']}</td>";
        echo "<td>{$fk['CONSTRAINT_NAME']}</td>";
        echo "<td>{$fk['REFERENCED_TABLE_NAME']}</td>";
        echo "<td>{$fk['REFERENCED_COLUMN_NAME']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color:orange;'><strong>duty_assignments টেবিলে কোন ফরেন কী কনস্ট্রেইন্ট পাওয়া যায়নি।</strong></p>";
    
    // ফরেন কী কনস্ট্রেইন্ট যোগ করার অপশন
    echo "<h3>ফরেন কী কনস্ট্রেইন্ট যোগ করা:</h3>";
    echo "<pre>";
    echo "ALTER TABLE duty_assignments ADD CONSTRAINT duty_assignments_ibfk_1 \n";
    echo "FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE;\n";
    echo "</pre>";
    
    if (isset($_GET['fix']) && $_GET['fix'] === 'add_constraint') {
        try {
            // ফরেন কী কনস্ট্রেইন্ট যোগ করা
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            $addConstraintSql = "ALTER TABLE duty_assignments ADD CONSTRAINT duty_assignments_ibfk_1 FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE";
            $pdo->exec($addConstraintSql);
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "<p style='color:green;'><strong>ফরেন কী কনস্ট্রেইন্ট সফলভাবে যোগ করা হয়েছে।</strong></p>";
        } catch (PDOException $e) {
            echo "<p style='color:red;'><strong>এরর: " . $e->getMessage() . "</strong></p>";
            // ফরেন কী চেক পুনরায় চালু করা
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        }
    }
    
    // ফরেন কী কনস্ট্রেইন্ট যোগ করার বাটন
    echo "<p><a href='?fix=add_constraint' onclick=\"return confirm('আপনি কি নিশ্চিত যে আপনি ফরেন কী কনস্ট্রেইন্ট যোগ করতে চান?');\" style='background-color:#ffc107;color:black;padding:10px;text-decoration:none;border-radius:5px;'>ফরেন কী কনস্ট্রেইন্ট যোগ করুন</a></p>";
}

// টেবিল স্ট্যাটিসটিক্স
echo "<h2>টেবিল স্ট্যাটিসটিক্স</h2>";
echo "<p>duty_assignments টেবিলে রেকর্ড সংখ্যা: " . $pdo->query("SELECT COUNT(*) FROM duty_assignments")->fetchColumn() . "</p>";
echo "<p>teachers টেবিলে রেকর্ড সংখ্যা: " . $pdo->query("SELECT COUNT(*) FROM teachers")->fetchColumn() . "</p>";

// হোম পেইজে ফিরে যাওয়ার লিংক
echo "<p><a href='index.php' style='background-color:#6c757d;color:white;padding:10px;text-decoration:none;border-radius:5px;'>হোম পেইজে ফিরে যান</a></p>";
?>