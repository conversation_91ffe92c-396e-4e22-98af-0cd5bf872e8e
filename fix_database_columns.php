<?php
// Database Column Fix Script
// This script will check and add missing columns to the students table

$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔧 Database Column Fix Script</h2>";
    echo "<p>Checking and fixing missing columns in students table...</p>";
    
    // Check if students table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'students'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        echo "<div style='color: red;'>❌ Students table does not exist. Creating new table...</div>";
        
        // Create complete students table with all required columns
        $createTableSQL = "
            CREATE TABLE `students` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `father_name` varchar(255) DEFAULT NULL,
                `gender` enum('Male','Female','Other') DEFAULT 'Male',
                `roll` varchar(50) NOT NULL,
                `registration` varchar(100) DEFAULT NULL,
                `department` varchar(100) DEFAULT NULL,
                `academic_year` varchar(20) DEFAULT NULL,
                `student_type` enum('Regular','Irregular','Improvement') DEFAULT 'Regular',
                `subjects` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_roll` (`roll`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        echo "<div style='color: green;'>✅ Students table created successfully with all required columns.</div>";
        
    } else {
        echo "<div style='color: green;'>✅ Students table exists. Checking columns...</div>";
        
        // Get current table structure
        $stmt = $pdo->query("DESCRIBE students");
        $currentColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>📋 Current Columns:</h3>";
        echo "<ul>";
        foreach ($currentColumns as $column) {
            echo "<li>$column</li>";
        }
        echo "</ul>";
        
        // Define required columns with their definitions
        $requiredColumns = [
            'father_name' => "varchar(255) DEFAULT NULL",
            'gender' => "enum('Male','Female','Other') DEFAULT 'Male'"
        ];
        
        $columnsAdded = 0;
        
        foreach ($requiredColumns as $columnName => $columnDefinition) {
            if (!in_array($columnName, $currentColumns)) {
                echo "<div style='color: orange;'>⚠️ Missing column: $columnName</div>";
                
                // Add the missing column
                $alterSQL = "ALTER TABLE students ADD COLUMN `$columnName` $columnDefinition";
                
                try {
                    $pdo->exec($alterSQL);
                    echo "<div style='color: green;'>✅ Added column: $columnName</div>";
                    $columnsAdded++;
                } catch (PDOException $e) {
                    echo "<div style='color: red;'>❌ Failed to add column $columnName: " . $e->getMessage() . "</div>";
                }
            } else {
                echo "<div style='color: green;'>✅ Column exists: $columnName</div>";
            }
        }
        
        if ($columnsAdded > 0) {
            echo "<div style='color: green;'><strong>✅ Added $columnsAdded missing columns successfully!</strong></div>";
        } else {
            echo "<div style='color: green;'><strong>✅ All required columns already exist!</strong></div>";
        }
    }
    
    // Show final table structure
    echo "<h3>📋 Final Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE students");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test CSV upload functionality
    echo "<h3>🧪 Testing CSV Upload Functionality:</h3>";
    
    try {
        require_once 'models/Student.php';
        
        $student = new Student();
        $testData = [
            'name' => 'Test Student',
            'father_name' => 'Test Father',
            'gender' => 'Male',
            'roll' => 'TEST001',
            'registration' => 'REG001',
            'department' => 'Science',
            'academic_year' => '2024',
            'student_type' => 'Regular',
            'subjects' => '101,102,103'
        ];
        
        // Try to insert test data
        $sql = "INSERT INTO students (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects)
                VALUES (:name, :father_name, :gender, :roll, :registration, :department, :academic_year, :student_type, :subjects)";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($testData);
        
        if ($result) {
            echo "<div style='color: green;'>✅ Test insert successful! CSV upload should work now.</div>";
            
            // Clean up test data
            $pdo->exec("DELETE FROM students WHERE roll = 'TEST001'");
            echo "<div style='color: blue;'>🧹 Test data cleaned up.</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ Test insert failed: " . $e->getMessage() . "</div>";
    }
    
    echo "<hr>";
    echo "<h3>🚀 Next Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='csv_upload.php'>Test CSV Upload</a></li>";
    echo "<li><a href='system_startup_check.php'>Run System Check</a></li>";
    echo "<li><a href='index.php'>Go to Home Page</a></li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<div style='color: red;'>❌ Database Error: " . $e->getMessage() . "</div>";
    echo "<p><strong>Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL is running</li>";
    echo "<li>Check database connection settings</li>";
    echo "<li><a href='db_connection_test.php'>Test Database Connection</a></li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Column Fix - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 900px;
            margin: 0 auto;
        }
        
        table {
            font-size: 14px;
        }
        
        th {
            background: #f8f9fa !important;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Content is generated by PHP above -->
    </div>
</body>
</html>
