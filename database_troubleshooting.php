<?php
// Database Troubleshooting and Diagnostic Tool
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Troubleshooting - EXMM</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 1000px;
            margin: 0 auto;
        }
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            font-weight: 600;
        }
        .btn {
            font-family: 'Hind Siliguri', sans-serif;
            font-weight: 500;
            margin: 5px;
        }
        .alert {
            border-radius: 10px;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">🔧 Database Troubleshooting Tool</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔍 Database Diagnostics</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        echo "<h6>Connection Test:</h6>";
                        try {
                            $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo "<span class='status-good'>✅ MySQL Connection: OK</span><br>";
                            
                            // Check if database exists
                            $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
                            $dbExists = $stmt->fetch();
                            
                            if ($dbExists) {
                                echo "<span class='status-good'>✅ Database '$dbname': EXISTS</span><br>";
                                
                                // Connect to specific database
                                $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                
                                // Check tables
                                $stmt = $pdo->query('SHOW TABLES');
                                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                
                                if (empty($tables)) {
                                    echo "<span class='status-warning'>⚠️ Tables: NONE FOUND</span><br>";
                                } else {
                                    echo "<span class='status-good'>✅ Tables: " . count($tables) . " found</span><br>";
                                    echo "<small>Tables: " . implode(', ', $tables) . "</small><br>";
                                }
                                
                                // Check foreign keys
                                $stmt = $pdo->query("SELECT COUNT(*) as fk_count FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_SCHEMA = '$dbname' AND REFERENCED_TABLE_NAME IS NOT NULL");
                                $fkCount = $stmt->fetch()['fk_count'];
                                
                                if ($fkCount > 0) {
                                    echo "<span class='status-warning'>⚠️ Foreign Keys: $fkCount found</span><br>";
                                } else {
                                    echo "<span class='status-good'>✅ Foreign Keys: None</span><br>";
                                }
                                
                            } else {
                                echo "<span class='status-warning'>⚠️ Database '$dbname': NOT EXISTS</span><br>";
                            }
                            
                        } catch(PDOException $e) {
                            echo "<span class='status-error'>❌ Connection Error: " . $e->getMessage() . "</span><br>";
                        }
                        ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🛠️ Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="reset_database.php" class="btn btn-danger">
                                🗑️ Reset Database (Delete All)
                            </a>
                            <a href="setup_database.php" class="btn btn-primary">
                                🔧 Setup Database (Create Tables)
                            </a>
                            <a href="check_db_structure.php" class="btn btn-info">
                                📋 Check Database Structure
                            </a>
                            <a href="teacher_duty_management.php" class="btn btn-success">
                                👥 Go to Teacher Management
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">📚 Common Issues & Solutions</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="troubleshootingAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                Foreign Key Constraint Error (1451)
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <strong>Problem:</strong> Cannot delete or update due to foreign key constraints.<br>
                                <strong>Solution:</strong>
                                <ol>
                                    <li>Click "Reset Database" to safely remove all tables</li>
                                    <li>Then click "Setup Database" to recreate tables</li>
                                    <li>The reset script automatically handles foreign key constraints</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                Database Connection Failed
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <strong>Problem:</strong> Cannot connect to MySQL database.<br>
                                <strong>Solutions:</strong>
                                <ol>
                                    <li>Make sure XAMPP is running</li>
                                    <li>Start MySQL service in XAMPP Control Panel</li>
                                    <li>Check if port 3306 is available</li>
                                    <li>Verify database credentials in config files</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                Tables Not Found
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                            <div class="accordion-body">
                                <strong>Problem:</strong> Database exists but tables are missing.<br>
                                <strong>Solution:</strong>
                                <ol>
                                    <li>Click "Setup Database" to create required tables</li>
                                    <li>If that fails, first click "Reset Database" then "Setup Database"</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <p class="text-muted">
                <small>Database Troubleshooting Tool - EXMM Student Management System</small>
            </p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
