<?php
// Simple test file to check if basic functionality works
echo "<!DOCTYPE html>";
echo "<html><head><title>Test Seat Cards</title></head><body>";
echo "<h1>Test Page</h1>";

// Test database connection
try {
    require_once __DIR__ . '/models/Student.php';
    $student = new Student();
    $students = $student->getAll();
    echo "<p>Total students in database: " . count($students) . "</p>";
    
    // Test subject filter
    if (isset($_GET['subject']) && !empty($_GET['subject'])) {
        $subjectCode = $_GET['subject'];
        $filteredStudents = $student->getStudentsWithSubjectCode($subjectCode);
        echo "<p>Students with subject code " . $subjectCode . ": " . count($filteredStudents) . "</p>";
        
        // Show first 5 students
        echo "<h3>First 5 students:</h3>";
        for ($i = 0; $i < min(5, count($filteredStudents)); $i++) {
            $s = $filteredStudents[$i];
            echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
            echo "<strong>Name:</strong> " . htmlspecialchars($s['student_name']) . "<br>";
            echo "<strong>Roll:</strong> " . htmlspecialchars($s['roll_no']) . "<br>";
            echo "<strong>Reg:</strong> " . htmlspecialchars($s['reg_no']) . "<br>";
            echo "<strong>Group:</strong> " . htmlspecialchars($s['group_name']) . "<br>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
