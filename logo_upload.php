<?php
session_start();

// Handle logo upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['logo'])) {
    $uploadDir = 'uploads/';
    
    // Create uploads directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    $file = $_FILES['logo'];
    $fileName = 'college_logo.' . pathinfo($file['name'], PATHINFO_EXTENSION);
    $uploadPath = $uploadDir . $fileName;
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        $error = 'শুধুমাত্র JPG, PNG, GIF ফাইল আপলোড করা যাবে।';
    } elseif ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        $error = 'ফাইলের সাইজ ৫ MB এর কম হতে হবে।';
    } else {
        // Delete existing logo if exists
        $existingFiles = glob($uploadDir . 'college_logo.*');
        foreach ($existingFiles as $existingFile) {
            unlink($existingFile);
        }
        
        // Upload new logo
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            $success = 'লোগো সফলভাবে আপলোড হয়েছে!';
        } else {
            $error = 'লোগো আপলোড করতে সমস্যা হয়েছে।';
        }
    }
}

// Get current logo
$currentLogo = null;
$logoFiles = glob('uploads/college_logo.*');
if (!empty($logoFiles)) {
    $currentLogo = $logoFiles[0];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কলেজ লোগো আপলোড</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            padding-top: 50px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .current-logo {
            max-width: 200px;
            max-height: 200px;
            border: 3px solid #dee2e6;
            border-radius: 10px;
            padding: 10px;
            background: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
        }
        
        .btn-danger {
            border-radius: 25px;
            padding: 10px 30px;
        }
        
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-image me-2"></i>
                            কলেজ লোগো আপলোড
                        </h4>
                        <small>ডিউটি লেটারের হেডিং এর বাম পাশে দেখানোর জন্য</small>
                    </div>
                    <div class="card-body">
                        <?php if (isset($success)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Current Logo Display -->
                        <?php if ($currentLogo): ?>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h5><i class="fas fa-eye me-2"></i>বর্তমান লোগো</h5>
                                    <div class="text-center">
                                        <img src="<?php echo $currentLogo; ?>" alt="Current Logo" class="current-logo">
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                ফাইল: <?php echo basename($currentLogo); ?><br>
                                                সাইজ: <?php echo round(filesize($currentLogo) / 1024, 2); ?> KB
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h5><i class="fas fa-file-alt me-2"></i>প্রিভিউ (ডিউটি লেটারে)</h5>
                                    <div class="border rounded p-3" style="background: #f8f9fa;">
                                        <div style="position: relative; text-align: center; padding: 20px;">
                                            <img src="<?php echo $currentLogo; ?>" alt="Logo Preview" 
                                                 style="position: absolute; left: 10px; top: 10px; width: 60px; height: 60px; object-fit: contain;">
                                            <div style="margin-left: 70px;">
                                                <div style="font-weight: bold; font-size: 16px;">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
                                                <div style="font-size: 12px;">দামুড়হুদা, চুয়াডাঙ্গা</div>
                                                <div style="font-size: 11px;">উচ্চ মাধ্যমিক পরীক্ষা-২০২৫</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Upload Form -->
                        <form method="POST" enctype="multipart/form-data" id="logoForm">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>লোগো ফাইল আপলোড করুন</h5>
                                <p class="text-muted">
                                    ফাইল টেনে এনে ছাড়ুন অথবা ক্লিক করে নির্বাচন করুন<br>
                                    <small>সাপোর্টেড ফরম্যাট: JPG, PNG, GIF | সর্বোচ্চ সাইজ: ৫ MB</small>
                                </p>
                                <input type="file" name="logo" id="logoFile" accept="image/*" class="d-none" required>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('logoFile').click()">
                                    <i class="fas fa-folder-open me-2"></i>ফাইল নির্বাচন করুন
                                </button>
                            </div>
                            
                            <div id="fileInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span id="fileName"></span>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-upload me-2"></i>আপলোড করুন
                                </button>
                                
                                <?php if ($currentLogo): ?>
                                    <a href="?delete=1" class="btn btn-danger btn-lg" 
                                       onclick="return confirm('আপনি কি নিশ্চিত যে লোগো মুছে ফেলতে চান?')">
                                        <i class="fas fa-trash me-2"></i>লোগো মুছুন
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                        
                        <div class="mt-4 pt-4 border-top">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="individual_duty_letter_generator.php" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-file-alt me-2"></i>ডিউটি লেটার জেনারেটর
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="index.php" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-home me-2"></i>হোম পেজ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File input change handler
        document.getElementById('logoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = `নির্বাচিত ফাইল: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`;
                document.getElementById('fileInfo').style.display = 'block';
            }
        });
        
        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('logoFile');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                const event = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(event);
            }
        });
        
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });
    </script>
</body>
</html>

<?php
// Handle logo deletion
if (isset($_GET['delete']) && $_GET['delete'] == '1') {
    $logoFiles = glob('uploads/college_logo.*');
    foreach ($logoFiles as $logoFile) {
        unlink($logoFile);
    }
    header('Location: logo_upload.php?deleted=1');
    exit;
}

if (isset($_GET['deleted'])) {
    echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            const alert = document.createElement("div");
            alert.className = "alert alert-success";
            alert.innerHTML = "<i class=\"fas fa-check-circle me-2\"></i>লোগো সফলভাবে মুছে ফেলা হয়েছে!";
            document.querySelector(".card-body").insertBefore(alert, document.querySelector(".card-body").firstChild);
        });
    </script>';
}
?>
