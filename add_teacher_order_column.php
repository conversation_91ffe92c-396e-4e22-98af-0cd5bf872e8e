<?php
require_once 'includes/teacher_db.php';

try {
    // Check if teacher_order column already exists
    $checkColumnSQL = "SHOW COLUMNS FROM duty_assignments LIKE 'teacher_order'";
    $stmt = $pdo->query($checkColumnSQL);
    
    if ($stmt->rowCount() == 0) {
        // Add teacher_order column
        $addColumnSQL = "ALTER TABLE duty_assignments ADD COLUMN teacher_order INT DEFAULT 0 AFTER duty_shift";
        $pdo->exec($addColumnSQL);
        
        // Add index for better performance
        $addIndexSQL = "ALTER TABLE duty_assignments ADD INDEX idx_teacher_order (teacher_order)";
        $pdo->exec($addIndexSQL);
        
        echo "teacher_order column added successfully!<br>";
        
        // Initialize existing records with default order based on teacher sl_number
        $initializeOrderSQL = "
            UPDATE duty_assignments da
            JOIN teachers t ON da.teacher_id = t.id
            SET da.teacher_order = CAST(t.sl_number AS UNSIGNED)
            WHERE da.teacher_order = 0
        ";
        $pdo->exec($initializeOrderSQL);
        
        echo "Existing records initialized with default order!<br>";
        echo "Migration completed successfully!";
    } else {
        echo "teacher_order column already exists!";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
