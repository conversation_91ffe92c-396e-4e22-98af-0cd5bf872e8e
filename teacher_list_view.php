<?php
session_start();
require_once 'includes/teacher_db.php';

// Get teachers from database
// Get all teachers sorted by serial number in ascending order
$uploadedTeachers = $teacherManager->getAllTeachers('sl_number ASC');

// Handle search and filter
$searchTerm = $_GET['search'] ?? '';
$statusFilter = $_GET['status'] ?? '';
$subjectFilter = $_GET['subject'] ?? '';

// Use database search function
$filteredTeachers = $teacherManager->searchTeachers($searchTerm, $statusFilter, $subjectFilter);

// Get unique subjects for filter dropdown
$subjects = $teacherManager->getUniqueSubjects();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক তালিকা - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .list-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .teacher-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .teacher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .teacher-photo {
            width: 80px;
            height: 80px;
            flex-shrink: 0;
        }
        .teacher-img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 50%;
            border: 3px solid #ddd;
        }
        .teacher-placeholder {
            width: 80px;
            height: 80px;
            background: #f8f9fa;
            border: 3px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 30px;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-normal { background: #e3f2fd; color: #1976d2; }
        .status-always { background: #e8f5e8; color: #2e7d32; }
        .status-never { background: #ffebee; color: #c62828; }
        .filter-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .view-mode-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .view-mode-buttons .btn {
            border-radius: 20px;
        }
        .table-view {
            display: none;
        }
        .table-view.active {
            display: block;
        }
        .card-view.active {
            display: block;
        }
        .card-view {
            display: none;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="list-card">
                        <div class="text-center">
                            <h1><i class="fas fa-users text-primary"></i> শিক্ষক তালিকা</h1>
                            <p class="text-muted mb-0">সব শিক্ষকদের তথ্য দেখুন, খুঁজুন এবং ব্যবস্থাপনা করুন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <?php $stats = $teacherManager->getTeacherStats(); ?>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['total']; ?></h3>
                        <p class="mb-0">মোট শিক্ষক</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['normal']; ?></h3>
                        <p class="mb-0">সাধারন ডিউটি</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['always']; ?></h3>
                        <p class="mb-0">সম সময় অন্তর্ভুক্ত</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <h3><?php echo $stats['never']; ?></h3>
                        <p class="mb-0">কখনো অন্তর্ভুক্ত নয়</p>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="list-card">
                <div class="filter-section">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label class="form-label">খুঁজুন</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>" placeholder="নাম, মোবাইল, বিষয় বা পদবী লিখুন">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">ডিউটি স্ট্যাটাস</label>
                            <select class="form-select" name="status">
                                <option value="">সব স্ট্যাটাস</option>
                                <option value="সাধারন" <?php echo ($statusFilter === 'সাধারন') ? 'selected' : ''; ?>>সাধারন</option>
                                <option value="সম সময় অন্তর্ভুক্ত" <?php echo ($statusFilter === 'সম সময় অন্তর্ভুক্ত') ? 'selected' : ''; ?>>সম সময় অন্তর্ভুক্ত</option>
                                <option value="কখনো অন্তর্ভুক্ত নয়" <?php echo ($statusFilter === 'কখনো অন্তর্ভুক্ত নয়') ? 'selected' : ''; ?>>কখনো অন্তর্ভুক্ত নয়</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">বিষয়</label>
                            <select class="form-select" name="subject">
                                <option value="">সব বিষয়</option>
                                <?php foreach ($subjects as $subject): ?>
                                    <option value="<?php echo htmlspecialchars($subject); ?>" <?php echo ($subjectFilter === $subject) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($subject); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i>খুঁজুন
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- View Mode Buttons -->
                <div class="view-mode-buttons">
                    <button type="button" class="btn btn-outline-primary active" onclick="switchView('card')">
                        <i class="fas fa-th-large me-2"></i>কার্ড ভিউ
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="switchView('table')">
                        <i class="fas fa-table me-2"></i>টেবিল ভিউ
                    </button>
                    <a href="teacher_duty_management.php" class="btn btn-success ms-auto">
                        <i class="fas fa-plus me-2"></i>নতুন শিক্ষক যুক্ত করুন
                    </a>
                </div>

                <!-- Card View -->
                <div class="card-view active" id="cardView">
                    <?php if (empty($filteredTeachers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোনো শিক্ষক পাওয়া যায়নি</h5>
                            <p class="text-muted">নতুন শিক্ষক যুক্ত করুন অথবা ফিল্টার পরিবর্তন করুন</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($filteredTeachers as $index => $teacher): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="teacher-card">
                                        <div class="d-flex align-items-start mb-3">
                                            <!-- Teacher Photo -->
                                            <div class="teacher-photo me-3">
                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" class="teacher-img">
                                                <?php else: ?>
                                                    <div class="teacher-placeholder">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Teacher Info -->
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                    <span class="status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal'); ?>">
                                                        <?php echo $teacher['duty_status']; ?>
                                                    </span>
                                                </div>
                                                <p class="text-muted small mb-2">
                                                    <i class="fas fa-book me-1"></i> <?php echo htmlspecialchars($teacher['subject']); ?><br>
                                                    <i class="fas fa-id-badge me-1"></i> <?php echo htmlspecialchars($teacher['designation']); ?><br>
                                                    <i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($teacher['mobile']); ?><br>
                                                    <i class="fas fa-university me-1"></i> <?php echo htmlspecialchars($teacher['college']); ?>
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="d-flex gap-2">
                                            <a href="teacher_duty_management.php#teacher-<?php echo $index; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit me-1"></i>সম্পাদনা
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-info" onclick="viewTeacherDetails(<?php echo $index; ?>)">
                                                <i class="fas fa-eye me-1"></i>বিস্তারিত
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Table View -->
                <div class="table-view" id="tableView">
                    <?php if (empty($filteredTeachers)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোনো শিক্ষক পাওয়া যায়নি</h5>
                            <p class="text-muted">নতুন শিক্ষক যুক্ত করুন অথবা ফিল্টার পরিবর্তন করুন</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ছবি</th>
                                        <th>নাম</th>
                                        <th>বিষয়</th>
                                        <th>পদবী</th>
                                        <th>মোবাইল</th>
                                        <th>ডিউটি স্ট্যাটাস</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($filteredTeachers as $index => $teacher): ?>
                                        <tr>
                                            <td>
                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                                <?php else: ?>
                                                    <div style="width: 50px; height: 50px; background: #f8f9fa; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                        <i class="fas fa-user text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($teacher['name']); ?></strong><br>
                                                <small class="text-muted">SL: <?php echo htmlspecialchars($teacher['sl_number'] ?? 'N/A'); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['mobile']); ?></td>
                                            <td>
                                                <span class="status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal'); ?>">
                                                    <?php echo $teacher['duty_status']; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="teacher_duty_management.php#teacher-<?php echo $index; ?>" class="btn btn-sm btn-outline-primary" title="সম্পাদনা">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="viewTeacherDetails(<?php echo $index; ?>)" title="বিস্তারিত">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Details Modal -->
    <div class="modal fade" id="teacherDetailsModal" tabindex="-1" aria-labelledby="teacherDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="teacherDetailsModalLabel">
                        <i class="fas fa-user me-2"></i>শিক্ষকের বিস্তারিত তথ্য
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="teacherDetailsContent">
                    <!-- Teacher details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>বন্ধ করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Switch between card and table view
        function switchView(viewType) {
            const cardView = document.getElementById('cardView');
            const tableView = document.getElementById('tableView');
            const buttons = document.querySelectorAll('.view-mode-buttons .btn');

            // Remove active class from all buttons
            buttons.forEach(btn => btn.classList.remove('active'));

            if (viewType === 'card') {
                cardView.classList.add('active');
                tableView.classList.remove('active');
                buttons[0].classList.add('active');
            } else {
                tableView.classList.add('active');
                cardView.classList.remove('active');
                buttons[1].classList.add('active');
            }
        }

        // View teacher details
        function viewTeacherDetails(index) {
            const teachers = <?php echo json_encode(array_values($filteredTeachers)); ?>;
            const teacher = teachers[index];

            if (teacher) {
                const content = `
                    <div class="row">
                        <div class="col-md-4 text-center">
                            ${teacher.photo && teacher.photo !== '' ?
                                `<img src="${teacher.photo}" alt="Teacher Photo" style="width: 200px; height: 200px; object-fit: cover; border-radius: 10px; border: 3px solid #ddd;">` :
                                `<div style="width: 200px; height: 200px; background: #f8f9fa; border: 3px solid #ddd; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                </div>`
                            }
                            <h5 class="mt-3">${teacher.name}</h5>
                            <span class="status-badge status-${teacher.duty_status === 'সম সময় অন্তর্ভুক্ত' ? 'always' : (teacher.duty_status === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal')}">${teacher.duty_status}</span>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tr>
                                    <th width="30%">ক্রমিক নং:</th>
                                    <td>${teacher.sl}</td>
                                </tr>
                                <tr>
                                    <th>নাম:</th>
                                    <td>${teacher.name}</td>
                                </tr>
                                <tr>
                                    <th>মোবাইল:</th>
                                    <td>${teacher.mobile}</td>
                                </tr>
                                <tr>
                                    <th>বিষয়:</th>
                                    <td>${teacher.subject}</td>
                                </tr>
                                <tr>
                                    <th>পদবী:</th>
                                    <td>${teacher.designation}</td>
                                </tr>
                                <tr>
                                    <th>কলেজ:</th>
                                    <td>${teacher.college}</td>
                                </tr>
                                <tr>
                                    <th>ডিউটি স্ট্যাটাস:</th>
                                    <td><span class="status-badge status-${teacher.duty_status === 'সম সময় অন্তর্ভুক্ত' ? 'always' : (teacher.duty_status === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal')}">${teacher.duty_status}</span></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                `;

                document.getElementById('teacherDetailsContent').innerHTML = content;
                const modal = new bootstrap.Modal(document.getElementById('teacherDetailsModal'));
                modal.show();
            }
        }
    </script>
</body>
</html>
