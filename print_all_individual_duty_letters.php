<?php
require_once 'includes/teacher_db.php';

// Function to get college logo
function getCollegeLogo() {
    $logoExtensions = ['png', 'jpg', 'jpeg', 'gif'];
    foreach ($logoExtensions as $ext) {
        $logoPath = "uploads/college_logo.$ext";
        if (file_exists($logoPath)) {
            return $logoPath;
        }
    }
    return null;
}

// Get parameters
$date = $_GET['date'] ?? '';
$dates = $_GET['dates'] ?? ''; // Support for multiple dates
$allDuties = $_GET['all_duties'] ?? ''; // Support for all duties

// Handle different modes
$teacherAssignments = [];

if ($allDuties) {
    // Get all teachers who have any duty assignments
    $allDutyDates = $teacherManager->getAllDutyDates();
    $allTeachers = [];

    foreach ($allDutyDates as $dutyDate) {
        $assignments = $teacherManager->getDutyAssignments($dutyDate);
        foreach ($assignments as $assignment) {
            $teacherId = $assignment['teacher_id'];
            if (!isset($allTeachers[$teacherId])) {
                $allTeachers[$teacherId] = $assignment;
            }
        }
    }
    
    // Sort teachers by sl_number
    uasort($allTeachers, function($a, $b) {
        // Get sl_number from each teacher
        $aNum = isset($a['sl_number']) ? intval($a['sl_number']) : 0;
        $bNum = isset($b['sl_number']) ? intval($b['sl_number']) : 0;
        return $aNum - $bNum;
    });

    // Get all duty dates for each teacher
    foreach ($allTeachers as $teacherId => $teacher) {
        $teacherDutyDetails = $teacherManager->getAllDutyDatesForTeacher($teacherId);
        if (!empty($teacherDutyDetails)) {
            $teacherAssignments[$teacherId] = [
                'teacher' => $teacher,
                'duties' => $teacherDutyDetails // Use 'duties' key
            ];
        }
    }

} else {
    // Handle multiple dates
    $dutyDates = [];
    if (!empty($dates)) {
        $dutyDates = explode(',', $dates);
    } elseif (!empty($date)) {
        $dutyDates = [$date];
    } else {
        die('তারিখ প্রয়োজন!');
    }

    // Get all duty assignments for these dates
    foreach ($dutyDates as $dutyDate) {
        $assignments = $teacherManager->getDutyAssignments(trim($dutyDate));
        foreach ($assignments as $assignment) {
            $teacherId = $assignment['teacher_id'];
            if (!isset($teacherAssignments[$teacherId])) {
                $teacherAssignments[$teacherId] = [
                    'teacher' => $assignment,
                    'duties' => [] // Use 'duties' key for consistency
                ];
            }
            // To be compatible with the new structure, we create a similar duty item
            $teacherAssignments[$teacherId]['duties'][] = [
                'duty_date' => trim($dutyDate),
                'room_number' => $assignment['room_number'] ?? null,
                'duty_shift' => $assignment['duty_shift'] ?? 'Morning' // Assume morning if not set
            ];
        }
    }
    
    // Sort teacher assignments by sl_number
    uasort($teacherAssignments, function($a, $b) {
        // Get sl_number from each teacher
        $aTeacher = $a['teacher'];
        $bTeacher = $b['teacher'];
        $aNum = isset($aTeacher['sl_number']) ? intval($aTeacher['sl_number']) : 0;
        $bNum = isset($bTeacher['sl_number']) ? intval($bTeacher['sl_number']) : 0;
        return $aNum - $bNum;
    });
}

if (empty($teacherAssignments)) {
    die('কোন ডিউটি বন্টন পাওয়া যায়নি!');
}

// Get signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Generate memo number
$memoNumber = sprintf("০১/%s-%02d", date('Y'), date('m', strtotime($date)));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সকল ডিউটি পত্র - <?php echo formatDateBengali($date); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 12mm;
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            line-height: 1.3;
            color: #000;
            background: white;
            margin: 0;
            padding: 0;
            font-size: 13px;
        }

        .letter-container {
            background: white;
            padding: 12px;
            border: 2px solid #000;
            margin-bottom: 0;
            page-break-after: always;
            min-height: 95vh;
            max-height: 95vh;
            overflow: hidden;
        }
        
        .letter-container:last-child {
            page-break-after: auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 12px;
            border-bottom: 2px solid #000;
            padding-bottom: 8px;
            position: relative;
        }

        .header-logo {
            position: absolute;
            left: 15px;
            top: 8px;
            width: 60px;
            height: 60px;
            object-fit: contain;
        }

        .logo {
            width: 50px;
            height: 50px;
            margin: 0 auto 6px;
        }

        .college-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
            color: #000;
        }

        .college-address {
            font-size: 12px;
            margin-bottom: 2px;
            color: #333;
        }

        .college-info {
            font-size: 11px;
            color: #666;
        }

        .memo-section {
            display: flex;
            justify-content: space-between;
            margin: 12px 0;
            padding: 6px 0;
            border-bottom: 1px solid #ddd;
        }

        .memo-number {
            font-weight: bold;
            font-size: 12px;
        }

        .subject-line {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            margin: 12px 0;
            padding: 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .letter-body {
            margin: 12px 0;
            text-align: justify;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .teacher-info-box {
            background: #f8f9fa;
            border: 2px solid #000;
            border-radius: 3px;
            padding: 6px;
            margin: 8px 0;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 11px;
        }

        .info-table td {
            padding: 4px 6px;
            border: 1px solid #000;
            text-align: center;
        }

        .info-table .label {
            background: #e9ecef;
            font-weight: bold;
            width: 16.66%;
        }

        .duty-schedule-box {
            border: 2px solid #000;
            border-radius: 3px;
            padding: 8px;
            margin: 8px 0;
        }

        .duty-schedule-title {
            font-weight: bold;
            font-size: 13px;
            margin-bottom: 6px;
            text-align: center;
        }
        
        .duty-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .duty-table th, .duty-table td {
            border: 1px solid #000;
            padding: 5px;
            text-align: left;
            vertical-align: top;
        }
        
        .duty-table th {
            background-color: #f2f2f2;
            text-align: center;
        }

        .signatures {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
            page-break-inside: avoid;
        }

        .signature-box {
            text-align: center;
            width: 180px;
            position: relative;
        }

        .signature-image {
            max-width: 100px;
            max-height: 40px;
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            background: white;
            padding: 0 8px;
        }

        .signature-line {
            border-top: 2px solid #000;
            margin-top: 40px;
            padding-top: 6px;
            font-weight: bold;
            font-size: 12px;
            position: relative;
            z-index: 1;
        }

        .signature-name {
            font-size: 11px;
            font-weight: bold;
            margin-top: 3px;
            color: #333;
        }

        .signature-details {
            font-size: 10px;
            margin-top: 2px;
            line-height: 1.2;
        }

        @media print {
            body {
                margin: 0;
                padding: 0;
                font-size: 12px;
            }

            .no-print {
                display: none !important;
            }

            .letter-container {
                border: 2px solid #000;
                box-shadow: none;
                padding: 8px;
                margin-bottom: 0;
                min-height: auto;
                max-height: none;
                page-break-inside: avoid;
            }

            .signatures {
                page-break-inside: avoid;
                margin-top: 15px;
            }

            .header {
                margin-bottom: 8px;
                padding-bottom: 6px;
            }

            .memo-section {
                margin: 8px 0;
                padding: 4px 0;
            }

            .subject-line {
                margin: 8px 0;
                padding: 6px;
            }

            .teacher-info-box,
            .duty-schedule-box {
                margin: 6px 0;
                padding: 4px;
            }

            .letter-body {
                margin: 8px 0;
            }
        }
        
        .print-info {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .print-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            width: 100%;
        }
        
        .print-button:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="print-info no-print">
        <strong>📄 মোট লেটার: <?php echo count($teacherAssignments); ?> টি</strong><br>
        <small>
            <?php if ($allDuties): ?>
                সব শিক্ষকের সব ডিউটি
            <?php else: ?>
                তারিখ: <?php
                $formattedDates = [];
                foreach ($dutyDates as $dutyDate) {
                    $formattedDates[] = formatDateBengali(trim($dutyDate));
                }
                echo implode(', ', $formattedDates);
                ?>
            <?php endif; ?>
        </small>
        <button class="print-button" onclick="window.print()">
            🖨️ সব প্রিন্ট করুন
        </button>
    </div>

    <?php
    $letterIndex = 0;
    foreach ($teacherAssignments as $teacherId => $data):
        $teacher = $teacherManager->getTeacherById($teacherId);
        if (!$teacher) continue;

        // New logic to separate duties by shift
        $morningDuties = [];
        $afternoonDuties = [];

        if (isset($data['duties']) && is_array($data['duties'])) {
            foreach ($data['duties'] as $duty) {
                if (isset($duty['duty_shift']) && $duty['duty_shift'] === 'Afternoon') {
                    $afternoonDuties[] = $duty;
                } else {
                    $morningDuties[] = $duty; // Default to morning if not set
                }
            }
        }
        
        $letterIndex++;
    ?>
        
        <div class="letter-container">
            <!-- Header -->
            <div class="header">
                <?php
                // Check for uploaded logo
                $logoPath = getCollegeLogo();
                if ($logoPath): ?>
                    <img src="<?php echo $logoPath; ?>" alt="College Logo" class="header-logo">
                <?php endif; ?>

                <div class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
                <div class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</div>
                <div class="college-info">
                    উচ্চ মাধ্যমিক পরীক্ষা-২০২৫ | কেন্দ্র নম্বর: ২৯৫
                </div>
            </div>

            <!-- Memo Section -->
            <div class="memo-section">
                <div>
                    <div class="memo-number">স্মারক নং: <?php echo $memoNumber; ?>-<?php echo sprintf('%02d', $letterIndex); ?></div>
                </div>
                <div>
                    <div><strong>তারিখ:</strong> <?php echo formatDateBengali(date('Y-m-d')); ?></div>
                </div>
            </div>

            <!-- Subject -->
            <div class="subject-line">
                <strong>বিষয়:</strong> HSC পরীক্ষা-২০২৫ এর ডিউটি সংক্রান্ত।
            </div>

            <!-- Teacher Information Table (Top) -->
            <div class="teacher-info-box">
                <table class="info-table">
                    <tr>
                        <td class="label">নাম</td>
                        <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                        <td class="label">পদবী</td>
                        <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                        <td class="label">কলেজ</td>
                        <td><?php echo htmlspecialchars($teacher['college']); ?></td>
                    </tr>
                </table>
            </div>

            <!-- Duty Schedule -->
            <div class="duty-schedule-box">
                <div class="duty-schedule-title">আপনার ডিউটির সময়সূচী</div>
                <table class="duty-table">
                    <thead>
                        <tr>
                            <th style="width:50%;">সকাল (Morning)</th>
                            <th style="width:50%;">বিকাল (Afternoon)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $maxRows = max(count($morningDuties), count($afternoonDuties));
                        if ($maxRows > 0) {
                            for ($i = 0; $i < $maxRows; $i++) {
                                echo '<tr>';
                                // Morning duty
                                echo '<td>';
                                if (isset($morningDuties[$i])) {
                                    $duty = $morningDuties[$i];
                                    $roomInfo = !empty($duty['room_number']) ? ' (কক্ষ: ' . htmlspecialchars($duty['room_number']) . ')' : '';
                                    echo formatDateBengali($duty['duty_date']) . $roomInfo;
                                }
                                echo '</td>';

                                // Afternoon duty
                                echo '<td>';
                                if (isset($afternoonDuties[$i])) {
                                    $duty = $afternoonDuties[$i];
                                    $roomInfo = !empty($duty['room_number']) ? ' (কক্ষ: ' . htmlspecialchars($duty['room_number']) . ')' : '';
                                    echo formatDateBengali($duty['duty_date']) . $roomInfo;
                                }
                                echo '</td>';
                                echo '</tr>';
                            }
                        } else {
                            // If there are no duties at all
                            echo '<tr><td colspan="2" style="text-align:center;">কোন ডিউটি পাওয়া যায়নি।</td></tr>';
                        }
                        ?>
                    </tbody>
                </table>
            </div>

            <!-- Body of the letter -->
            <div class="letter-body">
                উপর্যুক্ত বিষয়ের প্রেক্ষিতে জানানো যাচ্ছে যে, আসন্ন উচ্চ মাধ্যমিক পরীক্ষা-২০২৫ সুষ্ঠুভাবে পরিচালনার জন্য আপনাকে কক্ষ পরিদর্শক হিসেবে দায়িত্ব প্রদান করা হলো। আপনার নির্ধারিত দায়িত্ব যথাযথভাবে পালনের জন্য অনুরোধ করা হলো।
            </div>

            <!-- Signatures -->
            <div class="signatures">
                <div class="signature-box">
                    <?php if ($principalSignature && file_exists($principalSignature['signature_path'])): ?>
                        <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>"
                             alt="Principal Signature" class="signature-image">
                    <?php endif; ?>
                    <div class="signature-line">অধ্যক্ষ</div>
                    <div class="signature-name">মোঃ কামাল উদ্দীন</div>
                    <div class="signature-details">
                        আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                        দামুড়হুদা, চুয়াডাঙ্গা
                    </div>
                </div>

                <div class="signature-box">
                    <?php if ($convenerSignature && file_exists($convenerSignature['signature_path'])): ?>
                        <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>"
                             alt="Convener Signature" class="signature-image">
                    <?php endif; ?>
                    <div class="signature-line">আহবায়ক</div>
                    <div class="signature-name">মোঃ আবু জাফর মোঃ হাসিবুল আলম</div>
                    <div class="signature-details">
                        HSC পরীক্ষা-২০২৫<br>
                        আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                        দামুড়হুদা, চুয়াডাঙ্গা
                    </div>

                </div>
            </div>


        </div>
    <?php endforeach; ?>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() {
        //     setTimeout(() => {
        //         window.print();
        //     }, 2000);
        // }
    </script>
</body>
</html>
