<?php
require_once __DIR__ . '/models/Student.php';

// Get parameters
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$searchName = $_GET['search'] ?? '';
$subjectCode = $_GET['subject'] ?? '';
$groupName = $_GET['group'] ?? '';

// Validate cards per page
if ($cardsPerPage < 1 || $cardsPerPage > 30) {
    $cardsPerPage = 12;
}

// Get students
$student = new Student();
$students = [];
$error = '';

try {
    // Start with all students
    $students = $student->getAll();

    // Apply subject filter
    if (!empty($subjectCode)) {
        $students = $student->getStudentsWithSubjectCode($subjectCode);
    }

    // Apply additional filters
    if (!empty($searchName)) {
        $students = array_filter($students, function($s) use ($searchName) {
            return stripos($s['student_name'], $searchName) !== false;
        });
    }

    if (!empty($groupName)) {
        $students = array_filter($students, function($s) use ($groupName) {
            return stripos($s['group_name'], $groupName) !== false;
        });
    }

    // Convert filtered array back to indexed array
    $students = array_values($students);

} catch (Exception $e) {
    $error = $e->getMessage();
    $students = [];
}

$totalStudents = count($students);
$totalPages = ceil($totalStudents / $cardsPerPage);
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Seat Cards - HSC Exam 2025</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 8mm;
            background: white;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 6mm;
            height: 281mm;
        }
        
        .seat-card {
            border: 1.5px solid #4a90e2;
            border-radius: 12px;
            padding: 10px;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
            height: 67mm;
            box-shadow: 0 2px 4px rgba(74, 144, 226, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
            color: white;
            padding: 6px;
            margin: -10px -10px 8px -10px;
            border-radius: 10px 10px 0 0;
        }
        
        .exam-title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .college-info {
            font-size: 9px;
            opacity: 0.9;
        }
        
        .student-name {
            font-size: 15px;
            font-weight: 600;
            color: #2c3e50;
            margin: 6px 0;
            line-height: 1.2;
            min-height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .roll-number {
            background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
            border: 2px solid #4a90e2;
            border-radius: 8px;
            padding: 10px;
            margin: 6px 0;
            font-size: 22px;
            font-weight: bold;
            color: #2c3e50;
            box-shadow: inset 0 1px 3px rgba(74, 144, 226, 0.1);
        }
        
        .student-details {
            font-size: 10px;
            color: #34495e;
            line-height: 1.4;
            background: rgba(255, 255, 255, 0.7);
            padding: 4px;
            border-radius: 6px;
        }
        
        .detail-row {
            margin: 1px 0;
        }
        
        .label {
            font-weight: 600;
            color: #4a90e2;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                padding: 8mm;
                box-shadow: none;
            }
            
            .seat-card {
                border: 1.5px solid #666 !important;
                background: #f8f8f8 !important;
                box-shadow: none !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .card-header {
                background: #e0e0e0 !important;
                color: #333 !important;
            }
            
            .roll-number {
                background: white !important;
                border: 2px solid #666 !important;
                color: #000 !important;
            }
            
            .label {
                color: #333 !important;
            }
        }
        
        @page {
            size: A4;
            margin: 0;
        }
    </style>
</head>
<body>
    <?php if ($totalStudents > 0): ?>
        <?php for ($page = 0; $page < $totalPages; $page++): ?>
            <div class="page">
                <div class="cards-container">
                    <?php 
                    $startIndex = $page * $cardsPerPage;
                    $endIndex = min($startIndex + $cardsPerPage, $totalStudents);
                    
                    for ($i = $startIndex; $i < $endIndex; $i++): 
                        $s = $students[$i];
                    ?>
                        <div class="seat-card">
                            <div class="card-header">
                                <div class="exam-title">HSC Exam-2025</div>
                                <div class="college-info">দামুড়হুদা, কোড. 295</div>
                            </div>
                            
                            <div class="student-name">
                                <?php echo htmlspecialchars($s['student_name']); ?>
                            </div>
                            
                            <div class="roll-number">
                                <?php echo htmlspecialchars($s['roll_no']); ?>
                            </div>
                            
                            <div class="student-details">
                                <div class="detail-row">
                                    <span class="label">রেজিঃ</span> <?php echo htmlspecialchars($s['reg_no']); ?>
                                </div>
                                <div class="detail-row">
                                    <span class="label">বিভাগ:</span> <?php echo htmlspecialchars($s['group_name']); ?>
                                </div>
                                <div class="detail-row">
                                    <span class="label">শিক্ষাবর্ষ:</span> <?php echo htmlspecialchars($s['session']); ?>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                    
                    <?php 
                    // Fill remaining slots with empty cards if needed
                    $remainingSlots = $cardsPerPage - ($endIndex - $startIndex);
                    for ($j = 0; $j < $remainingSlots; $j++): 
                    ?>
                        <div class="seat-card" style="border: 1px dashed #ddd; background: #f9f9f9; box-shadow: none;">
                            <div style="color: #ccc; font-size: 12px; margin: auto;">Empty</div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        <?php endfor; ?>
    <?php else: ?>
        <div class="page">
            <div style="text-align: center; margin-top: 100px; font-size: 18px; color: #666;">
                কোন শিক্ষার্থী পাওয়া যায়নি
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
