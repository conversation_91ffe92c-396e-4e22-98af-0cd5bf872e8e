<?php
require_once __DIR__ . '/models/Student.php';

// Get search parameters from URL
$subjectCode = $_GET['code'] ?? '';
$searchName = $_GET['search'] ?? '';
$searchRoll = $_GET['roll'] ?? '';
$searchSession = $_GET['session'] ?? '';
$searchGroup = $_GET['group'] ?? '';
$searchFather = $_GET['father'] ?? '';
$searchGender = $_GET['gender'] ?? '';
$searchType = $_GET['type'] ?? '';
$sortBy = $_GET['sort'] ?? 'roll_no';
$sortOrder = $_GET['order'] ?? 'asc';
$perPage = (int)($_GET['per_page'] ?? 25);
$currentPage = (int)($_GET['page'] ?? 1);

// Validate pagination parameters
if ($perPage < 5 || $perPage > 100) {
    $perPage = 25;
}
if ($currentPage < 1) {
    $currentPage = 1;
}

// Subject names mapping
$subjectNames = [
    '101' => 'Bangla',
    '107' => 'English',
    '275' => 'Information & Technology',
    '174' => 'Physics',
    '176' => 'Chemistry',
    '178' => 'Biology',
    '265' => 'Higher Math',
    '253' => 'Accounting',
    '292' => 'Finance & Banking',
    '277' => 'Business Organization & Management',
    '286' => 'Production Management & Marketing',
    '109' => 'Economics',
    '121' => 'Logic',
    '117' => 'Sociology',
    '249' => 'Study Of Islam',
    '271' => 'Social Work',
    '273' => 'Home Science',
    '267' => 'Islamic History',
    '269' => 'Civics & Good Governance',
    '304' => 'History',
    '129' => 'Statistics'
];

$currentSubjectName = $subjectNames[$subjectCode] ?? 'Unknown Subject';

// Get all students and filter by subject code
$studentsWithCode = [];
$totalStudents = 0;
$error = '';

try {
    $student = new Student();
    $allStudents = $student->getAll();
    $totalStudents = count($allStudents);
    
    // Filter students who have the specified subject code
    foreach ($allStudents as $s) {
        $hasCode = false;

        // Check subjects field (new format) and individual sub_* fields (old format)
        if (!empty($s['subjects'])) {
            // New format: comma-separated subjects
            $subjects = explode(',', $s['subjects']);
            foreach ($subjects as $subject) {
                if (trim($subject) === $subjectCode) {
                    $hasCode = true;
                    break;
                }
            }
        }

        // Also check old format for backward compatibility
        if (!$hasCode) {
            for ($i = 1; $i <= 13; $i++) {
                $subValue = trim($s["sub_$i"] ?? '');
                if ($subValue === $subjectCode) {
                    $hasCode = true;
                    break;
                }
            }
        }
        
        if ($hasCode) {
            // Apply advanced search filters
            $matchesFilters = true;

            // Name filter (check both old and new field names)
            $studentName = $s['name'] ?? $s['student_name'] ?? '';
            if (!empty($searchName) && stripos($studentName, $searchName) === false) {
                $matchesFilters = false;
            }

            // Roll number filter (check both old and new field names)
            $rollNo = $s['roll'] ?? $s['roll_no'] ?? '';
            if (!empty($searchRoll) && stripos($rollNo, $searchRoll) === false) {
                $matchesFilters = false;
            }

            // Session filter (check both old and new field names)
            $session = $s['academic_year'] ?? $s['session'] ?? '';
            if (!empty($searchSession) && stripos($session, $searchSession) === false) {
                $matchesFilters = false;
            }

            // Group filter (check both old and new field names)
            $group = $s['department'] ?? $s['group_name'] ?? '';
            if (!empty($searchGroup) && stripos($group, $searchGroup) === false) {
                $matchesFilters = false;
            }

            // Father's name filter
            $fatherName = $s['father_name'] ?? '';
            if (!empty($searchFather) && stripos($fatherName, $searchFather) === false) {
                $matchesFilters = false;
            }

            // Gender filter
            $gender = $s['gender'] ?? 'Male';
            if (!empty($searchGender) && strcasecmp($gender, $searchGender) !== 0) {
                $matchesFilters = false;
            }

            // Type filter (check both old and new field names)
            $type = $s['student_type'] ?? $s['type'] ?? '';
            if (!empty($searchType) && stripos($type, $searchType) === false) {
                $matchesFilters = false;
            }

            if ($matchesFilters) {
                $studentsWithCode[] = $s;
            }
        }
    }

    // Sort students based on selected criteria
    usort($studentsWithCode, function($a, $b) use ($sortBy, $sortOrder) {
        // Map field names for compatibility
        $fieldMap = [
            'roll_no' => ['roll', 'roll_no'],
            'student_name' => ['name', 'student_name'],
            'father_name' => ['father_name'],
            'session' => ['academic_year', 'session'],
            'group_name' => ['department', 'group_name'],
            'type' => ['student_type', 'type']
        ];

        $valueA = '';
        $valueB = '';

        if (isset($fieldMap[$sortBy])) {
            foreach ($fieldMap[$sortBy] as $field) {
                if (!empty($a[$field])) {
                    $valueA = $a[$field];
                    break;
                }
            }
            foreach ($fieldMap[$sortBy] as $field) {
                if (!empty($b[$field])) {
                    $valueB = $b[$field];
                    break;
                }
            }
        } else {
            $valueA = $a[$sortBy] ?? '';
            $valueB = $b[$sortBy] ?? '';
        }

        // Handle numeric sorting for roll_no and id
        if ($sortBy === 'roll_no' || $sortBy === 'id') {
            $valueA = (int)$valueA;
            $valueB = (int)$valueB;
            $result = $valueA - $valueB;
        } else {
            // String comparison for other fields
            $result = strcasecmp($valueA, $valueB);
        }

        // Apply sort order
        return $sortOrder === 'desc' ? -$result : $result;
    });

    // Pagination calculations
    $totalResults = count($studentsWithCode);
    $totalPages = max(1, ceil($totalResults / $perPage)); // Ensure at least 1 page
    $currentPage = max(1, min((int)$currentPage, $totalPages)); // Ensure current page is valid integer
    $offset = ($currentPage - 1) * $perPage;

    // Get students for current page
    $studentsForPage = array_slice($studentsWithCode, $offset, $perPage);

} catch (Exception $e) {
    $error = $e->getMessage();
    $studentsWithCode = [];
    $studentsForPage = [];
    $totalResults = 0;
    $totalPages = 0;
}

$count = $totalResults ?? count($studentsWithCode);
$percentage = $totalStudents > 0 ? round(($count / $totalStudents) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subject Filter - <?php echo $subjectCode; ?> - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>📚 Subject Filter: <?php echo $currentSubjectName; ?> (<?php echo $subjectCode; ?>)</h2>
                </div>
            </div>

            <!-- Subject Code Selection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>🔄 Select Subject Code</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php 
                                $colors = ['primary', 'success', 'info', 'warning', 'danger', 'secondary'];
                                $colorIndex = 0;
                                foreach ($subjectNames as $code => $name): 
                                    $btnColor = $colors[$colorIndex % count($colors)];
                                    $isActive = ($code === $subjectCode) ? 'active' : '';
                                    $colorIndex++;
                                ?>
                                    <div class="col-md-3 mb-2">
                                        <a href="?code=<?php echo $code; ?>" 
                                           class="btn btn-<?php echo $btnColor; ?> w-100 <?php echo $isActive; ?>"
                                           title="<?php echo $name; ?>">
                                            <?php echo $code; ?> - <?php echo $name; ?>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($subjectCode)): ?>
                <!-- Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h3 class="text-success"><?php echo $count; ?></h3>
                                <p class="mb-0">Students Found</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h3 class="text-info"><?php echo $percentage; ?>%</h3>
                                <p class="mb-0">of Total Students</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-secondary">
                            <div class="card-body text-center">
                                <h3 class="text-secondary"><?php echo $totalStudents; ?></h3>
                                <p class="mb-0">Total Students</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h3 class="text-success">
                                    <i class="fas fa-file-csv"></i>
                                </h3>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success dropdown-toggle btn-sm" data-bs-toggle="dropdown" title="Export student data to CSV file">
                                        <i class="fas fa-download me-1"></i>Export CSV
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="export_subject.php?code=<?php echo urlencode($subjectCode); ?>">
                                            <i class="fas fa-download me-2"></i>সব শিক্ষার্থী (<?php echo $count; ?> জন)
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">ফিল্টার অনুযায়ী</h6></li>
                                        <?php if (!empty($searchGroup)): ?>
                                            <li><a class="dropdown-item" href="export_subject.php?code=<?php echo urlencode($subjectCode); ?>&group=<?php echo urlencode($searchGroup); ?>">
                                                <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($searchGroup); ?> গ্রুপ
                                            </a></li>
                                        <?php endif; ?>
                                        <?php if (!empty($searchType)): ?>
                                            <li><a class="dropdown-item" href="export_subject.php?code=<?php echo urlencode($subjectCode); ?>&type=<?php echo urlencode($searchType); ?>">
                                                <i class="fas fa-user-tag me-2"></i><?php echo htmlspecialchars($searchType); ?> টাইপ
                                            </a></li>
                                        <?php endif; ?>
                                        <?php if (!empty($searchGender)): ?>
                                            <li><a class="dropdown-item" href="export_subject.php?code=<?php echo urlencode($subjectCode); ?>&gender=<?php echo urlencode($searchGender); ?>">
                                                <i class="fas fa-venus-mars me-2"></i><?php echo htmlspecialchars($searchGender); ?>
                                            </a></li>
                                        <?php endif; ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="javascript:void(0)" onclick="exportSelectedStudents()">
                                            <i class="fas fa-check-square me-2"></i>নির্বাচিত শিক্ষার্থী
                                        </a></li>
                                    </ul>
                                </div>
                                <p class="mb-0 mt-2 small text-muted">CSV ফাইল</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Search Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">🔍 উন্নত সার্চ অপশন</h5>
                                <button class="btn btn-outline-primary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
                                    <i class="fas fa-search-plus"></i> Advanced Search
                                </button>
                            </div>
                            <div class="card-body">
                                <!-- Basic Search -->
                                <form method="GET" id="searchForm">
                                    <input type="hidden" name="code" value="<?php echo htmlspecialchars($subjectCode); ?>">

                                    <!-- Quick Search Row -->
                                    <div class="row g-3 mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Student Name</label>
                                            <input type="text" class="form-control" name="search"
                                                   placeholder="Search by student name..."
                                                   value="<?php echo htmlspecialchars($searchName); ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Roll Number</label>
                                            <input type="text" class="form-control" name="roll"
                                                   placeholder="Roll number..."
                                                   value="<?php echo htmlspecialchars($searchRoll); ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary flex-fill">
                                                    <i class="fas fa-search"></i> Search
                                                </button>
                                                <a href="?code=<?php echo $subjectCode; ?>" class="btn btn-outline-secondary">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Advanced Search Collapse -->
                                    <div class="collapse <?php echo (!empty($searchSession) || !empty($searchGroup) || !empty($searchFather) || !empty($searchGender) || !empty($searchType)) ? 'show' : ''; ?>" id="advancedSearch">
                                        <hr>
                                        <h6 class="text-primary mb-3"><i class="fas fa-filter"></i> Advanced Filters</h6>

                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label class="form-label">Session</label>
                                                <input type="text" class="form-control" name="session"
                                                       placeholder="e.g., 2023-24"
                                                       value="<?php echo htmlspecialchars($searchSession); ?>">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Group</label>
                                                <select class="form-select" name="group">
                                                    <option value="">All Groups</option>
                                                    <option value="Science" <?php echo $searchGroup === 'Science' ? 'selected' : ''; ?>>Science</option>
                                                    <option value="Commerce" <?php echo $searchGroup === 'Commerce' ? 'selected' : ''; ?>>Commerce</option>
                                                    <option value="Business Studies" <?php echo $searchGroup === 'Business Studies' ? 'selected' : ''; ?>>Business Studies</option>
                                                    <option value="Arts" <?php echo $searchGroup === 'Arts' ? 'selected' : ''; ?>>Arts</option>
                                                    <option value="Humanities" <?php echo $searchGroup === 'Humanities' ? 'selected' : ''; ?>>Humanities</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Gender</label>
                                                <select class="form-select" name="gender">
                                                    <option value="">All Genders</option>
                                                    <option value="Male" <?php echo $searchGender === 'Male' ? 'selected' : ''; ?>>Male</option>
                                                    <option value="Female" <?php echo $searchGender === 'Female' ? 'selected' : ''; ?>>Female</option>
                                                    <option value="Other" <?php echo $searchGender === 'Other' ? 'selected' : ''; ?>>Other</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Type</label>
                                                <select class="form-select" name="type">
                                                    <option value="">All Types</option>
                                                    <option value="Regular" <?php echo $searchType === 'Regular' ? 'selected' : ''; ?>>Regular</option>
                                                    <option value="Irregular" <?php echo $searchType === 'Irregular' ? 'selected' : ''; ?>>Irregular</option>
                                                    <option value="Private" <?php echo $searchType === 'Private' ? 'selected' : ''; ?>>Private</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row g-3 mt-2">
                                            <div class="col-md-6">
                                                <label class="form-label">Father's Name</label>
                                                <input type="text" class="form-control" name="father"
                                                       placeholder="Search by father's name..."
                                                       value="<?php echo htmlspecialchars($searchFather); ?>">
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Sort By</label>
                                                <select class="form-select" name="sort">
                                                    <option value="roll_no" <?php echo $sortBy === 'roll_no' ? 'selected' : ''; ?>>Roll Number</option>
                                                    <option value="student_name" <?php echo $sortBy === 'student_name' ? 'selected' : ''; ?>>Student Name</option>
                                                    <option value="father_name" <?php echo $sortBy === 'father_name' ? 'selected' : ''; ?>>Father's Name</option>
                                                    <option value="session" <?php echo $sortBy === 'session' ? 'selected' : ''; ?>>Session</option>
                                                    <option value="group_name" <?php echo $sortBy === 'group_name' ? 'selected' : ''; ?>>Group</option>
                                                    <option value="id" <?php echo $sortBy === 'id' ? 'selected' : ''; ?>>ID</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">Sort Order</label>
                                                <select class="form-select" name="order">
                                                    <option value="asc" <?php echo $sortOrder === 'asc' ? 'selected' : ''; ?>>Ascending</option>
                                                    <option value="desc" <?php echo $sortOrder === 'desc' ? 'selected' : ''; ?>>Descending</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <div class="d-flex gap-2">
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-search"></i> Apply Advanced Search
                                                    </button>
                                                    <a href="?code=<?php echo $subjectCode; ?>" class="btn btn-outline-danger">
                                                        <i class="fas fa-eraser"></i> Clear All Filters
                                                    </a>
                                                    <button type="button" class="btn btn-outline-info" onclick="saveSearchPreset()">
                                                        <i class="fas fa-save"></i> Save Search
                                                    </button>
                                                    <button type="button" class="btn btn-outline-warning" onclick="loadSearchPreset()">
                                                        <i class="fas fa-folder-open"></i> Load Search
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Results Summary -->
                <?php if (!empty($searchName) || !empty($searchRoll) || !empty($searchSession) || !empty($searchGroup) || !empty($searchFather) || !empty($searchGender) || !empty($searchType)): ?>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-filter"></i> Active Filters:</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <?php if (!empty($searchName)): ?>
                                        <span class="badge bg-primary">Name: <?php echo htmlspecialchars($searchName); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchRoll)): ?>
                                        <span class="badge bg-success">Roll: <?php echo htmlspecialchars($searchRoll); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchSession)): ?>
                                        <span class="badge bg-info">Session: <?php echo htmlspecialchars($searchSession); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchGroup)): ?>
                                        <span class="badge bg-warning">Group: <?php echo htmlspecialchars($searchGroup); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchFather)): ?>
                                        <span class="badge bg-secondary">Father: <?php echo htmlspecialchars($searchFather); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchGender)): ?>
                                        <span class="badge bg-danger">Gender: <?php echo htmlspecialchars($searchGender); ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($searchType)): ?>
                                        <span class="badge bg-dark">Type: <?php echo htmlspecialchars($searchType); ?></span>
                                    <?php endif; ?>
                                    <span class="badge bg-light text-dark">Sort: <?php echo ucfirst($sortBy); ?> (<?php echo strtoupper($sortOrder); ?>)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Quick Filter Buttons -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-bolt"></i> Quick Filters</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showSearchStats()" title="Search Statistics">
                                        <i class="fas fa-chart-pie"></i> Stats
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" onclick="exportFilteredResults()" title="Export Results">
                                        <i class="fas fa-download"></i> Export
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="showKeyboardShortcuts()" title="Keyboard Shortcuts">
                                        <i class="fas fa-keyboard"></i> Help
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row g-2 align-items-center">
                                    <div class="col-auto">
                                        <span class="text-muted small fw-bold">Gender:</span>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('gender', 'Male')">
                                            <i class="fas fa-male"></i> Male
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-danger btn-sm" onclick="quickFilter('gender', 'Female')">
                                            <i class="fas fa-female"></i> Female
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <span class="text-muted">|</span>
                                    </div>
                                    <div class="col-auto">
                                        <span class="text-muted small fw-bold">Group:</span>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-success btn-sm" onclick="quickFilter('group', 'Science')">
                                            <i class="fas fa-flask"></i> Science
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-warning btn-sm" onclick="quickFilter('group', 'Commerce')">
                                            <i class="fas fa-chart-line"></i> Commerce
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-primary btn-sm" onclick="quickFilter('group', 'Business Studies')">
                                            <i class="fas fa-briefcase"></i> Business Studies
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-info btn-sm" onclick="quickFilter('group', 'Arts')">
                                            <i class="fas fa-palette"></i> Arts
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="quickFilter('group', 'Humanities')">
                                            <i class="fas fa-book"></i> Humanities
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <span class="text-muted">|</span>
                                    </div>
                                    <div class="col-auto">
                                        <span class="text-muted small fw-bold">Type:</span>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-secondary btn-sm" onclick="quickFilter('type', 'Regular')">
                                            <i class="fas fa-user-check"></i> Regular
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-dark btn-sm" onclick="quickFilter('type', 'Private')">
                                            <i class="fas fa-user-tie"></i> Private
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <span class="text-muted">|</span>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-danger btn-sm" onclick="clearFilter('gender')" title="Clear Gender Filter">
                                            <i class="fas fa-times"></i> Clear Gender
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-outline-danger btn-sm" onclick="clearFilter('group')" title="Clear Group Filter">
                                            <i class="fas fa-times"></i> Clear Group
                                        </button>
                                    </div>
                                </div>

                                <!-- Results Summary -->
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <div class="alert alert-light mb-0">
                                            <div class="row align-items-center">
                                                <div class="col-md-8">
                                                    <h6 class="text-info mb-1">
                                                        📊 Subject Code <?php echo $subjectCode; ?> - <?php echo $currentSubjectName; ?>
                                                    </h6>
                                                    <div>
                                                        <span class="badge bg-success me-2"><?php echo $count; ?> students found</span>
                                                        <span class="badge bg-info me-2"><?php echo $totalStudents; ?> total students</span>
                                                        <span class="badge bg-warning me-2"><?php echo $percentage; ?>% of total</span>
                                                        <?php if ((int)$count > 0): ?>
                                                            <span class="badge bg-secondary">Page <?php echo (int)$currentPage; ?> of <?php echo (int)$totalPages; ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="col-md-4 text-end">
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-success btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                                            <i class="fas fa-id-card me-1"></i>সীট কার্ড প্রিন্ট
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li><a class="dropdown-item" href="working_seat_cards.php?subject=<?php echo urlencode($subjectCode); ?>" target="_blank">
                                                                <i class="fas fa-users me-2"></i>সব শিক্ষার্থীর সীটকার্ড
                                                            </a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><h6 class="dropdown-header">ফিল্টার অনুযায়ী</h6></li>
                                                            <?php if (!empty($searchGroup)): ?>
                                                                <li><a class="dropdown-item" href="working_seat_cards.php?subject=<?php echo urlencode($subjectCode); ?>&group=<?php echo urlencode($searchGroup); ?>" target="_blank">
                                                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($searchGroup); ?> গ্রুপ
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <?php if (!empty($searchType)): ?>
                                                                <li><a class="dropdown-item" href="working_seat_cards.php?subject=<?php echo urlencode($subjectCode); ?>&type=<?php echo urlencode($searchType); ?>" target="_blank">
                                                                    <i class="fas fa-user-tag me-2"></i><?php echo htmlspecialchars($searchType); ?> টাইপ
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <?php if (!empty($searchGender)): ?>
                                                                <li><a class="dropdown-item" href="working_seat_cards.php?subject=<?php echo urlencode($subjectCode); ?>&gender=<?php echo urlencode($searchGender); ?>" target="_blank">
                                                                    <i class="fas fa-venus-mars me-2"></i><?php echo htmlspecialchars($searchGender); ?>
                                                                </a></li>
                                                            <?php endif; ?>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item" href="javascript:void(0)" onclick="printSelectedStudents()">
                                                                <i class="fas fa-check-square me-2"></i>নির্বাচিত শিক্ষার্থী
                                                            </a></li>
                                                        </ul>
                                                    </div>
                                                    <a href="view_students.php" class="btn btn-outline-primary btn-sm me-2">
                                                        <i class="fas fa-arrow-left"></i> Back to All Students
                                                    </a>
                                                    <a href="subject_codes_dashboard.php" class="btn btn-outline-info btn-sm">
                                                        <i class="fas fa-chart-bar"></i> Dashboard
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pagination Controls -->
                <?php if ($count > 0): ?>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center">
                                                <span class="me-2">Show:</span>
                                                <?php
                                                $perPageOptions = [5, 10, 25, 50, 100];
                                                $currentParams = $_GET;
                                                foreach ($perPageOptions as $option):
                                                    $currentParams['per_page'] = $option;
                                                    $currentParams['page'] = 1; // Reset to first page
                                                    $isActive = ($perPage == $option) ? 'active' : '';
                                                ?>
                                                    <a href="?<?php echo http_build_query($currentParams); ?>"
                                                       class="btn btn-outline-primary btn-sm me-1 <?php echo $isActive; ?>">
                                                        <?php echo $option; ?>
                                                    </a>
                                                <?php endforeach; ?>
                                                <span class="ms-2 text-muted">
                                                    Showing <?php echo (int)$offset + 1; ?>-<?php echo min((int)$offset + (int)$perPage, (int)$count); ?> of <?php echo (int)$count; ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <?php if ((int)$totalPages > 1): ?>
                                                <nav aria-label="Page navigation">
                                                    <ul class="pagination pagination-sm justify-content-end mb-0">
                                                        <?php
                                                        $currentParams = $_GET;

                                                        // Previous button
                                                        if ((int)$currentPage > 1):
                                                            $currentParams['page'] = (int)$currentPage - 1;
                                                        ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="?<?php echo http_build_query($currentParams); ?>">
                                                                    <i class="fas fa-chevron-left"></i>
                                                                </a>
                                                            </li>
                                                        <?php else: ?>
                                                            <li class="page-item disabled">
                                                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                                                            </li>
                                                        <?php endif; ?>

                                                        <?php
                                                        // Page numbers
                                                        $startPage = max(1, (int)$currentPage - 2);
                                                        $endPage = min((int)$totalPages, (int)$currentPage + 2);

                                                        for ($i = (int)$startPage; $i <= (int)$endPage; $i++):
                                                            $currentParams['page'] = $i;
                                                            $isActive = ($i == (int)$currentPage) ? 'active' : '';
                                                        ?>
                                                            <li class="page-item <?php echo $isActive; ?>">
                                                                <a class="page-link" href="?<?php echo http_build_query($currentParams); ?>">
                                                                    <?php echo $i; ?>
                                                                </a>
                                                            </li>
                                                        <?php endfor; ?>

                                                        <?php
                                                        // Next button
                                                        if ((int)$currentPage < (int)$totalPages):
                                                            $currentParams['page'] = (int)$currentPage + 1;
                                                        ?>
                                                            <li class="page-item">
                                                                <a class="page-link" href="?<?php echo http_build_query($currentParams); ?>">
                                                                    <i class="fas fa-chevron-right"></i>
                                                                </a>
                                                            </li>
                                                        <?php else: ?>
                                                            <li class="page-item disabled">
                                                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                                                            </li>
                                                        <?php endif; ?>
                                                    </ul>
                                                </nav>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Error Display -->
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Students List -->
                <div class="row">
                    <div class="col-12">
                        <?php if ($count > 0): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h5>👥 Students with Subject <?php echo $subjectCode; ?> (<?php echo $count; ?> found)</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Bulk Actions -->
                                    <div class="mb-3">
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="selectAllStudents()">
                                                <i class="fas fa-check-square me-1"></i>সব নির্বাচন
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="deselectAllStudents()">
                                                <i class="fas fa-square me-1"></i>সব বাতিল
                                            </button>
                                            <button class="btn btn-sm btn-success" onclick="printSelectedStudents()">
                                                <i class="fas fa-print me-1"></i>নির্বাচিত সীটকার্ড প্রিন্ট
                                            </button>
                                            <span class="btn btn-sm btn-outline-info" id="selectedCount">০টি নির্বাচিত</span>
                                        </div>
                                    </div>

                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-dark">
                                                <tr>
                                                    <?php
                                                    // Function to create sortable column header
                                                    function createSortableHeader($field, $label, $currentSort, $currentOrder, $params) {
                                                        $newOrder = ($currentSort === $field && $currentOrder === 'asc') ? 'desc' : 'asc';
                                                        $icon = '';
                                                        if ($currentSort === $field) {
                                                            $icon = $currentOrder === 'asc' ? ' <i class="fas fa-sort-up"></i>' : ' <i class="fas fa-sort-down"></i>';
                                                        } else {
                                                            $icon = ' <i class="fas fa-sort text-muted"></i>';
                                                        }

                                                        $url = '?' . http_build_query(array_merge($params, ['sort' => $field, 'order' => $newOrder]));
                                                        return "<a href='$url' class='text-white text-decoration-none'>$label$icon</a>";
                                                    }

                                                    $sortParams = [
                                                        'code' => $subjectCode,
                                                        'search' => $searchName,
                                                        'roll' => $searchRoll,
                                                        'session' => $searchSession,
                                                        'group' => $searchGroup,
                                                        'father' => $searchFather,
                                                        'gender' => $searchGender,
                                                        'type' => $searchType,
                                                        'per_page' => $perPage,
                                                        'page' => $currentPage
                                                    ];
                                                    ?>
                                                    <th>
                                                        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleAllCheckboxes(this)">
                                                    </th>
                                                    <th><?php echo createSortableHeader('id', 'ID', $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th><?php echo createSortableHeader('student_name', 'Student Name', $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th><?php echo createSortableHeader('father_name', "Father's Name", $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th><?php echo createSortableHeader('roll_no', 'Roll No', $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th><?php echo createSortableHeader('session', 'Session', $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th><?php echo createSortableHeader('group_name', 'Group', $sortBy, $sortOrder, $sortParams); ?></th>
                                                    <th>Found In</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($studentsForPage as $s): ?>
                                                    <?php
                                                    // Find which subjects contain the target code
                                                    $foundIn = [];

                                                    // Check new subjects field format
                                                    if (!empty($s['subjects'])) {
                                                        $subjects = explode(',', $s['subjects']);
                                                        foreach ($subjects as $index => $subject) {
                                                            if (trim($subject) === $subjectCode) {
                                                                $foundIn[] = "Sub " . ($index + 1);
                                                            }
                                                        }
                                                    }

                                                    // Also check old format for backward compatibility
                                                    for ($i = 1; $i <= 13; $i++) {
                                                        if (trim($s["sub_$i"] ?? '') === $subjectCode) {
                                                            $foundIn[] = "Sub $i";
                                                        }
                                                    }

                                                    // Get field values with compatibility
                                                    $studentName = $s['name'] ?? $s['student_name'] ?? 'Unknown';
                                                    $fatherName = $s['father_name'] ?? '';
                                                    $rollNo = $s['roll'] ?? $s['roll_no'] ?? '';
                                                    $session = $s['academic_year'] ?? $s['session'] ?? '';
                                                    $group = $s['department'] ?? $s['group_name'] ?? '';
                                                    ?>
                                                    <tr>
                                                        <td>
                                                            <input type="checkbox" class="form-check-input student-checkbox" value="<?php echo htmlspecialchars($rollNo); ?>" data-name="<?php echo htmlspecialchars($studentName); ?>">
                                                        </td>
                                                        <td><?php echo $s['id'] ?? 'N/A'; ?></td>
                                                        <td>
                                                            <strong><?php echo htmlspecialchars($studentName); ?></strong>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($fatherName); ?></td>
                                                        <td><?php echo htmlspecialchars($rollNo); ?></td>
                                                        <td><?php echo htmlspecialchars($session); ?></td>
                                                        <td><?php echo htmlspecialchars($group); ?></td>
                                                        <td>
                                                            <span class="badge bg-success"><?php echo count($foundIn); ?>x</span>
                                                            <?php echo implode(', ', $foundIn); ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group" role="group">
                                                                <a href="edit_student.php?id=<?php echo $s['id']; ?>" class="btn btn-sm btn-primary" title="Edit Student">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <a href="view_student_details.php?id=<?php echo $s['id']; ?>" class="btn btn-sm btn-info" title="View Details">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="working_seat_cards.php?search=<?php echo urlencode($studentName); ?>" class="btn btn-sm btn-success" target="_blank" title="Generate Seat Card">
                                                                    <i class="fas fa-id-card"></i>
                                                                </a>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning text-center">
                                <h5>📭 No Students Found</h5>
                                <p>No students found with subject code <strong><?php echo $subjectCode; ?></strong>
                                <?php if (!empty($searchName)): ?>
                                    and name containing "<strong><?php echo htmlspecialchars($searchName); ?></strong>"
                                <?php endif; ?>
                                .</p>
                                <p class="text-muted">Make sure students have been uploaded with the correct subject codes.</p>
                                <a href="add_student.php" class="btn btn-primary">➕ Add Student</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-info text-center">
                    <h5>📋 Select a Subject Code</h5>
                    <p>Please select a subject code from the buttons above to filter students.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Advanced Search Functions
        function saveSearchPreset() {
            const form = document.getElementById('searchForm');
            const formData = new FormData(form);
            const searchData = {};

            for (let [key, value] of formData.entries()) {
                if (value && key !== 'code') {
                    searchData[key] = value;
                }
            }

            if (Object.keys(searchData).length === 0) {
                alert('কোনো সার্চ ফিল্টার সেট করা নেই।');
                return;
            }

            const presetName = prompt('সার্চ প্রিসেটের নাম দিন:');
            if (presetName) {
                let savedPresets = JSON.parse(localStorage.getItem('searchPresets') || '{}');
                savedPresets[presetName] = searchData;
                localStorage.setItem('searchPresets', JSON.stringify(savedPresets));
                alert(`সার্চ প্রিসেট "${presetName}" সেভ করা হয়েছে!`);
            }
        }

        function loadSearchPreset() {
            const savedPresets = JSON.parse(localStorage.getItem('searchPresets') || '{}');
            const presetNames = Object.keys(savedPresets);

            if (presetNames.length === 0) {
                alert('কোনো সেভ করা সার্চ প্রিসেট নেই।');
                return;
            }

            let presetList = 'সেভ করা সার্চ প্রিসেট:\n\n';
            presetNames.forEach((name, index) => {
                presetList += `${index + 1}. ${name}\n`;
            });

            const selection = prompt(presetList + '\nকোন প্রিসেট লোড করতে চান? (নাম লিখুন)');

            if (selection && savedPresets[selection]) {
                const preset = savedPresets[selection];
                const form = document.getElementById('searchForm');

                // Clear form first
                form.reset();

                // Set code value
                form.querySelector('[name="code"]').value = '<?php echo htmlspecialchars($subjectCode); ?>';

                // Apply preset values
                Object.keys(preset).forEach(key => {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        field.value = preset[key];
                    }
                });

                // Show advanced search if needed
                const advancedFields = ['session', 'group', 'father', 'gender', 'type', 'sort', 'order'];
                const hasAdvancedFilters = advancedFields.some(field => preset[field]);

                if (hasAdvancedFilters) {
                    const advancedCollapse = document.getElementById('advancedSearch');
                    if (!advancedCollapse.classList.contains('show')) {
                        new bootstrap.Collapse(advancedCollapse).show();
                    }
                }

                alert(`সার্চ প্রিসেট "${selection}" লোড করা হয়েছে! এখন সার্চ করুন।`);
            }
        }

        // Auto-submit form when filters change (optional)
        function enableAutoSearch() {
            const form = document.getElementById('searchForm');
            const inputs = form.querySelectorAll('input, select');

            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (this.name !== 'code') {
                        // Add a small delay to prevent too many requests
                        clearTimeout(window.searchTimeout);
                        window.searchTimeout = setTimeout(() => {
                            form.submit();
                        }, 500);
                    }
                });
            });
        }

        // Initialize auto-search (uncomment if needed)
        // enableAutoSearch();

        // Quick filter buttons
        function quickFilter(field, value) {
            const form = document.getElementById('searchForm');
            const fieldElement = form.querySelector(`[name="${field}"]`);

            if (fieldElement) {
                fieldElement.value = value;
                form.submit();
            }
        }

        // Clear specific filter
        function clearFilter(field) {
            const form = document.getElementById('searchForm');
            const fieldElement = form.querySelector(`[name="${field}"]`);

            if (fieldElement) {
                fieldElement.value = '';
                form.submit();
            }
        }

        // Export filtered results
        function exportFilteredResults() {
            const currentUrl = new URL(window.location);
            currentUrl.pathname = currentUrl.pathname.replace('subject_filter.php', 'export_subject.php');
            window.open(currentUrl.toString(), '_blank');
        }

        // Show search statistics
        function showSearchStats() {
            const totalResults = <?php echo $count; ?>;
            const totalStudents = <?php echo $totalStudents; ?>;
            const percentage = <?php echo $percentage; ?>;

            alert(`সার্চ পরিসংখ্যান:\n\nমোট ফলাফল: ${totalResults} জন\nমোট ছাত্র: ${totalStudents} জন\nশতকরা: ${percentage}%`);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+F to focus on name search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.querySelector('[name="search"]').focus();
            }

            // Ctrl+R to clear all filters
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                window.location.href = '?code=<?php echo $subjectCode; ?>';
            }
        });

        // Show keyboard shortcuts help
        function showKeyboardShortcuts() {
            alert('কীবোর্ড শর্টকাট:\n\nCtrl+F: নাম সার্চে ফোকাস\nCtrl+R: সব ফিল্টার ক্লিয়ার');
        }

        // Bulk selection functions
        function toggleAllCheckboxes(masterCheckbox) {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = masterCheckbox.checked;
            });
            updateSelectedCount();
        }

        function selectAllStudents() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
            document.getElementById('selectAll').checked = true;
            updateSelectedCount();
        }

        function deselectAllStudents() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('selectAll').checked = false;
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
            const count = checkedBoxes.length;
            document.getElementById('selectedCount').textContent = `${count}টি নির্বাচিত`;
        }

        function printSelectedStudents() {
            const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
            const rollNumbers = [];

            checkedBoxes.forEach(cb => {
                if (cb.value) {
                    rollNumbers.push(cb.value);
                }
            });

            if (rollNumbers.length === 0) {
                alert('কোন শিক্ষার্থী নির্বাচন করা হয়নি!');
                return;
            }

            const url = 'working_seat_cards.php?subject=<?php echo urlencode($subjectCode); ?>&rolls=' + rollNumbers.join(',');
            window.open(url, '_blank');
        }

        function exportSelectedStudents() {
            const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
            const rollNumbers = [];

            checkedBoxes.forEach(cb => {
                if (cb.value) {
                    rollNumbers.push(cb.value);
                }
            });

            if (rollNumbers.length === 0) {
                alert('কোন শিক্ষার্থী নির্বাচন করা হয়নি!');
                return;
            }

            const url = 'export_subject.php?code=<?php echo urlencode($subjectCode); ?>&rolls=' + rollNumbers.join(',');
            window.location.href = url;
        }

        // Add event listeners for checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(cb => {
                cb.addEventListener('change', updateSelectedCount);
            });
            updateSelectedCount();
        });
    </script>
</body>
</html>
