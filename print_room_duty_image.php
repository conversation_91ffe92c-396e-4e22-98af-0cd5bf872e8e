<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$date = $_GET['date'] ?? '';
$shift = $_GET['shift'] ?? 'all';

// Redirect if no date selected
if (empty($date)) {
    header("Location: duty_assignments_management.php?tab=room-duty");
    exit;
}

// Get assignments for selected date
$assignments = $teacherManager->getDutyAssignments($date);

// Filter by shift if needed
if ($shift !== 'all') {
    $assignments = array_filter($assignments, function($assignment) use ($shift) {
        return $assignment['duty_shift'] === $shift;
    });
}

// Group assignments by room
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomNumber = !empty($assignment['room_number']) ? $assignment['room_number'] : 'অনির্দিষ্ট';
    if (!isset($roomAssignments[$roomNumber])) {
        $roomAssignments[$roomNumber] = [];
    }
    $roomAssignments[$roomNumber][] = $assignment;
}

// Sort rooms numerically
ksort($roomAssignments, SORT_NATURAL);

// Get date details
$dateDetails = $teacherManager->getDutyDateDetails($date);

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Check if college logo exists
$logoPath = 'uploads/college_logo.jpg';
$hasLogo = file_exists($logoPath);

// Get signatures if available
$principalSignature = '';
$convenerSignature = '';
$signatureFiles = glob('uploads/signatures/*.jpg');
foreach ($signatureFiles as $signFile) {
    if (stripos($signFile, 'principal') !== false) {
        $principalSignature = $signFile;
    } elseif (stripos($signFile, 'convener') !== false) {
        $convenerSignature = $signFile;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম অনুযায়ী ডিউটি তালিকা - <?php echo formatDateBengali($date); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
            font-size: 16px;
            line-height: 1.4;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
                font-size: 14px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .print-container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
            }
            
            .page-break {
                page-break-after: always;
            }
            
            .duty-sheet {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
        
        .print-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn-control {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .btn-control:hover {
            transform: translateY(-5px);
        }
        
        .duty-sheet {
            background: white;
            border-radius: 8px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
            break-inside: avoid;
        }
        
        .sheet-header {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            text-align: center;
            padding: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 15px;
        }
        
        .college-logo {
            height: 80px;
            margin-bottom: 10px;
        }
        
        .college-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            color: #333;
        }
        
        .college-address {
            font-size: 16px;
            color: #555;
            margin-bottom: 10px;
        }
        
        .exam-info {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        
        .exam-title {
            font-size: 18px;
            font-weight: 600;
            color: #343a40;
        }
        
        .duty-date {
            font-size: 16px;
            color: #495057;
        }
        
        .room-info {
            background-color: #e9f2ff;
            padding: 10px 15px;
            border-bottom: 1px solid #b8daff;
        }
        
        .room-title {
            font-size: 18px;
            font-weight: 600;
            color: #0056b3;
        }
        
        .teacher-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            padding: 15px;
        }
        
        .teacher-card {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
            background: white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }
        
        .teacher-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        
        .teacher-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 2px solid white;
            box-shadow: 0 3px 8px rgba(0,0,0,0.1);
        }
        
        .teacher-details {
            padding: 10px;
        }
        
        .teacher-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .teacher-info {
            font-size: 14px;
            color: #6c757d;
            margin-bottom: 3px;
        }
        
        .signature-area {
            display: flex;
            justify-content: space-between;
            padding: 30px 50px;
            margin-top: 20px;
            gap: 30px;
        }
        
        .signature {
            flex: 1;
            text-align: center;
        }
        
        .signature img {
            height: 50px;
            margin-bottom: 5px;
        }
        
        .signature-line {
            width: 80%;
            height: 1px;
            background-color: #333;
            margin: 10px auto 5px;
        }
        
        .signature-text {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button onclick="window.print()" class="btn btn-primary btn-control" title="প্রিন্ট">
            <i class="fas fa-print fa-lg"></i>
        </button>
        <a href="duty_assignments_management.php?tab=room-duty" class="btn btn-secondary btn-control" title="ফিরে যান">
            <i class="fas fa-arrow-left fa-lg"></i>
        </a>
    </div>
    
    <?php if (empty($roomAssignments)): ?>
        <div class="container mt-5">
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                এই তারিখে কোন রুম ডিউটি বন্টন করা হয়নি!
            </div>
            <div class="text-center">
                <a href="duty_assignments_management.php?tab=room-duty" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                </a>
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($roomAssignments as $roomNumber => $teachers): ?>
            <div class="container duty-sheet">
                <div class="sheet-header">
                    <div class="logo-container">
                        <?php if ($hasLogo): ?>
                            <img src="<?php echo $logoPath; ?>" alt="College Logo" class="college-logo">
                        <?php endif; ?>
                    </div>
                    <div class="college-name">আকুল পদ্ম শাহ ডিগ্রী কলেজ</div>
                    <div class="college-address">নাছিরকোনা, চুয়াডাঙ্গা</div>
                    <div class="exam-info">
                        <div class="exam-title">
                            <strong>HSC পরীক্ষা- <?php echo date('Y'); ?></strong><br>
                            <?php if (!empty($dateDetails)): ?>
                                <?php echo $dateDetails['exam_type'] == 'theory' ? 'তত্ত্বীয় পরীক্ষা' : 'ব্যবহারিক পরীক্ষা'; ?>
                                (<?php 
                                if ($shift == 'Morning' || ($shift == 'all' && $dateDetails['exam_shift'] == 'Morning')) echo 'সকাল শিফট';
                                elseif ($shift == 'Afternoon' || ($shift == 'all' && $dateDetails['exam_shift'] == 'Afternoon')) echo 'বিকাল শিফট';
                                else echo 'সকাল ও বিকাল শিফট';
                                ?>)
                            <?php else: ?>
                                পরীক্ষা ডিউটি
                            <?php endif; ?>
                        </div>
                        <div class="duty-date">তারিখঃ <?php echo formatDateBengali($date); ?></div>
                    </div>
                </div>
                
                <div class="room-info">
                    <h5 class="room-title">
                        <i class="fas fa-door-open me-2"></i>
                        <?php echo $roomNumber === 'অনির্দিষ্ট' ? 'অনির্দিষ্ট রুম' : 'রুম নম্বরঃ ' . $roomNumber; ?>
                        <span class="badge bg-primary ms-2"><?php echo count($teachers); ?> জন শিক্ষক</span>
                    </h5>
                </div>
                
                <div class="teacher-grid">
                    <?php foreach ($teachers as $teacher): ?>
                        <div class="teacher-card">
                            <div class="teacher-header">
                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" 
                                         alt="Teacher Photo" class="teacher-photo">
                                <?php else: ?>
                                    <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                <div>
                                    <div class="teacher-name"><?php echo htmlspecialchars($teacher['teacher_name']); ?></div>
                                    <div class="badge bg-secondary"><?php echo htmlspecialchars($teacher['duty_shift']); ?> Shift</div>
                                </div>
                            </div>
                            <div class="teacher-details">
                                <div class="teacher-info">
                                    <i class="fas fa-book me-2 text-primary"></i> <?php echo htmlspecialchars($teacher['subject']); ?>
                                </div>
                                <div class="teacher-info">
                                    <i class="fas fa-user-tie me-2 text-info"></i> <?php echo htmlspecialchars($teacher['designation']); ?>
                                </div>
                                <div class="teacher-info">
                                    <i class="fas fa-mobile-alt me-2 text-success"></i> <?php echo htmlspecialchars($teacher['mobile']); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="signature-area">
                    <div class="signature">
                        <?php if (!empty($convenerSignature) && file_exists($convenerSignature)): ?>
                            <img src="<?php echo $convenerSignature; ?>" alt="Convener Signature">
                        <?php else: ?>
                            <div class="signature-line"></div>
                        <?php endif; ?>
                        <div class="signature-text">পরীক্ষা কমিটির আহবায়ক</div>
                    </div>
                    <div class="signature">
                        <?php if (!empty($principalSignature) && file_exists($principalSignature)): ?>
                            <img src="<?php echo $principalSignature; ?>" alt="Principal Signature">
                        <?php else: ?>
                            <div class="signature-line"></div>
                        <?php endif; ?>
                        <div class="signature-text">অধ্যক্ষ</div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html> 