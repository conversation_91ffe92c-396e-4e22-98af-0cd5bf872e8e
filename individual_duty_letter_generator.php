<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Get selected date
$selectedDate = $_GET['date'] ?? '';
$dutyAssignments = [];

if ($selectedDate) {
    $dutyAssignments = $teacherManager->getDutyAssignments($selectedDate);
}

// Get signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');

// Add getTeacherById method if not exists
if (!method_exists($teacherManager, 'getTeacherById')) {
    class_alias('TeacherManager', 'TeacherManagerExtended');
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ব্যক্তিগত ডিউটি লেটার জেনারেটর - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .date-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .date-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .teacher-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .teacher-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        
        .letter-preview {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 1rem;
            min-height: 400px;
        }
        
        .signature-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .signature-preview {
            max-width: 100px;
            max-height: 50px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-file-signature me-3"></i>ব্যক্তিগত ডিউটি লেটার জেনারেটর
                </h1>
                <p class="text-white-50">প্রতিটি শিক্ষকের জন্য আলাদা ডিউটি পত্র তৈরি করুন</p>
            </div>

            <!-- Navigation -->
            <div class="text-center mb-4">
                <a href="index.php" class="btn btn-outline-light me-2">
                    <i class="fas fa-home me-2"></i>হোম
                </a>
                <a href="duty_letter_generator.php" class="btn btn-outline-light me-2">
                    <i class="fas fa-file-alt me-2"></i>সাধারণ ডিউটি লেটার
                </a>
                <a href="signature_upload.php" class="btn btn-outline-light">
                    <i class="fas fa-signature me-2"></i>স্বাক্ষর আপলোড
                </a>
            </div>

            <div class="row">
                <!-- Date Selection -->
                <div class="col-lg-4">
                    <div class="main-card">
                        <h4 class="mb-4">
                            <i class="fas fa-calendar text-primary me-2"></i>তারিখ নির্বাচন
                        </h4>
                        
                        <?php if (empty($dutyDates)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                কোন ডিউটি বন্টন পাওয়া যায়নি!
                                <br><br>
                                <a href="teacher_duty_management.php" class="btn btn-warning btn-sm">
                                    <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($dutyDates as $date): ?>
                                <div class="date-card <?php echo $selectedDate === $date ? 'border-primary bg-primary text-white' : ''; ?>">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">
                                                <?php echo formatDateBengali($date); ?>
                                            </h6>
                                            <small class="<?php echo $selectedDate === $date ? 'text-white-50' : 'text-muted'; ?>">
                                                <?php 
                                                $count = count($teacherManager->getDutyAssignments($date));
                                                echo $count . ' জন শিক্ষক';
                                                ?>
                                            </small>
                                        </div>
                                        <div>
                                            <a href="?date=<?php echo urlencode($date); ?>" 
                                               class="btn <?php echo $selectedDate === $date ? 'btn-light' : 'btn-primary'; ?> btn-sm">
                                                <?php echo $selectedDate === $date ? 'নির্বাচিত' : 'নির্বাচন'; ?>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Teacher Selection & Letter Generation -->
                <div class="col-lg-8">
                    <div class="main-card">
                        <?php if ($selectedDate && !empty($dutyAssignments)): ?>
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <h4>
                                    <i class="fas fa-users text-success me-2"></i>
                                    শিক্ষক নির্বাচন - <?php echo formatDateBengali($selectedDate); ?>
                                </h4>
                                <div>
                                    <a href="print_all_individual_duty_letters.php?all_duties=1"
                                       target="_blank" class="btn btn-success">
                                        <i class="fas fa-print me-2"></i>সব শিক্ষকের সব ডিউটি লেটার
                                    </a>
                                </div>
                            </div>

                            <!-- Signature Status -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="signature-info">
                                        <h6><i class="fas fa-user-tie me-2"></i>প্রিন্সিপাল স্বাক্ষর</h6>
                                        <?php if ($principalSignature): ?>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>" 
                                                     alt="Principal Signature" class="signature-preview me-2">
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>আপলোড করা আছে
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>স্বাক্ষর আপলোড করা হয়নি
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="signature-info">
                                        <h6><i class="fas fa-user-check me-2"></i>কনভেনার স্বাক্ষর</h6>
                                        <?php if ($convenerSignature): ?>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>" 
                                                     alt="Convener Signature" class="signature-preview me-2">
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i>আপলোড করা আছে
                                                </span>
                                            </div>
                                        <?php else: ?>
                                            <div class="text-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>স্বাক্ষর আপলোড করা হয়নি
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Teacher List -->
                            <h5 class="mb-3">
                                <i class="fas fa-list me-2"></i>শিক্ষক তালিকা (<?php echo count($dutyAssignments); ?> জন)
                            </h5>
                            
                            <div class="row">
                                <?php foreach ($dutyAssignments as $index => $assignment): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="teacher-card">
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <?php 
                                                    $teacher = $teacherManager->getTeacherById($assignment['teacher_id']);
                                                    if (!empty($teacher['photo']) && file_exists($teacher['photo'])): 
                                                    ?>
                                                        <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" 
                                                             alt="Teacher Photo" class="teacher-photo">
                                                    <?php else: ?>
                                                        <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                            <i class="fas fa-user text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1"><?php echo htmlspecialchars($assignment['teacher_name']); ?></h6>
                                                    <small class="text-muted">
                                                        <?php echo htmlspecialchars($assignment['designation']); ?><br>
                                                        <?php echo htmlspecialchars($assignment['subject'] ?? ''); ?>
                                                    </small>
                                                    <?php if ($assignment['room_number']): ?>
                                                        <br><span class="badge bg-info">রুম: <?php echo htmlspecialchars($assignment['room_number']); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div>
                                                    <?php
                                                    // Get all duty dates for this teacher
                                                    $allDutyDates = $teacherManager->getAllDutyDatesForTeacher($assignment['teacher_id']);
                                                    $dutyDatesString = [];
                                                    foreach ($allDutyDates as $dutyDate) {
                                                        $dutyDatesString[] = $dutyDate['duty_date'];
                                                    }
                                                    $datesParam = implode(',', $dutyDatesString);
                                                    ?>
                                                    <a href="print_individual_duty_letter.php?dates=<?php echo urlencode($datesParam); ?>&teacher_id=<?php echo $assignment['teacher_id']; ?>"
                                                       target="_blank" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-file-alt me-1"></i>সব ডিউটি লেটার
                                                    </a>
                                                    <small class="d-block text-muted mt-1">
                                                        <?php echo count($allDutyDates); ?> টি ডিউটি
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Letter Preview -->
                            <div class="letter-preview">
                                <h6 class="text-center mb-4">
                                    <i class="fas fa-eye me-2"></i>লেটার প্রিভিউ
                                </h6>
                                <div class="text-center">
                                    <p class="mb-3">
                                        <strong>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</strong><br>
                                        দামুড়হুদা, চুয়াডাঙ্গা
                                    </p>
                                    <p class="mb-3">
                                        <strong>স্মারক নং:</strong> ০১/<?php echo date('Y'); ?>-<?php echo date('m', strtotime($selectedDate)); ?>
                                    </p>
                                    <p class="mb-3">
                                        <strong>বিষয়:</strong> HSC পরীক্ষা-২০২৫ এর ডিউটি সংক্রান্ত।
                                    </p>
                                    <div class="text-start">
                                        <p>প্রিয় [শিক্ষকের নাম],</p>
                                        <p>
                                            আগামী <strong><?php echo formatDateBengali($selectedDate); ?></strong> তারিখে অনুষ্ঠিতব্য 
                                            HSC পরীক্ষা-২০২৫ এর তত্ত্বাবধানের জন্য আপনাকে ডিউটিতে নিয়োজিত করা হলো।
                                        </p>
                                        <p>
                                            <strong>আপনার দায়িত্ব:</strong><br>
                                            • নির্ধারিত সময়ে উপস্থিত থাকা<br>
                                            • পরীক্ষার নিয়মকানুন যথাযথভাবে পালন<br>
                                            • কোন সমস্যা হলে তাৎক্ষণিক কর্তৃপক্ষকে অবগত করা
                                        </p>
                                    </div>
                                </div>
                            </div>

                        <?php elseif ($selectedDate): ?>
                            <div class="alert alert-warning text-center">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                <h5>এই তারিখে কোন ডিউটি বন্টন পাওয়া যায়নি!</h5>
                                <p>প্রথমে শিক্ষকদের ডিউটি বন্টন করুন।</p>
                                <a href="teacher_duty_management.php" class="btn btn-warning">
                                    <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info text-center">
                                <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                <h5>তারিখ নির্বাচন করুন</h5>
                                <p>ব্যক্তিগত ডিউটি লেটার তৈরি করতে বাম পাশ থেকে একটি তারিখ নির্বাচন করুন।</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
