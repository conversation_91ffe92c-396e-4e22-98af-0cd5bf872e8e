<?php
require_once __DIR__ . '/models/Student.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>Debug Students</title></head><body>";
echo "<h1>Student Database Debug</h1>";

try {
    $student = new Student();
    
    // Check total students
    $allStudents = $student->getAll();
    echo "<h2>Total Students: " . count($allStudents) . "</h2>";
    
    if (count($allStudents) > 0) {
        // Show first student structure
        echo "<h3>First Student Data Structure:</h3>";
        echo "<pre>";
        print_r($allStudents[0]);
        echo "</pre>";
        
        // Check subject 253 specifically
        echo "<h3>Students with Subject Code 253:</h3>";
        $students253 = $student->getStudentsWithSubjectCode('253');
        echo "<p>Found: " . count($students253) . " students</p>";
        
        if (count($students253) > 0) {
            echo "<h4>First 3 students with subject 253:</h4>";
            for ($i = 0; $i < min(3, count($students253)); $i++) {
                $s = $students253[$i];
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
                echo "<strong>Name:</strong> " . htmlspecialchars($s['student_name']) . "<br>";
                echo "<strong>Roll:</strong> " . htmlspecialchars($s['roll_no']) . "<br>";
                echo "<strong>Subjects:</strong> ";
                for ($j = 1; $j <= 13; $j++) {
                    $subKey = 'sub_' . $j;
                    if (!empty($s[$subKey])) {
                        echo $s[$subKey] . " ";
                    }
                }
                echo "<br>";
                echo "</div>";
            }
        }
        
        // Check what subjects exist
        echo "<h3>All Subject Codes Found:</h3>";
        $allSubjects = [];
        foreach ($allStudents as $s) {
            for ($j = 1; $j <= 13; $j++) {
                $subKey = 'sub_' . $j;
                if (!empty($s[$subKey])) {
                    $allSubjects[$s[$subKey]] = ($allSubjects[$s[$subKey]] ?? 0) + 1;
                }
            }
        }
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Subject Code</th><th>Student Count</th></tr>";
        foreach ($allSubjects as $code => $count) {
            echo "<tr><td>$code</td><td>$count</td></tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: red;'>No students found in database!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "</body></html>";
?>
