<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Get selected date
$selectedDate = $_GET['date'] ?? '';
$dutyAssignments = [];

if ($selectedDate) {
    $dutyAssignments = $teacherManager->getDutyAssignments($selectedDate);
}

// Get signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি লেটার জেনারেটর - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .date-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .date-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .teacher-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .teacher-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .signature-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .signature-preview {
            max-width: 100px;
            max-height: 50px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-file-alt me-3"></i>ডিউটি লেটার জেনারেটর
                    </h1>
                    <p class="text-white-50">পরীক্ষার ডিউটি লেটার তৈরি ও প্রিন্ট করুন</p>
                </div>

                <!-- Navigation -->
                <div class="text-center mb-4">
                    <a href="index.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-home me-2"></i>হোম
                    </a>
                    <a href="teacher_duty_management.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনা
                    </a>
                    <a href="signature_upload.php" class="btn btn-outline-light">
                        <i class="fas fa-signature me-2"></i>স্বাক্ষর আপলোড
                    </a>
                </div>

                <div class="row">
                    <!-- Date Selection -->
                    <div class="col-lg-4">
                        <div class="main-card">
                            <h4 class="mb-4">
                                <i class="fas fa-calendar text-primary me-2"></i>তারিখ নির্বাচন
                            </h4>
                            
                            <?php if (empty($dutyDates)): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    কোন ডিউটি বন্টন পাওয়া যায়নি!
                                    <br><br>
                                    <a href="teacher_duty_management.php" class="btn btn-warning btn-sm">
                                        <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                    </a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($dutyDates as $date): ?>
                                    <div class="date-card <?php echo $selectedDate === $date ? 'border-primary bg-primary text-white' : ''; ?>">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo date('d F Y', strtotime($date)); ?>
                                                </h6>
                                                <small class="<?php echo $selectedDate === $date ? 'text-white-50' : 'text-muted'; ?>">
                                                    <?php 
                                                    $count = count($teacherManager->getDutyAssignments($date));
                                                    echo $count . ' জন শিক্ষক';
                                                    ?>
                                                </small>
                                            </div>
                                            <div>
                                                <a href="?date=<?php echo urlencode($date); ?>" 
                                                   class="btn <?php echo $selectedDate === $date ? 'btn-light' : 'btn-primary'; ?> btn-sm">
                                                    <?php echo $selectedDate === $date ? 'নির্বাচিত' : 'নির্বাচন'; ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Letter Preview & Generation -->
                    <div class="col-lg-8">
                        <div class="main-card">
                            <?php if ($selectedDate && !empty($dutyAssignments)): ?>
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4>
                                        <i class="fas fa-file-alt text-success me-2"></i>
                                        ডিউটি লেটার - <?php echo date('d F Y', strtotime($selectedDate)); ?>
                                    </h4>
                                    <div>
                                        <a href="print_duty_letter.php?date=<?php echo urlencode($selectedDate); ?>" 
                                           target="_blank" class="btn btn-success">
                                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                        </a>
                                    </div>
                                </div>

                                <!-- Signature Status -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="signature-info">
                                            <h6><i class="fas fa-user-tie me-2"></i>প্রিন্সিপাল স্বাক্ষর</h6>
                                            <?php if ($principalSignature): ?>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>" 
                                                         alt="Principal Signature" class="signature-preview me-2">
                                                    <span class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>আপলোড করা আছে
                                                    </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-warning">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>স্বাক্ষর আপলোড করা হয়নি
                                                    <br>
                                                    <a href="signature_upload.php" class="btn btn-warning btn-sm mt-2">
                                                        <i class="fas fa-upload me-1"></i>আপলোড করুন
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="signature-info">
                                            <h6><i class="fas fa-user-check me-2"></i>কনভেনার স্বাক্ষর</h6>
                                            <?php if ($convenerSignature): ?>
                                                <div class="d-flex align-items-center">
                                                    <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>" 
                                                         alt="Convener Signature" class="signature-preview me-2">
                                                    <span class="text-success">
                                                        <i class="fas fa-check-circle me-1"></i>আপলোড করা আছে
                                                    </span>
                                                </div>
                                            <?php else: ?>
                                                <div class="text-warning">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>স্বাক্ষর আপলোড করা হয়নি
                                                    <br>
                                                    <a href="signature_upload.php" class="btn btn-warning btn-sm mt-2">
                                                        <i class="fas fa-upload me-1"></i>আপলোড করুন
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Teacher List -->
                                <h5 class="mb-3">
                                    <i class="fas fa-users me-2"></i>ডিউটিতে নিয়োজিত শিক্ষকগণ (<?php echo count($dutyAssignments); ?> জন)
                                </h5>
                                
                                <div class="teacher-list">
                                    <?php foreach ($dutyAssignments as $index => $assignment): ?>
                                        <div class="teacher-item">
                                            <div class="row align-items-center">
                                                <div class="col-md-1">
                                                    <span class="badge bg-primary"><?php echo $index + 1; ?></span>
                                                </div>
                                                <div class="col-md-4">
                                                    <strong><?php echo htmlspecialchars($assignment['name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($assignment['designation']); ?></small>
                                                </div>
                                                <div class="col-md-3">
                                                    <small class="text-muted">বিষয়:</small>
                                                    <br><?php echo htmlspecialchars($assignment['subject']); ?>
                                                </div>
                                                <div class="col-md-2">
                                                    <small class="text-muted">মোবাইল:</small>
                                                    <br><?php echo htmlspecialchars($assignment['mobile']); ?>
                                                </div>
                                                <div class="col-md-2">
                                                    <?php if ($assignment['room_number']): ?>
                                                        <span class="badge bg-info">রুম: <?php echo htmlspecialchars($assignment['room_number']); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                            <?php elseif ($selectedDate): ?>
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                    <h5>এই তারিখে কোন ডিউটি বন্টন পাওয়া যায়নি!</h5>
                                    <p>প্রথমে শিক্ষকদের ডিউটি বন্টন করুন।</p>
                                    <a href="teacher_duty_management.php" class="btn btn-warning">
                                        <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                    <h5>তারিখ নির্বাচন করুন</h5>
                                    <p>ডিউটি লেটার তৈরি করতে বাম পাশ থেকে একটি তারিখ নির্বাচন করুন।</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
