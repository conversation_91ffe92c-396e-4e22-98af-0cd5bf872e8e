<?php
session_start();
require_once 'config/database.php';

$message = '';
$messageType = '';
$uploadedStudents = [];

// Ensure uploads directory exists and is writable
function ensureUploadsDirectory() {
    $uploadsDir = __DIR__ . '/uploads';
    if (!is_dir($uploadsDir)) {
        if (!mkdir($uploadsDir, 0777, true)) {
            throw new Exception('Cannot create uploads directory. Please check file permissions.');
        }
    }

    if (!is_writable($uploadsDir)) {
        if (!chmod($uploadsDir, 0777)) {
            throw new Exception('Uploads directory is not writable. Please check file permissions.');
        }
    }

    return $uploadsDir;
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        // Ensure uploads directory exists
        ensureUploadsDirectory();
        // Check file upload
        if ($_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload failed. Error code: ' . $_FILES['csv_file']['error']);
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($_FILES['csv_file']['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'csv') {
            throw new Exception('Please upload a CSV file only.');
        }

        // Check file size (max 5MB)
        if ($_FILES['csv_file']['size'] > 5 * 1024 * 1024) {
            throw new Exception('File size too large. Maximum 5MB allowed.');
        }

        // Read CSV file
        $csvFile = $_FILES['csv_file']['tmp_name'];
        $handle = fopen($csvFile, 'r');
        
        if (!$handle) {
            throw new Exception('Cannot read CSV file.');
        }

        // Database connection with error handling
        try {
            $database = new Database();
            $pdo = $database->getConnection();

            if (!$pdo) {
                throw new Exception('Database connection failed. Please check if XAMPP MySQL is running.');
            }
        } catch (Exception $dbError) {
            throw new Exception('Database connection error: ' . $dbError->getMessage() .
                               '<br><br><strong>Solutions:</strong><br>' .
                               '1. Start XAMPP Control Panel<br>' .
                               '2. Start MySQL service<br>' .
                               '3. <a href="system_startup_check.php">Run system check</a>');
        }

        // Prepare SQL statement
        $sql = "INSERT INTO students (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);

        $rowCount = 0;
        $successCount = 0;
        $errors = [];

        // Read CSV line by line
        while (($row = fgetcsv($handle)) !== FALSE) {
            $rowCount++;
            
            // Skip header row
            if ($rowCount === 1) {
                continue;
            }

            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }

            try {
                // Map CSV columns to database fields
                $name = trim($row[7] ?? ''); // Student Name
                $fatherName = trim($row[8] ?? ''); // Father Name
                $gender = trim($row[9] ?? 'Male'); // Gender
                $roll = trim($row[2] ?? ''); // Roll No
                $registration = trim($row[3] ?? ''); // Reg No
                $department = trim($row[6] ?? 'Science'); // Group
                $academicYear = trim($row[4] ?? '2024'); // Session
                $studentType = trim($row[5] ?? 'Regular'); // Type

                // Validate gender
                if (!in_array($gender, ['Male', 'Female', 'Other'])) {
                    $gender = 'Male'; // Default value
                }

                // Collect subjects (Sub 1 to Sub 13)
                $subjects = [];
                for ($i = 10; $i <= 22; $i++) {
                    if (!empty(trim($row[$i] ?? ''))) {
                        $subjects[] = trim($row[$i]);
                    }
                }
                $subjectsString = implode(',', $subjects);

                // Validate required fields
                if (empty($name)) {
                    $errors[] = "Row $rowCount: Student name is required";
                    continue;
                }

                if (empty($roll)) {
                    $errors[] = "Row $rowCount: Roll number is required";
                    continue;
                }

                // Execute insert
                $result = $stmt->execute([
                    $name,
                    $fatherName,
                    $gender,
                    $roll,
                    $registration,
                    $department,
                    $academicYear,
                    $studentType,
                    $subjectsString
                ]);

                if ($result) {
                    $successCount++;
                    $uploadedStudents[] = [
                        'name' => $name,
                        'father_name' => $fatherName,
                        'gender' => $gender,
                        'roll' => $roll,
                        'registration' => $registration,
                        'department' => $department,
                        'academic_year' => $academicYear,
                        'student_type' => $studentType,
                        'subjects' => $subjectsString
                    ];
                }

            } catch (PDOException $e) {
                $errors[] = "Row $rowCount: Database error - " . $e->getMessage();
            }
        }

        fclose($handle);

        // Show results
        if ($successCount > 0) {
            $message = "Successfully uploaded $successCount students!";
            $messageType = 'success';
            
            if (!empty($errors)) {
                $message .= "<br><br>Errors encountered:<br>" . implode('<br>', array_slice($errors, 0, 5));
                if (count($errors) > 5) {
                    $message .= "<br>... and " . (count($errors) - 5) . " more errors.";
                }
            }
        } else {
            $message = "No students were uploaded.<br>" . implode('<br>', $errors);
            $messageType = 'danger';
        }

    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Upload - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background: rgba(255,255,255,0.2);
        }
        .upload-area.dragover {
            border-color: #28a745;
            background: rgba(40,167,69,0.1);
        }
        .file-input {
            display: none;
        }
        .upload-icon {
            font-size: 4rem;
            color: #007bff;
            margin-bottom: 20px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-file-csv"></i> CSV File Upload</h3>
                            <div>
                                <button onclick="downloadSampleCSV()" class="btn btn-info">
                                    <i class="fas fa-download"></i> Download Sample CSV
                                </button>
                                <a href="view_students.php" class="btn btn-secondary">
                                    <i class="fas fa-list"></i> View Students
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            
                            <!-- Message Display -->
                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                    <?php echo $message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <!-- Upload Form -->
                            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                <div class="upload-area" id="uploadArea" onclick="document.getElementById('csvFile').click()">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h4>Click Here to Upload CSV File</h4>
                                    <p class="text-muted">or drag and drop your CSV file here</p>
                                    <input type="file" name="csv_file" id="csvFile" class="file-input" accept=".csv" required>
                                    <div class="btn btn-primary mt-3">
                                        <i class="fas fa-folder-open"></i> Choose CSV File
                                    </div>
                                </div>
                                
                                <div id="fileInfo" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <strong>Selected File:</strong> <span id="fileName"></span><br>
                                        <strong>Size:</strong> <span id="fileSize"></span>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-success btn-lg" id="uploadBtn" disabled>
                                        <i class="fas fa-upload"></i> Upload CSV File
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg ms-2" id="previewBtn" disabled onclick="previewCSV()">
                                        <i class="fas fa-eye"></i> Preview CSV
                                    </button>
                                </div>
                            </form>

                            <!-- Upload Results -->
                            <?php if (!empty($uploadedStudents)): ?>
                                <div class="mt-5">
                                    <h5><i class="fas fa-check-circle text-success"></i> Uploaded Students (First 10):</h5>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Name</th>
                                                    <th>Father Name</th>
                                                    <th>Gender</th>
                                                    <th>Roll</th>
                                                    <th>Registration</th>
                                                    <th>Department</th>
                                                    <th>Year</th>
                                                    <th>Type</th>
                                                    <th>Subjects</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach (array_slice($uploadedStudents, 0, 10) as $student): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($student['name']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['father_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['gender']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['roll']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['registration']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['department']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['academic_year']); ?></td>
                                                    <td><?php echo htmlspecialchars($student['student_type']); ?></td>
                                                    <td><?php echo htmlspecialchars(substr($student['subjects'], 0, 20)) . (strlen($student['subjects']) > 20 ? '...' : ''); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Instructions -->
                            <div class="row mt-5">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h5><i class="fas fa-info-circle"></i> File Requirements</h5>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success"></i> CSV files only (.csv)</li>
                                                <li><i class="fas fa-check text-success"></i> Maximum file size: 5MB</li>
                                                <li><i class="fas fa-check text-success"></i> First row should contain headers</li>
                                                <li><i class="fas fa-check text-success"></i> Student Name and Roll Number are required</li>
                                                <li><i class="fas fa-check text-success"></i> Use UTF-8 encoding for Bengali text</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h5><i class="fas fa-list"></i> Column Order (Required)</h5>
                                        </div>
                                        <div class="card-body">
                                            <small>
                                                <strong>Columns 1-10:</strong> C.Code, EIIN, Roll No, Reg No, Session, Type, Group, Student Name, Father Name, Gender<br>
                                                <strong>Columns 11-23:</strong> Sub 1, Sub 2, Sub 3, Sub 4, Sub 5, Sub 6, Sub 7, Sub 8, Sub 9, Sub 10, Sub 11, Sub 12, Sub 13
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('csvFile');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // File input change
        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.style.display = 'block';
                uploadBtn.disabled = false;
                document.getElementById('previewBtn').disabled = false;

                // Check file type
                if (!file.name.toLowerCase().endsWith('.csv')) {
                    alert('Please select a CSV file only.');
                    fileInput.value = '';
                    fileInfo.style.display = 'none';
                    uploadBtn.disabled = true;
                    document.getElementById('previewBtn').disabled = true;
                }
            }
        }

        function previewCSV() {
            const file = fileInput.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n');

                let previewHTML = '<div class="alert alert-info"><h5><i class="fas fa-eye"></i> CSV Preview (First 5 rows):</h5>';
                previewHTML += '<div class="table-responsive"><table class="table table-sm table-bordered">';

                for (let i = 0; i < Math.min(5, lines.length); i++) {
                    if (lines[i].trim()) {
                        const cells = lines[i].split(',');
                        previewHTML += '<tr>';
                        cells.forEach(cell => {
                            previewHTML += `<td style="font-size: 11px;">${cell.trim()}</td>`;
                        });
                        previewHTML += '</tr>';
                    }
                }

                previewHTML += '</table></div></div>';

                // Show preview in a modal or replace file info
                const existingPreview = document.getElementById('csvPreview');
                if (existingPreview) {
                    existingPreview.remove();
                }

                const previewDiv = document.createElement('div');
                previewDiv.id = 'csvPreview';
                previewDiv.innerHTML = previewHTML;
                fileInfo.parentNode.insertBefore(previewDiv, fileInfo.nextSibling);
            };
            reader.readAsText(file);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function downloadSampleCSV() {
            const csvContent = `C.Code,EIIN,Roll No,Reg No,Session,Type,Group,Student Name,Father Name,Gender,Sub 1,Sub 2,Sub 3,Sub 4,Sub 5,Sub 6,Sub 7,Sub 8,Sub 9,Sub 10,Sub 11,Sub 12,Sub 13
295,136257,100001,REG2024001,2024,Regular,Science,আব্দুল করিম,মোঃ রহিম,Male,101,102,103,104,105,106,107,108,109,110,111,112,113
295,136257,100002,REG2024002,2024,Regular,Science,ফাতেমা খাতুন,মোঃ আলী,Female,101,102,103,104,105,106,107,108,109,110,111,,
295,136257,100003,IMP2024001,2024,Improvement,Science,মোহাম্মদ রহিম,মোঃ করিম,Male,106,107,108,109,,,,,,,,,`;

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'sample_students.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', function() {
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
            uploadBtn.disabled = true;
        });
    </script>
</body>
</html>
