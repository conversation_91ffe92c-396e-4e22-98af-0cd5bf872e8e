<?php
require_once __DIR__ . '/models/Student.php';
require_once __DIR__ . '/utils/DatabaseHelper.php';
require_once __DIR__ . '/config/database.php';

// Get student ID from URL
$studentId = $_GET['id'] ?? null;

if (!$studentId) {
    header('Location: view_students.php');
    exit;
}

// Get student information
$student = new Student();
$studentData = $student->getById($studentId);

if (!$studentData) {
    header('Location: view_students.php?error=student_not_found');
    exit;
}

// Create database helper for field mapping
$database = new Database();
$db = $database->getConnection();
$dbHelper = new DatabaseHelper($db);

// Extract subjects using helper
$subjects = $dbHelper->extractSubjects($studentData);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থীর বিস্তারিত তথ্য - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Hind Siliguri', Arial, sans-serif;
        }
        .student-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .student-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .student-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            border: 4px solid rgba(255, 255, 255, 0.3);
        }
        .info-section {
            padding: 30px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
        }
        .info-value {
            color: #212529;
            flex: 1;
            text-align: right;
        }
        .subject-badge {
            display: inline-block;
            margin: 4px;
            padding: 10px 16px;
            background: #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            color: #495057;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .subject-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .subject-badge.primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .action-buttons {
            padding: 20px 30px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <div class="student-card">
                        <!-- Header Section -->
                        <div class="student-header">
                            <div class="student-avatar">
                                <i class="fas fa-user fa-3x"></i>
                            </div>
                            <h2 class="mb-2"><?php echo htmlspecialchars($dbHelper->getStudentName($studentData)); ?></h2>
                            <p class="mb-0">
                                <i class="fas fa-id-badge me-2"></i>
                                রোল: <?php echo htmlspecialchars($dbHelper->getRollNumber($studentData)); ?>
                            </p>
                        </div>

                        <!-- Personal Information -->
                        <div class="info-section">
                            <h5 class="text-primary mb-4">
                                <i class="fas fa-user-circle me-2"></i>ব্যক্তিগত তথ্য
                            </h5>
                            
                            <div class="info-row">
                                <span class="info-label">ID:</span>
                                <span class="info-value"><?php echo htmlspecialchars($studentData['id']); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">রেজিস্ট্রেশন:</span>
                                <span class="info-value"><?php echo htmlspecialchars($dbHelper->getRegistrationNumber($studentData)); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">পিতার নাম:</span>
                                <span class="info-value"><?php echo htmlspecialchars($studentData['father_name'] ?? 'N/A'); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">লিঙ্গ:</span>
                                <span class="info-value"><?php echo htmlspecialchars($studentData['gender'] ?? 'N/A'); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">বিভাগ:</span>
                                <span class="info-value"><?php echo htmlspecialchars($dbHelper->getDepartment($studentData)); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">সেশন:</span>
                                <span class="info-value"><?php echo htmlspecialchars($dbHelper->getAcademicYear($studentData)); ?></span>
                            </div>
                            
                            <div class="info-row">
                                <span class="info-label">ধরন:</span>
                                <span class="info-value">
                                    <span class="badge bg-<?php echo ($dbHelper->getStudentType($studentData) === 'Regular') ? 'success' : 'warning'; ?>">
                                        <?php echo htmlspecialchars($dbHelper->getStudentType($studentData)); ?>
                                    </span>
                                </span>
                            </div>
                        </div>

                        <!-- Subjects Section -->
                        <div class="info-section border-top">
                            <h5 class="text-primary mb-4">
                                <i class="fas fa-book me-2"></i>বিষয়সমূহ (<?php echo count($subjects); ?>টি)
                            </h5>
                            
                            <?php if (!empty($subjects)): ?>
                                <div class="subjects-container">
                                    <?php foreach ($subjects as $subject):
                                        if (!empty($subject)): ?>
                                            <span class="subject-badge <?php echo ($subject === '101') ? 'primary' : ''; ?>">
                                                <?php echo htmlspecialchars($subject); ?>
                                            </span>
                                        <?php endif;
                                    endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    কোনো বিষয় পাওয়া যায়নি
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <div class="d-flex justify-content-between">
                                <a href="view_students.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>ফিরে যান
                                </a>
                                <div>
                                    <a href="edit_student.php?id=<?php echo $studentData['id']; ?>" class="btn btn-warning me-2">
                                        <i class="fas fa-edit me-2"></i>সম্পাদনা
                                    </a>
                                    <a href="working_seat_cards.php?rolls=<?php echo urlencode($dbHelper->getRollNumber($studentData)); ?>" 
                                       class="btn btn-primary me-2" target="_blank">
                                        <i class="fas fa-id-card me-2"></i>সিট কার্ড
                                    </a>
                                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                        <i class="fas fa-trash me-2"></i>মুছুন
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Metadata -->
                        <?php if (!empty($studentData['created_at'])): ?>
                            <div class="text-center py-3 border-top">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    যোগ করা হয়েছে: <?php echo date('d/m/Y H:i', strtotime($studentData['created_at'])); ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete() {
            if (confirm('আপনি কি নিশ্চিত যে এই শিক্ষার্থীকে মুছে ফেলতে চান?\n\nনাম: <?php echo addslashes($dbHelper->getStudentName($studentData)); ?>\nরোল: <?php echo addslashes($dbHelper->getRollNumber($studentData)); ?>')) {
                window.location.href = 'delete_student.php?id=<?php echo $studentData['id']; ?>';
            }
        }
    </script>
</body>
</html>
