<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/Student.php';

try {
    $subjectCode = $_GET['code'] ?? '';
    
    if (empty($subjectCode)) {
        // Return total students if no subject code
        $database = new Database();
        $db = $database->getConnection();
        $stmt = $db->query("SELECT COUNT(*) as total FROM students");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo json_encode([
            'success' => true,
            'count' => $total,
            'subject_code' => '',
            'students' => []
        ]);
        exit;
    }
    
    $student = new Student();
    $students = $student->getStudentsWithSubjectCode($subjectCode);
    
    // Sort by roll number
    usort($students, function($a, $b) {
        return (int)$a['roll_no'] - (int)$b['roll_no'];
    });
    
    // Get subject name from subject_filter.php mapping
    $subjectNames = [
        '101' => 'Bangla',
        '107' => 'English',
        '275' => 'Information & Technology',
        '174' => 'Physics',
        '176' => 'Chemistry',
        '178' => 'Biology',
        '265' => 'Higher Math',
        '253' => 'Accounting',
        '292' => 'Finance & Banking',
        '277' => 'Business Organization & Management',
        '286' => 'Production Management & Marketing',
        '109' => 'Economics',
        '121' => 'Logic',
        '117' => 'Sociology',
        '249' => 'Study Of Islam',
        '271' => 'Social Work',
        '273' => 'Home Science',
        '267' => 'Islamic History',
        '269' => 'Civics & Good Governance',
        '304' => 'History',
        '129' => 'Statistics'
    ];

    echo json_encode([
        'success' => true,
        'count' => count($students),
        'subject_code' => $subjectCode,
        'subject_name' => $subjectNames[$subjectCode] ?? 'অজানা বিষয়',
        'students' => $students,
        'roll_range' => [
            'min' => count($students) > 0 ? min(array_column($students, 'roll_no')) : 0,
            'max' => count($students) > 0 ? max(array_column($students, 'roll_no')) : 0
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'count' => 0,
        'students' => []
    ]);
}
?>
