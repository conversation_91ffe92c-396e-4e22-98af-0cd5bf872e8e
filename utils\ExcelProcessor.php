<?php
// Check if vendor/autoload.php exists
if (!file_exists(__DIR__ . '/../vendor/autoload.php')) {
    throw new Exception('Composer dependencies not installed. Please run "composer install" or use manual student entry instead.');
}

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../models/Student.php';

use PhpOffice\PhpSpreadsheet\IOFactory;

class ExcelProcessor {
    private $student;
    private $allowedExtensions = ['xlsx', 'xls', 'csv'];

    public function __construct() {
        $this->student = new Student();
    }

    public function validateFile($file) {
        $errors = [];

        // Check if file was uploaded
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = "File upload error occurred.";
            return $errors;
        }

        // Check file size (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            $errors[] = "File size too large. Maximum 10MB allowed.";
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedExtensions)) {
            $errors[] = "Invalid file format. Only Excel (.xlsx, .xls) and CSV files are allowed.";
        }

        return $errors;
    }

    public function processExcelFile($filePath) {
        try {
            $spreadsheet = IOFactory::load($filePath);
            $worksheet = $spreadsheet->getActiveSheet();
            $rows = $worksheet->toArray();

            // Remove header row
            $header = array_shift($rows);
            
            $students = [];
            $errors = [];
            $rowNumber = 2; // Starting from row 2 (after header)

            foreach ($rows as $row) {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    $rowNumber++;
                    continue;
                }

                $studentData = $this->mapRowToStudent($row, $rowNumber);
                
                if ($studentData['valid']) {
                    $students[] = $studentData['data'];
                } else {
                    $errors = array_merge($errors, $studentData['errors']);
                }
                
                $rowNumber++;
            }

            return [
                'success' => empty($errors),
                'students' => $students,
                'errors' => $errors,
                'total_rows' => count($rows),
                'valid_rows' => count($students)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'students' => [],
                'errors' => ["Error reading Excel file: " . $e->getMessage()],
                'total_rows' => 0,
                'valid_rows' => 0
            ];
        }
    }

    private function mapRowToStudent($row, $rowNumber) {
        $errors = [];
        
        // Map Excel columns to database fields
        $studentData = [
            'c_code' => trim($row[0] ?? ''),
            'eiin' => trim($row[1] ?? ''),
            'roll_no' => trim($row[2] ?? ''),
            'reg_no' => trim($row[3] ?? ''),
            'session' => trim($row[4] ?? ''),
            'type' => trim($row[5] ?? ''),
            'group_name' => trim($row[6] ?? ''),
            'student_name' => trim($row[7] ?? ''),
            'father_name' => trim($row[8] ?? ''),
            'gender' => trim($row[9] ?? ''),
            'sub_1' => trim($row[10] ?? ''),
            'sub_2' => trim($row[11] ?? ''),
            'sub_3' => trim($row[12] ?? ''),
            'sub_4' => trim($row[13] ?? ''),
            'sub_5' => trim($row[14] ?? ''),
            'sub_6' => trim($row[15] ?? ''),
            'sub_7' => trim($row[16] ?? ''),
            'sub_8' => trim($row[17] ?? ''),
            'sub_9' => trim($row[18] ?? ''),
            'sub_10' => trim($row[19] ?? ''),
            'sub_11' => trim($row[20] ?? ''),
            'sub_12' => trim($row[21] ?? ''),
            'sub_13' => trim($row[22] ?? '')
        ];

        // Validate required fields
        if (empty($studentData['student_name'])) {
            $errors[] = "Row {$rowNumber}: Student name is required.";
        }

        // Validate gender
        if (!empty($studentData['gender']) && !in_array($studentData['gender'], ['Male', 'Female', 'Other'])) {
            $errors[] = "Row {$rowNumber}: Invalid gender value. Use 'Male', 'Female', or 'Other'.";
        }

        return [
            'valid' => empty($errors),
            'data' => $studentData,
            'errors' => $errors
        ];
    }

    public function saveStudents($students) {
        try {
            return $this->student->bulkInsert($students);
        } catch (Exception $e) {
            throw new Exception("Error saving students: " . $e->getMessage());
        }
    }
}
?>
