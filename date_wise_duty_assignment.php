<?php
session_start();
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

// Handle duty assignment
if (isset($_POST['assign_duty'])) {
    $dutyDate = $_POST['duty_date'];
    $selectedTeachers = $_POST['selected_teachers'] ?? [];
    $roomAssignments = $_POST['room_assignments'] ?? [];
    $dutyShift = $_POST['duty_shift'] ?? 'Morning';

    if (!empty($dutyDate) && !empty($selectedTeachers)) {
        try {
            // Prepare room assignments array
            $roomData = [];
            foreach ($selectedTeachers as $teacherId) {
                $roomData[$teacherId] = $roomAssignments[$teacherId] ?? null;
            }

            // Update database structure once to support shift-specific duties
            $teacherManager->updateDutyAssignmentsTableStructure();
            
            $teacherManager->assignDuty($dutyDate, $selectedTeachers, $roomData, $dutyShift);

            // Update session data for room-wise assignment compatibility
            if (!isset($_SESSION['duty_assignments'])) {
                $_SESSION['duty_assignments'] = [];
            }

            // Get all teachers from database to ensure we have the latest data
            $allTeachers = $teacherManager->getAllTeachers();

            // Update uploaded_teachers session with database data
            $_SESSION['uploaded_teachers'] = [];
            foreach ($allTeachers as $index => $teacher) {
                $_SESSION['uploaded_teachers'][$index] = $teacher;
            }

            // Store teacher indices in session for room assignment page
            $teacherIndices = [];
            foreach ($selectedTeachers as $teacherId) {
                // Find teacher index in uploaded teachers array
                foreach ($_SESSION['uploaded_teachers'] as $index => $teacher) {
                    if (isset($teacher['id']) && $teacher['id'] == $teacherId) {
                        $teacherIndices[] = $index;
                        break;
                    }
                }
            }
            $_SESSION['duty_assignments'][$dutyDate] = $teacherIndices;

            $message = 'ডিউটি সফলভাবে বন্টন করা হয়েছে!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'ডিউটি বন্টনে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'তারিখ এবং শিক্ষক নির্বাচন করুন!';
        $messageType = 'warning';
    }
}

// Handle date-wise duty reset
if (isset($_POST['reset_duty'])) {
    $dutyDate = $_POST['duty_date'];
    $dutyShift = $_POST['duty_shift'] ?? 'all';
    
    if (!empty($dutyDate)) {
        try {
            if ($dutyShift === 'all') {
                // Remove all duty assignments for this date
                $teacherManager->removeDutyAssignment($dutyDate);
            } else {
                // Only remove specific shift assignments
                $sql = "DELETE FROM duty_assignments WHERE duty_date = ? AND duty_shift = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$dutyDate, $dutyShift]);
            }
            
            // Also remove from session if it exists
            if (isset($_SESSION['duty_assignments'][$dutyDate])) {
                unset($_SESSION['duty_assignments'][$dutyDate]);
            }
            
            $message = formatDateBengali($dutyDate) . ' তারিখের ' . ($dutyShift === 'all' ? 'সকল' : ($dutyShift === 'Morning' ? 'সকালের' : 'বিকালের')) . ' ডিউটি সফলভাবে রিসেট করা হয়েছে!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'ডিউটি রিসেট করতে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'তারিখ নির্বাচন করুন!';
        $messageType = 'warning';
    }
}

// Get all teachers
$teachers = $teacherManager->getAllTeachers();

// Handle teacher search
$searchTerm = $_GET['search'] ?? $_POST['search'] ?? '';
if (!empty($searchTerm)) {
    $searchTermLower = mb_strtolower($searchTerm, 'UTF-8');
    $teachers = array_filter($teachers, function($teacher) use ($searchTermLower) {
        return mb_strpos(mb_strtolower($teacher['name'], 'UTF-8'), $searchTermLower) !== false
            || mb_strpos(mb_strtolower($teacher['subject'], 'UTF-8'), $searchTermLower) !== false
            || mb_strpos(mb_strtolower($teacher['designation'], 'UTF-8'), $searchTermLower) !== false
            || mb_strpos(mb_strtolower($teacher['mobile'], 'UTF-8'), $searchTermLower) !== false;
    });
}

// Get selected date assignments
$selectedDate = $_GET['date'] ?? '';
$selectedShift = $_GET['shift'] ?? 'all';
$currentAssignments = [];
if ($selectedDate) {
    $currentAssignments = $teacherManager->getDutyAssignments($selectedDate, '', $selectedShift);
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

if (isset($_POST['update_duty_assignment'])) {
    $dutyId = $_POST['edit_duty_id'];
    $teacherId = $_POST['edit_teacher_id'];
    $dutyDate = $_POST['edit_duty_date'];
    $roomNumber = $_POST['edit_room_number'];
    try {
        $teacherManager->updateTeacherDuty($dutyId, $teacherId, $dutyDate, $roomNumber);
        echo '<div class="alert alert-success mt-3">ডিউটি সফলভাবে আপডেট হয়েছে!</div>';
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-3">ত্রুটি: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}
if (isset($_POST['confirm_delete_duty_assignment'])) {
    $dutyId = $_POST['delete_duty_id'];
    try {
        $teacherManager->deleteTeacherDuty($dutyId);
        echo '<div class="alert alert-success mt-3">ডিউটি সফলভাবে ডিলিট হয়েছে!</div>';
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-3">ত্রুটি: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

if (isset($_POST['add_teacher_assignment'])) {
    $dutyDate = $_POST['add_duty_date'];
    $teacherId = $_POST['add_teacher_id'];
    $roomNumber = $_POST['add_room_number'];
    $dutyShift = $_POST['add_duty_shift'] ?? 'Morning';
    
    try {
        // Update database structure once to support shift-specific duties
        $teacherManager->updateDutyAssignmentsTableStructure();
        
        // Check if teacher already has duty on this date and shift
        $exists = $teacherManager->checkTeacherDutyExists($teacherId, $dutyDate, $dutyShift);
        
        if (!$exists) {
            $teacherManager->assignTeacherToDuty($teacherId, $dutyDate, $roomNumber, $dutyShift);
            echo '<div class="alert alert-success mt-3">নতুন শিক্ষক সফলভাবে অ্যাসাইন হয়েছে!</div>';
        } else {
            echo '<div class="alert alert-warning mt-3">শিক্ষকের ইতিমধ্যেই একই তারিখ এবং শিফটে ডিউটি রয়েছে!</div>';
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-3">ত্রুটি: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>তারিখ ভিত্তিক ডিউটি বন্টন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .teacher-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .teacher-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .teacher-card.selected {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .teacher-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
        }
        
        .status-normal { background: #e3f2fd; color: #1976d2; }
        .status-always { background: #e8f5e8; color: #2e7d32; }
        .status-never { background: #ffebee; color: #c62828; }
        
        .room-input {
            width: 80px;
        }
        
        .date-selector {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .current-assignments {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        /* Table view font enhancements */
        .table-view table th, .table-view table td {
            font-weight: 600;
            color: #222;
            background: #fff;
            padding: 0.75rem 1rem;
        }
        .table-view table th {
            background: #f3f6fa;
            font-size: 1.05rem;
            letter-spacing: 0.5px;
        }
        .table-view table td {
            font-size: 1.01rem;
        }
        .table-view .teacher-photo {
            border: 2px solid #b3b3b3;
        }
        .table-view input[type="text"], .table-view .form-control {
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-calendar-plus me-3"></i>তারিখ ভিত্তিক ডিউটি বন্টন
                </h1>
                <p class="text-white-50">নির্দিষ্ট তারিখের জন্য শিক্ষকদের ডিউটি বন্টন করুন</p>
            </div>

            <!-- Message Display -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <strong><?php echo $message; ?></strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Date Selector -->
            <div class="date-selector">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4 class="mb-2">
                            <i class="fas fa-calendar-day me-2"></i>তারিখ এবং শিফট নির্বাচন করুন
                        </h4>
                        <p class="mb-0">যে তারিখের জন্য ডিউটি বন্টন করতে চান সেই তারিখ নির্বাচন করুন</p>
                    </div>
                    <div class="col-md-6">
                        <form method="GET" class="row g-2">
                            <div class="col-md-6">
                                <input type="date" name="date" class="form-control" 
                                       value="<?php echo $selectedDate; ?>" required>
                            </div>
                            <div class="col-md-4">
                                <select name="shift" class="form-select">
                                    <option value="all" <?php if ($selectedShift === 'all') echo 'selected'; ?>>সব শিফট</option>
                                    <option value="Morning" <?php if ($selectedShift === 'Morning') echo 'selected'; ?>>সকাল</option>
                                    <option value="Afternoon" <?php if ($selectedShift === 'Afternoon') echo 'selected'; ?>>বিকাল</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-light w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Current Assignments (if any) -->
            <?php if ($selectedDate && !empty($currentAssignments)): ?>
                <div class="current-assignments">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle text-warning me-2"></i>
                            <?php echo formatDateBengali($selectedDate); ?> তারিখে বর্তমান ডিউটি বন্টন
                        </h5>
                        
                        <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#resetDutyModal">
                            <i class="fas fa-trash me-2"></i>এই তারিখের ডিউটি রিসেট করুন
                        </button>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">ডিউটি তালিকা</h5>
                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addTeacherModal">
                            <i class="fas fa-plus"></i> শিক্ষক অ্যাসাইন করুন
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">শিক্ষকের নাম</th>
                                    <th scope="col">বিষয়</th>
                                    <th scope="col">পদবি</th>
                                    <th scope="col">মোবাইল</th>
                                    <th scope="col">রুম নং</th>
                                    <th scope="col">অপরিক্রিত</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 1; foreach ($currentAssignments as $assignment): ?>
                                <tr>
                                    <td><?php echo $i++; ?></td>
                                    <td><?php echo htmlspecialchars($assignment['teacher_name']); ?></td>
                                    <td><?php echo htmlspecialchars($assignment['subject']); ?></td>
                                    <td><?php echo htmlspecialchars($assignment['designation']); ?></td>
                                    <td><?php echo htmlspecialchars($assignment['mobile']); ?></td>
                                    <td>
                                        <?php if (!empty($assignment['room_number'])): ?>
                                            <span class="badge bg-info">রুম: <?php echo $assignment['room_number']; ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">রুম বরাদ্দ নেই</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#editDutyModal_<?php echo $assignment['id']; ?>"><i class="fas fa-pen"></i></button>
                                        <button class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteDutyModal_<?php echo $assignment['id']; ?>"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <!-- Edit Modal -->
                                <div class="modal fade" id="editDutyModal_<?php echo $assignment['id']; ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form method="POST">
                                                <div class="modal-header bg-warning">
                                                    <h5 class="modal-title">ডিউটি এডিট করুন</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <input type="hidden" name="edit_duty_id" value="<?php echo $assignment['id']; ?>">
                                                    <input type="hidden" name="edit_duty_date" value="<?php echo $selectedDate; ?>">
                                                    <div class="mb-2">
                                                        <label>রুম নম্বর</label>
                                                        <input type="text" name="edit_room_number" class="form-control" value="<?php echo htmlspecialchars($assignment['room_number']); ?>">
                                                    </div>
                                                    <div class="mb-2">
                                                        <label>শিক্ষক</label>
                                                        <select name="edit_teacher_id" class="form-select">
                                                            <?php foreach ($teachers as $teacher): ?>
                                                                <option value="<?php echo $teacher['id']; ?>" <?php if($teacher['id']==$assignment['teacher_id']) echo 'selected'; ?>><?php echo htmlspecialchars($teacher['name']); ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                    <button type="submit" name="update_duty_assignment" class="btn btn-warning">আপডেট করুন</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteDutyModal_<?php echo $assignment['id']; ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form method="POST">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title">ডিউটি ডিলিট নিশ্চিত করুন</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <input type="hidden" name="delete_duty_id" value="<?php echo $assignment['id']; ?>">
                                                    <input type="hidden" name="delete_duty_date" value="<?php echo $selectedDate; ?>">
                                                    <p>আপনি কি নিশ্চিতভাবে <strong><?php echo htmlspecialchars($assignment['teacher_name']); ?></strong> কে এই ডিউটি থেকে বাদ দিতে চান?</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                    <button type="submit" name="confirm_delete_duty_assignment" class="btn btn-danger">ডিলিট করুন</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            নতুন ডিউটি বন্টন করলে বর্তমান বন্টন প্রতিস্থাপিত হবে।
                        </small>
                    </div>
                </div>
            <?php elseif ($selectedDate): ?>
                <div class="alert alert-info mb-4">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong><?php echo formatDateBengali($selectedDate); ?></strong> তারিখে কোন ডিউটি বন্টন করা হয়নি।
                            <p class="mb-0 mt-2">নিচে শিক্ষক নির্বাচন করে এই তারিখের জন্য ডিউটি বন্টন করুন।</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Duty Assignment Form -->
            <?php if ($selectedDate): ?>
                <div class="mb-3">
                    <form method="GET" class="row g-2 align-items-center">
                        <input type="hidden" name="date" value="<?php echo htmlspecialchars($selectedDate); ?>">
                        <div class="col-auto">
                            <input type="text" name="search" class="form-control" placeholder="নাম, বিষয়, পদবী, মোবাইল..." value="<?php echo htmlspecialchars($searchTerm); ?>">
                        </div>
                        <div class="col-auto">
                            <button type="submit" class="btn btn-outline-primary"><i class="fas fa-search"></i> খুঁজুন</button>
                        </div>
                        <?php if (!empty($searchTerm)): ?>
                        <div class="col-auto">
                            <a href="?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-outline-secondary"><i class="fas fa-times"></i> রিসেট</a>
                        </div>
                        <?php endif; ?>
                    </form>
                </div>

                <div class="d-flex justify-content-end mb-3">
                    <div class="btn-group view-mode-buttons" role="group">
                        <button type="button" class="btn btn-outline-primary active" id="cardViewBtn" onclick="switchView('card')">
                            <i class="fas fa-th-large me-1"></i> কার্ড ভিউ
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="tableViewBtn" onclick="switchView('table')">
                            <i class="fas fa-table me-1"></i> টেবিল ভিউ
                        </button>
                    </div>
                </div>

                <form method="POST">
                    <input type="hidden" name="duty_date" value="<?php echo $selectedDate; ?>">
                    <!-- Card View -->
                    <div class="row card-view" id="cardView">
                        <?php foreach ($teachers as $teacher): ?>
                            <?php if ($teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়'): ?>
                                <div class="col-lg-6 mb-3">
                                    <div class="teacher-card">
                                        <div class="d-flex align-items-center">
                                            <div class="form-check me-3">
                                                <input class="form-check-input teacher-checkbox" 
                                                       type="checkbox" 
                                                       name="selected_teachers[]" 
                                                       value="<?php echo $teacher['id']; ?>"
                                                       id="teacher_<?php echo $teacher['id']; ?>"
                                                       <?php echo ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') ? 'checked disabled' : ''; ?>
                                                       onchange="toggleRoomInput(this)">
                                            </div>
                                            <div class="me-3">
                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" 
                                                         alt="Teacher Photo" class="teacher-photo">
                                                <?php else: ?>
                                                    <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                        <i class="fas fa-user text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($teacher['subject']); ?> - 
                                                    <?php echo htmlspecialchars($teacher['designation']); ?>
                                                </small><br>
                                                <small class="text-muted">
                                                    <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($teacher['mobile']); ?>
                                                </small>
                                                <?php
                                                $statusClass = $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'status-always' : 
                                                              ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'status-never' : 'status-normal');
                                                ?>
                                                <span class="status-badge <?php echo $statusClass; ?> ms-2">
                                                    <?php echo $teacher['duty_status']; ?>
                                                </span>
                                            </div>
                                            <div class="ms-3">
                                                <label class="form-label small">রুম:</label>
                                                <input type="text" 
                                                       name="room_assignments[<?php echo $teacher['id']; ?>]" 
                                                       class="form-control room-input" 
                                                       placeholder="রুম নং"
                                                       id="room_<?php echo $teacher['id']; ?>"
                                                       disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত'): ?>
                                    <input type="hidden" name="selected_teachers[]" value="<?php echo $teacher['id']; ?>">
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                    <!-- Table View -->
                    <div class="table-responsive table-view" id="tableView" style="display:none;">
                        <table class="table table-bordered align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th style="width:40px;"></th>
                                    <th>ছবি</th>
                                    <th>নাম</th>
                                    <th>বিষয়</th>
                                    <th>পদবী</th>
                                    <th>মোবাইল</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>রুম</th>
                                    <th>অপরিক্রিত</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($teachers as $teacher): ?>
                                    <?php if ($teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়'): ?>
                                    <tr>
                                        <td>
                                            <input class="form-check-input teacher-checkbox" type="checkbox" name="selected_teachers[]" value="<?php echo $teacher['id']; ?>" id="table_teacher_<?php echo $teacher['id']; ?>" <?php echo ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') ? 'checked disabled' : ''; ?> onchange="toggleRoomInput(this)">
                                            <?php if ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত'): ?>
                                                <input type="hidden" name="selected_teachers[]" value="<?php echo $teacher['id']; ?>">
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="Teacher Photo" class="teacher-photo">
                                            <?php else: ?>
                                                <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['mobile']); ?></td>
                                        <td>
                                            <?php
                                            $statusClass = $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'status-always' : 
                                                          ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'status-never' : 'status-normal');
                                            ?>
                                            <span class="status-badge <?php echo $statusClass; ?> ms-2">
                                                <?php echo $teacher['duty_status']; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <input type="text" name="room_assignments[<?php echo $teacher['id']; ?>]" class="form-control room-input" placeholder="রুম নং" id="room_<?php echo $teacher['id']; ?>" disabled>
                                        </td>
                                        <td>
                                            <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#editDutyModal_<?php echo $teacher['id']; ?>"><i class="fas fa-pen"></i></button>
                                            <button class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deleteDutyModal_<?php echo $teacher['id']; ?>"><i class="fas fa-trash"></i></button>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-4">
                        <div class="row justify-content-center align-items-center mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="duty_shift" class="form-label">ডিউটি শিফট</label>
                                    <select name="duty_shift" id="duty_shift" class="form-select">
                                        <option value="Morning">সকাল</option>
                                        <option value="Afternoon">বিকাল</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <button type="submit" name="assign_duty" class="btn btn-custom">
                            <i class="fas fa-save me-2"></i>ডিউটি বন্টন করুন
                        </button>
                        <a href="duty_assignments_list.php" class="btn btn-outline-secondary ms-2">
                            <i class="fas fa-list me-2"></i>ডিউটি তালিকা দেখুন
                        </a>
                        <?php if (!empty($currentAssignments)): ?>
                        <button type="button" class="btn btn-outline-danger ms-2" data-bs-toggle="modal" data-bs-target="#resetDutyModal">
                            <i class="fas fa-trash me-2"></i>এই তারিখের ডিউটি রিসেট করুন
                        </button>
                        <?php endif; ?>
                    </div>
                </form>
            <?php else: ?>
                <div class="main-card">
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-day fa-4x text-muted mb-3"></i>
                        <h4>তারিখ নির্বাচন করুন</h4>
                        <p class="text-muted">ডিউটি বন্টন করার জন্য উপরে থেকে একটি তারিখ নির্বাচন করুন।</p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Reset Duty Confirmation Modal -->
            <?php if ($selectedDate): ?>
            <div class="modal fade" id="resetDutyModal" tabindex="-1" aria-labelledby="resetDutyModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="resetDutyModalLabel">
                                <i class="fas fa-exclamation-triangle me-2"></i>ডিউটি রিসেট নিশ্চিত করুন
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <p class="mb-0">
                                    <strong>সাবধান:</strong> আপনি কি নিশ্চিত যে আপনি 
                                    <strong><?php echo formatDateBengali($selectedDate); ?></strong> 
                                    তারিখের নির্বাচিত শিফ্টের ডিউটি বন্টন রিসেট করতে চান?
                                </p>
                                <p class="mt-2 mb-0">
                                    <i class="fas fa-info-circle me-1"></i>
                                    এই অপারেশন অপরিবর্তনীয়। এটি করলে এই তারিখের নির্বাচিত শিফ্টের সকল শিক্ষকের ডিউটি বন্টন মুছে যাবে।
                                </p>
                            </div>
                            
                            <form method="POST">
                                <input type="hidden" name="duty_date" value="<?php echo $selectedDate; ?>">
                                <div class="mb-3">
                                    <label for="reset_duty_shift" class="form-label">শিফট নির্বাচন করুন</label>
                                    <select class="form-select" name="duty_shift" id="reset_duty_shift">
                                        <option value="all">সব শিফট</option>
                                        <option value="Morning">সকাল</option>
                                        <option value="Afternoon">বিকাল</option>
                                    </select>
                                </div>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                    <button type="submit" name="reset_duty" class="btn btn-danger">
                                        <i class="fas fa-trash me-2"></i>রিসেট নিশ্চিত করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Add Teacher Modal -->
            <?php if ($selectedDate): ?>
            <div class="modal fade" id="addTeacherModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <form method="POST">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title">নতুন শিক্ষক অ্যাসাইন করুন</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <input type="hidden" name="add_duty_date" value="<?php echo $selectedDate; ?>">
                                <div class="mb-2">
                                    <label>শিক্ষক</label>
                                    <select name="add_teacher_id" class="form-select" required>
                                        <option value="">শিক্ষক নির্বাচন করুন</option>
                                        <?php foreach ($teachers as $teacher): ?>
                                            <option value="<?php echo $teacher['id']; ?>"><?php echo htmlspecialchars($teacher['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label>শিফট</label>
                                    <select name="add_duty_shift" class="form-select" required>
                                        <option value="Morning">সকাল</option>
                                        <option value="Afternoon">বিকাল</option>
                                    </select>
                                </div>
                                <div class="mb-2">
                                    <label>রুম নম্বর</label>
                                    <input type="text" name="add_room_number" class="form-control" placeholder="রুম নম্বর">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                <button type="submit" name="add_teacher_assignment" class="btn btn-success">অ্যাসাইন করুন</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle room input enabled state based on teacher checkbox
        function toggleRoomInput(checkbox) {
            const teacherId = checkbox.value;
            // Find all room inputs for this teacher (in case both views are present)
            const roomInputs = document.querySelectorAll('#room_' + teacherId);
            // Find all teacher cards (for card view highlight)
            const teacherCards = document.querySelectorAll('.teacher-card input[value="' + teacherId + '"]');
            roomInputs.forEach(function(roomInput) {
                if (checkbox.checked) {
                    roomInput.disabled = false;
                } else {
                    roomInput.disabled = true;
                    roomInput.value = '';
                }
            });
            // Card view highlight
            teacherCards.forEach(function(input) {
                const card = input.closest('.teacher-card');
                if (card) {
                    if (checkbox.checked) {
                        card.classList.add('selected');
                    } else {
                        card.classList.remove('selected');
                    }
                }
            });
        }
        
        // Select all checkboxes
        function selectAll() {
            document.querySelectorAll('.teacher-checkbox:not([disabled])').forEach(function(checkbox) {
                checkbox.checked = true;
                toggleRoomInput(checkbox);
            });
        }
        
        // Clear all checkboxes
        function clearAll() {
            document.querySelectorAll('.teacher-checkbox:not([disabled])').forEach(function(checkbox) {
                checkbox.checked = false;
                toggleRoomInput(checkbox);
            });
        }
        
        // Initialize all checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.teacher-checkbox').forEach(function(checkbox) {
                toggleRoomInput(checkbox);
            });
            
            // Fade out alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert-dismissible');
            if (alerts.length) {
                setTimeout(function() {
                    alerts.forEach(function(alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    });
                }, 5000);
            }
        });

        function switchView(view) {
            const cardView = document.getElementById('cardView');
            const tableView = document.getElementById('tableView');
            const cardBtn = document.getElementById('cardViewBtn');
            const tableBtn = document.getElementById('tableViewBtn');
            if (view === 'card') {
                cardView.style.display = '';
                tableView.style.display = 'none';
                cardBtn.classList.add('active');
                tableBtn.classList.remove('active');
            } else {
                cardView.style.display = 'none';
                tableView.style.display = '';
                cardBtn.classList.remove('active');
                tableBtn.classList.add('active');
            }
        }
    </script>
</body>
</html>
