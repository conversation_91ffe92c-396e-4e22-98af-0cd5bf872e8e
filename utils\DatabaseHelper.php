<?php
/**
 * Database Helper Utility
 * Provides helper functions for database operations and structure checking
 */

class DatabaseHelper {
    private $db;
    
    public function __construct($database) {
        $this->db = $database;
    }
    
    /**
     * Check if old subject columns (sub_1 to sub_13) exist in students table
     */
    public function hasOldSubjectColumns() {
        try {
            $stmt = $this->db->query("SHOW COLUMNS FROM students LIKE 'sub_1'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Check if new subjects field exists in students table
     */
    public function hasNewSubjectsField() {
        try {
            $stmt = $this->db->query("SHOW COLUMNS FROM students LIKE 'subjects'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get all column names from students table
     */
    public function getStudentsTableColumns() {
        try {
            $stmt = $this->db->query("DESCRIBE students");
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * Build subject filter SQL based on available columns
     */
    public function buildSubjectFilterSQL($subjectCode, $tableName = 'students') {
        $conditions = [];
        $params = [];
        
        // Check for new subjects field
        if ($this->hasNewSubjectsField()) {
            $conditions[] = "subjects LIKE :subject_pattern";
            $params['subject_pattern'] = '%' . $subjectCode . '%';
        }
        
        // Check for old subject columns
        if ($this->hasOldSubjectColumns()) {
            for ($i = 1; $i <= 13; $i++) {
                $conditions[] = "sub_$i = :subject_code_$i";
                $params["subject_code_$i"] = $subjectCode;
            }
        }
        
        if (empty($conditions)) {
            throw new Exception("No subject columns found in database");
        }
        
        $sql = "SELECT * FROM $tableName WHERE (" . implode(' OR ', $conditions) . ")";
        
        return ['sql' => $sql, 'params' => $params];
    }
    
    /**
     * Extract subjects from student data (handles both old and new formats)
     */
    public function extractSubjects($studentData) {
        $subjects = [];
        
        // Try new format first
        if (!empty($studentData['subjects'])) {
            $subjectArray = explode(',', $studentData['subjects']);
            $subjects = array_map('trim', $subjectArray);
            $subjects = array_filter($subjects);
        } 
        // Try old format
        elseif ($this->hasOldSubjectColumns() && isset($studentData['sub_1'])) {
            for ($i = 1; $i <= 13; $i++) {
                $subjectValue = $studentData["sub_$i"] ?? '';
                if (!empty($subjectValue)) {
                    $subjects[] = $subjectValue;
                }
            }
        }
        
        return $subjects;
    }
    
    /**
     * Check if a student has a specific subject
     */
    public function studentHasSubject($studentData, $subjectCode) {
        $subjects = $this->extractSubjects($studentData);
        return in_array($subjectCode, $subjects);
    }
    
    /**
     * Get standardized field value (handles different field names)
     */
    public function getFieldValue($data, $fieldMappings) {
        foreach ($fieldMappings as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                return $data[$field];
            }
        }
        return '';
    }
    
    /**
     * Get roll number from student data (handles different field names)
     */
    public function getRollNumber($studentData) {
        return $this->getFieldValue($studentData, ['roll_no', 'roll']);
    }
    
    /**
     * Get student name from student data (handles different field names)
     */
    public function getStudentName($studentData) {
        return $this->getFieldValue($studentData, ['student_name', 'name']);
    }
    
    /**
     * Get registration number from student data (handles different field names)
     */
    public function getRegistrationNumber($studentData) {
        return $this->getFieldValue($studentData, ['reg_no', 'registration']);
    }
    
    /**
     * Get department/group from student data (handles different field names)
     */
    public function getDepartment($studentData) {
        return $this->getFieldValue($studentData, ['group_name', 'department']);
    }
    
    /**
     * Get student type from student data (handles different field names)
     */
    public function getStudentType($studentData) {
        return $this->getFieldValue($studentData, ['type', 'student_type']);
    }
    
    /**
     * Get academic year from student data (handles different field names)
     */
    public function getAcademicYear($studentData) {
        return $this->getFieldValue($studentData, ['session', 'academic_year']);
    }
    
    /**
     * Get the correct roll column name for ORDER BY
     */
    public function getRollColumnName() {
        $columns = $this->getStudentsTableColumns();
        if (in_array('roll_no', $columns)) {
            return 'roll_no';
        } elseif (in_array('roll', $columns)) {
            return 'roll';
        } else {
            return 'id'; // Fallback to id if no roll column found
        }
    }

    /**
     * Build ORDER BY clause for roll numbers
     */
    public function buildRollOrderBy() {
        $columns = $this->getStudentsTableColumns();

        // Check which roll column exists and use only that one
        if (in_array('roll_no', $columns)) {
            return "ORDER BY CAST(roll_no AS UNSIGNED) ASC";
        } elseif (in_array('roll', $columns)) {
            return "ORDER BY CAST(roll AS UNSIGNED) ASC";
        } else {
            return "ORDER BY id ASC"; // Fallback to id
        }
    }

    /**
     * Generate debug information about database structure
     */
    public function getDebugInfo() {
        $info = [
            'has_old_columns' => $this->hasOldSubjectColumns(),
            'has_new_subjects_field' => $this->hasNewSubjectsField(),
            'table_columns' => $this->getStudentsTableColumns(),
            'roll_column' => $this->getRollColumnName(),
            'timestamp' => date('Y-m-d H:i:s')
        ];

        return $info;
    }
}
?>
