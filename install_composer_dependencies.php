<?php
/**
 * Composer Dependencies Installer
 * This script helps install Composer dependencies for Excel upload functionality
 */

$composerInstalled = file_exists(__DIR__ . '/vendor/autoload.php');
$composerExists = file_exists(__DIR__ . '/composer.json');

?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Dependencies - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3><i class="fas fa-download"></i> Install Composer Dependencies</h3>
                        <p class="mb-0">Enable Excel Upload Functionality</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Status Check -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="alert <?php echo $composerExists ? 'alert-success' : 'alert-danger'; ?>">
                                    <i class="fas <?php echo $composerExists ? 'fa-check' : 'fa-times'; ?>"></i>
                                    <strong>composer.json:</strong> <?php echo $composerExists ? 'Found' : 'Missing'; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert <?php echo $composerInstalled ? 'alert-success' : 'alert-warning'; ?>">
                                    <i class="fas <?php echo $composerInstalled ? 'fa-check' : 'fa-exclamation-triangle'; ?>"></i>
                                    <strong>Dependencies:</strong> <?php echo $composerInstalled ? 'Installed' : 'Not Installed'; ?>
                                </div>
                            </div>
                        </div>

                        <?php if ($composerInstalled): ?>
                            <!-- Already Installed -->
                            <div class="alert alert-success text-center">
                                <h4><i class="fas fa-check-circle"></i> Dependencies Already Installed!</h4>
                                <p>Excel upload functionality is now available.</p>
                                <a href="upload.php" class="btn btn-success btn-lg">
                                    <i class="fas fa-upload"></i> Go to Upload Page
                                </a>
                            </div>
                        <?php else: ?>
                            <!-- Auto Installation Option -->
                            <div class="alert alert-primary text-center mb-4">
                                <h5><i class="fas fa-magic"></i> Quick Installation</h5>
                                <p>Try our automatic installer for one-click setup!</p>
                                <a href="auto_install_composer.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic"></i> Auto Install Composer
                                </a>
                                <hr>
                                <small class="text-muted">Or follow the manual instructions below</small>
                            </div>
                            <!-- Installation Instructions -->
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> Installation Required</h5>
                                <p>To enable Excel upload functionality, you need to install Composer dependencies.</p>
                            </div>

                            <h5><i class="fas fa-list-ol"></i> Installation Steps:</h5>

                            <div class="step">
                                <h6><strong>Step 1:</strong> Install Composer (if not already installed)</h6>
                                <p>Download and install Composer from <a href="https://getcomposer.org/download/" target="_blank">https://getcomposer.org/download/</a></p>
                                <p><strong>For Windows:</strong> Download and run the installer</p>
                                <p><strong>For Linux/Mac:</strong> Use the command line installer</p>
                            </div>

                            <div class="step">
                                <h6><strong>Step 2:</strong> Open Command Prompt/Terminal</h6>
                                <p>Navigate to your project directory:</p>
                                <div class="code-block">
                                    cd <?php echo __DIR__; ?>
                                </div>
                            </div>

                            <div class="step">
                                <h6><strong>Step 3:</strong> Install Dependencies</h6>
                                <p>Run the following command:</p>
                                <div class="code-block">
                                    composer install
                                </div>
                                <p class="text-muted mt-2">This will download and install PhpSpreadsheet library for Excel processing.</p>
                            </div>

                            <div class="step">
                                <h6><strong>Step 4:</strong> Verify Installation</h6>
                                <p>After installation, refresh this page to verify that dependencies are installed correctly.</p>
                                <button onclick="location.reload()" class="btn btn-primary">
                                    <i class="fas fa-sync-alt"></i> Refresh Page
                                </button>
                            </div>

                            <!-- Alternative Options -->
                            <div class="alert alert-secondary mt-4">
                                <h6><i class="fas fa-lightbulb"></i> Alternative Options:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Use CSV Upload:</strong></p>
                                        <p>You can upload CSV files without installing dependencies.</p>
                                        <a href="upload.php" class="btn btn-outline-primary">
                                            <i class="fas fa-file-csv"></i> Upload CSV
                                        </a>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Manual Entry:</strong></p>
                                        <p>Add students one by one using the form.</p>
                                        <a href="add_student.php" class="btn btn-outline-success">
                                            <i class="fas fa-plus"></i> Add Student
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Troubleshooting -->
                        <div class="mt-4">
                            <h6><i class="fas fa-question-circle"></i> Troubleshooting:</h6>
                            <div class="accordion" id="troubleshootingAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                            Composer command not found
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p>If you get "composer command not found" error:</p>
                                            <ul>
                                                <li>Make sure Composer is installed correctly</li>
                                                <li>Add Composer to your system PATH</li>
                                                <li>Try using <code>php composer.phar install</code> instead</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                            Permission denied errors
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootingAccordion">
                                        <div class="accordion-body">
                                            <p>If you get permission errors:</p>
                                            <ul>
                                                <li>Run command prompt as Administrator (Windows)</li>
                                                <li>Use <code>sudo</code> prefix on Linux/Mac</li>
                                                <li>Check folder permissions</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-home"></i> Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
