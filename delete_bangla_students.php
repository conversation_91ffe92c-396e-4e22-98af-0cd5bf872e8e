<?php
require_once 'models/Student.php';

$message = '';
$messageType = '';
$banglaStudents = [];

// Get Bengali students before deletion for confirmation
$student = new Student();
$allStudents = $student->getAll();

// Filter students who have code 101 (Bangla) in any sub_1 to sub_13
foreach ($allStudents as $s) {
    $hasCode101 = false;
    for ($i = 1; $i <= 13; $i++) {
        if (trim($s["sub_$i"]) === '101') {
            if (!$hasCode101) {
                $banglaStudents[] = $s;
                $hasCode101 = true; // Count each student only once
            }
        }
    }
}

$totalBanglaStudents = count($banglaStudents);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
        try {
            // Delete students with Bengali subject code 101
            if ($student->deleteBySubjectCode('101')) {
                $message = "সফলভাবে {$totalBanglaStudents} জন বাংলা বিষয়ের শিক্ষার্থী মুছে ফেলা হয়েছে!";
                $messageType = 'success';
                $banglaStudents = []; // Clear the list after deletion
                $totalBanglaStudents = 0;
            } else {
                $message = 'বাংলা বিষয়ের শিক্ষার্থী মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বাংলা বিষয়ের শিক্ষার্থী মুছুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .danger-card {
            border: 2px solid #dc3545;
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
        }
        .warning-text {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card danger-card">
                        <div class="card-header bg-danger text-white text-center">
                            <h1><i class="fas fa-exclamation-triangle"></i> বাংলা বিষয়ের শিক্ষার্থী মুছুন</h1>
                            <h3>বিষয় কোড: ১০১</h3>
                            <p class="mb-0">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ, দামুড়হুদা, চুয়াডাঙ্গা</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Statistics and Warning -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h2><?php echo $totalBanglaStudents; ?></h2>
                            <p class="mb-0"><strong>বাংলা বিষয়ের শিক্ষার্থী পাওয়া গেছে</strong></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h4><i class="fas fa-exclamation-triangle"></i></h4>
                            <p class="mb-0"><strong>সতর্কতা: এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!</strong></p>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($totalBanglaStudents > 0): ?>
                <!-- Students List Preview -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h5><i class="fas fa-list"></i> মুছে ফেলার জন্য নির্ধারিত শিক্ষার্থীদের তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark sticky-top">
                                            <tr>
                                                <th>ক্রমিক</th>
                                                <th>রোল নং</th>
                                                <th>রেজি. নং</th>
                                                <th>শিক্ষার্থীর নাম</th>
                                                <th>পিতার নাম</th>
                                                <th>গ্রুপ</th>
                                                <th>সেশন</th>
                                                <th>বাংলা কোড অবস্থান</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($banglaStudents as $index => $s): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td><strong><?php echo htmlspecialchars($s['roll_no']); ?></strong></td>
                                                    <td><?php echo htmlspecialchars($s['reg_no']); ?></td>
                                                    <td><?php echo htmlspecialchars($s['student_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($s['father_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($s['group_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($s['session']); ?></td>
                                                    <td>
                                                        <?php
                                                        $positions = [];
                                                        for ($i = 1; $i <= 13; $i++) {
                                                            if (trim($s["sub_$i"]) === '101') {
                                                                $positions[] = "Sub_$i";
                                                            }
                                                        }
                                                        echo implode(', ', $positions);
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Confirmation Form -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card danger-card">
                            <div class="card-header bg-danger text-white">
                                <h5><i class="fas fa-exclamation-triangle"></i> নিশ্চিতকরণ প্রয়োজন</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-danger">
                                    <h6><strong>সতর্কতা:</strong></h6>
                                    <ul class="mb-0">
                                        <li>এই কাজটি সম্পন্ন হলে <strong><?php echo $totalBanglaStudents; ?> জন</strong> বাংলা বিষয়ের শিক্ষার্থী স্থায়ীভাবে মুছে যাবে</li>
                                        <li>এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না</li>
                                        <li>সংশ্লিষ্ট সকল ডেটা হারিয়ে যাবে</li>
                                    </ul>
                                </div>

                                <form method="POST" onsubmit="return confirmDeletion()">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="confirmCheckbox" required>
                                        <label class="form-check-label warning-text" for="confirmCheckbox">
                                            আমি নিশ্চিত যে আমি সকল বাংলা বিষয়ের শিক্ষার্থী মুছে ফেলতে চাই এবং এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                                        </label>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" name="confirm_delete" value="yes" class="btn btn-danger btn-lg me-3">
                                            <i class="fas fa-trash"></i> হ্যাঁ, সব মুছে ফেলুন
                                        </button>
                                        <a href="view_students.php" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times"></i> বাতিল করুন
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- No Students Found -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> কোন বাংলা বিষয়ের শিক্ষার্থী পাওয়া যায়নি</h5>
                                    <p>বর্তমানে কোন শিক্ষার্থী বাংলা বিষয় (কোড ১০১) নিয়ে নেই।</p>
                                    <a href="view_students.php" class="btn btn-primary">
                                        <i class="fas fa-users"></i> সব শিক্ষার্থী দেখুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5><i class="fas fa-navigation"></i> নেভিগেশন</h5>
                        </div>
                        <div class="card-body text-center">
                            <a href="view_students.php" class="btn btn-primary me-2">
                                <i class="fas fa-users"></i> সব শিক্ষার্থী
                            </a>
                            <a href="subject_filter.php" class="btn btn-info me-2">
                                <i class="fas fa-filter"></i> বিষয় ফিল্টার
                            </a>
                            <a href="index.php" class="btn btn-success">
                                <i class="fas fa-home"></i> হোম পেজ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDeletion() {
            return confirm('আপনি কি সত্যিই সকল বাংলা বিষয়ের শিক্ষার্থী মুছে ফেলতে চান? এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।');
        }
    </script>
</body>
</html>
