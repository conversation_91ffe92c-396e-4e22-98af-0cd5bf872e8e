<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Get selected date and teachers
$selectedDate = $_GET['date'] ?? '';
$dutyAssignments = [];

if ($selectedDate) {
    $dutyAssignments = $teacherManager->getDutyAssignments($selectedDate);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক আইডি কার্ড জেনারেটর - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .date-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .date-card:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .teacher-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .teacher-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        
        .id-card-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }
        
        .id-card-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
            opacity: 0.1;
        }
        
        .card-options {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-id-card me-3"></i>শিক্ষক আইডি কার্ড জেনারেটর
                    </h1>
                    <p class="text-white-50">পরীক্ষার তত্ত্বাবধায়ক আইডি কার্ড তৈরি করুন</p>
                </div>

                <!-- Navigation -->
                <div class="text-center mb-4">
                    <a href="index.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-home me-2"></i>হোম
                    </a>
                    <a href="teacher_duty_management.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনা
                    </a>
                    <a href="duty_letter_generator.php" class="btn btn-outline-light">
                        <i class="fas fa-file-alt me-2"></i>ডিউটি লেটার
                    </a>
                </div>

                <div class="row">
                    <!-- Date Selection -->
                    <div class="col-lg-4">
                        <div class="main-card">
                            <h4 class="mb-4">
                                <i class="fas fa-calendar text-primary me-2"></i>তারিখ নির্বাচন
                            </h4>
                            
                            <?php if (empty($dutyDates)): ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    কোন ডিউটি বন্টন পাওয়া যায়নি!
                                    <br><br>
                                    <a href="teacher_duty_management.php" class="btn btn-warning btn-sm">
                                        <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                    </a>
                                </div>
                            <?php else: ?>
                                <?php foreach ($dutyDates as $date): ?>
                                    <div class="date-card <?php echo $selectedDate === $date ? 'border-primary bg-primary text-white' : ''; ?>">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">
                                                    <?php echo date('d F Y', strtotime($date)); ?>
                                                </h6>
                                                <small class="<?php echo $selectedDate === $date ? 'text-white-50' : 'text-muted'; ?>">
                                                    <?php 
                                                    $count = count($teacherManager->getDutyAssignments($date));
                                                    echo $count . ' জন শিক্ষক';
                                                    ?>
                                                </small>
                                            </div>
                                            <div>
                                                <a href="?date=<?php echo urlencode($date); ?>" 
                                                   class="btn <?php echo $selectedDate === $date ? 'btn-light' : 'btn-primary'; ?> btn-sm">
                                                    <?php echo $selectedDate === $date ? 'নির্বাচিত' : 'নির্বাচন'; ?>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- ID Card Generation -->
                    <div class="col-lg-8">
                        <div class="main-card">
                            <?php if ($selectedDate && !empty($dutyAssignments)): ?>
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h4>
                                        <i class="fas fa-id-card text-success me-2"></i>
                                        আইডি কার্ড - <?php echo date('d F Y', strtotime($selectedDate)); ?>
                                    </h4>
                                    <div>
                                        <a href="print_teacher_id_cards.php?date=<?php echo urlencode($selectedDate); ?>" 
                                           target="_blank" class="btn btn-success">
                                            <i class="fas fa-print me-2"></i>সব কার্ড প্রিন্ট করুন
                                        </a>
                                    </div>
                                </div>

                                <!-- Card Options -->
                                <div class="card-options">
                                    <h5 class="mb-3">
                                        <i class="fas fa-cog me-2"></i>কার্ড অপশন
                                    </h5>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includePhoto" checked>
                                                <label class="form-check-label" for="includePhoto">
                                                    ছবি অন্তর্ভুক্ত করুন
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeLogo" checked>
                                                <label class="form-check-label" for="includeLogo">
                                                    কলেজ লোগো অন্তর্ভুক্ত করুন
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeRoom" checked>
                                                <label class="form-check-label" for="includeRoom">
                                                    রুম নম্বর দেখান
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- ID Card Preview -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="id-card-preview">
                                            <div class="text-center">
                                                <h6 class="mb-2">HSC পরীক্ষা-২০২৫</h6>
                                                <h5 class="mb-3">তত্ত্বাবধায়ক আইডি কার্ড</h5>
                                                <div class="d-flex align-items-center justify-content-center mb-3">
                                                    <div class="me-3">
                                                        <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                            <i class="fas fa-user fa-2x"></i>
                                                        </div>
                                                    </div>
                                                    <div class="text-start">
                                                        <h6 class="mb-1">নমুনা শিক্ষক</h6>
                                                        <small>সহকারী অধ্যাপক</small><br>
                                                        <small>বাংলা বিভাগ</small>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <small>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</small><br>
                                                    <small>দামুড়হুদা, চুয়াডাঙ্গা</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="alert alert-info">
                                            <h6><i class="fas fa-info-circle me-2"></i>কার্ডের বৈশিষ্ট্য</h6>
                                            <ul class="mb-0 small">
                                                <li>প্রতি পৃষ্ঠায় ৪টি কার্ড (২×২ লেআউট)</li>
                                                <li>শিক্ষকের ছবি ও তথ্য সহ</li>
                                                <li>কলেজ লোগো ও নাম</li>
                                                <li>পরীক্ষার তারিখ ও রুম নম্বর</li>
                                                <li>প্রিন্ট অপ্টিমাইজড ডিজাইন</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Teacher List -->
                                <h5 class="mb-3">
                                    <i class="fas fa-users me-2"></i>শিক্ষকগণ (<?php echo count($dutyAssignments); ?> জন)
                                </h5>
                                
                                <div class="row">
                                    <?php foreach ($dutyAssignments as $index => $assignment): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="teacher-card">
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        <?php
                                                        // Get teacher details to check for photo
                                                        $teacher = $teacherManager->getTeacherById($assignment['teacher_id']);
                                                        if (!empty($teacher['photo']) && file_exists($teacher['photo'])):
                                                        ?>
                                                            <img src="<?php echo htmlspecialchars($teacher['photo']); ?>"
                                                                 alt="Teacher Photo" class="teacher-photo">
                                                        <?php else: ?>
                                                            <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                                <i class="fas fa-user text-muted"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($assignment['name']); ?></h6>
                                                        <small class="text-muted"><?php echo htmlspecialchars($assignment['designation']); ?></small><br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($assignment['subject']); ?></small>
                                                        <?php if ($assignment['room_number']): ?>
                                                            <br><span class="badge bg-info">রুম: <?php echo htmlspecialchars($assignment['room_number']); ?></span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div>
                                                        <a href="print_teacher_id_cards.php?date=<?php echo urlencode($selectedDate); ?>&teacher_id=<?php echo $assignment['teacher_id']; ?>" 
                                                           target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-print me-1"></i>প্রিন্ট
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                            <?php elseif ($selectedDate): ?>
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                                    <h5>এই তারিখে কোন ডিউটি বন্টন পাওয়া যায়নি!</h5>
                                    <p>প্রথমে শিক্ষকদের ডিউটি বন্টন করুন।</p>
                                    <a href="teacher_duty_management.php" class="btn btn-warning">
                                        <i class="fas fa-plus me-2"></i>ডিউটি বন্টন করুন
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info text-center">
                                    <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                                    <h5>তারিখ নির্বাচন করুন</h5>
                                    <p>আইডি কার্ড তৈরি করতে বাম পাশ থেকে একটি তারিখ নির্বাচন করুন।</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
