<?php
require_once __DIR__ . '/models/Student.php';

$student = new Student();
$students = $student->getAll();

// Handle search
$searchResults = null;
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchCriteria = [
        'roll' => $_GET['roll_no'] ?? '',
        'name' => $_GET['student_name'] ?? '',
        'academic_year' => $_GET['session'] ?? ''
    ];
    $searchResults = $student->search($searchCriteria);
}

$displayStudents = $searchResults ?? $students;

// Handle success/error messages
$message = '';
$messageType = '';

if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'deleted':
            $message = 'Student deleted successfully!';
            $messageType = 'success';
            break;
        case 'updated':
            $message = 'Student updated successfully!';
            $messageType = 'success';
            break;
        case 'bulk_deleted':
            $count = $_GET['count'] ?? 0;
            $message = "সফলভাবে $count জন student মুছে ফেলা হয়েছে!";
            $messageType = 'success';
            break;
    }
}

if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'no_id':
            $message = 'No student ID provided!';
            $messageType = 'danger';
            break;
        case 'not_found':
            $message = 'Student not found!';
            $messageType = 'danger';
            break;
        case 'no_selection':
            $message = 'কোনো student নির্বাচন করা হয়নি। অনুগ্রহ করে কমপক্ষে একজন student নির্বাচন করুন।';
            $messageType = 'warning';
            break;
        case 'invalid_selection':
            $message = 'অবৈধ student selection। অনুগ্রহ করে আবার চেষ্টা করুন।';
            $messageType = 'danger';
            break;
        case 'students_not_found':
            $message = 'নির্বাচিত students পাওয়া যায়নি। তারা ইতিমধ্যে মুছে ফেলা হতে পারে।';
            $messageType = 'warning';
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Students - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .table-responsive {
            font-size: 0.9rem;
        }
        .search-form {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3>Student Records</h3>
                        <div>
                            <a href="upload.php" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Upload Excel
                            </a>
                            <a href="export_students.php" class="btn btn-success">
                                <i class="fas fa-download"></i> Export Excel
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Search Form -->
                        <div class="search-form">
                            <form method="GET" class="row g-3">
                                <div class="col-md-3">
                                    <label for="roll_no" class="form-label">Roll No.</label>
                                    <input type="text" class="form-control" id="roll_no" name="roll_no" 
                                           value="<?php echo htmlspecialchars($_GET['roll_no'] ?? ''); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="student_name" class="form-label">Student Name</label>
                                    <input type="text" class="form-control" id="student_name" name="student_name" 
                                           value="<?php echo htmlspecialchars($_GET['student_name'] ?? ''); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="session" class="form-label">Session</label>
                                    <input type="text" class="form-control" id="session" name="session" 
                                           value="<?php echo htmlspecialchars($_GET['session'] ?? ''); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid gap-2">
                                        <button type="submit" name="search" class="btn btn-outline-primary">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                        <a href="view_students.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-refresh"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Results Summary -->
                        <div class="mb-3 d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-info">
                                    Total Records: <?php echo count($displayStudents); ?>
                                </span>
                                <?php if ($searchResults !== null): ?>
                                    <span class="badge bg-warning">Search Results</span>
                                <?php endif; ?>
                            </div>
                            <div>
                                <a href="add_student.php" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus"></i> Add Student
                                </a>
                                <a href="subject_codes_dashboard.php" class="btn btn-warning btn-sm ms-2">
                                    <i class="fas fa-chart-bar"></i> Subject Codes
                                </a>
                                <?php if (count($displayStudents) > 0): ?>
                                    <a href="delete_all_records.php" class="btn btn-danger btn-sm ms-2">
                                        <i class="fas fa-trash"></i> Delete All
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Bulk Operations Controls -->
                        <?php if (count($displayStudents) > 0): ?>
                            <div id="bulkControls" class="alert alert-info" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-check-square"></i>
                                        <strong><span id="selectedCount">0</span> জন student নির্বাচিত</strong>
                                    </div>
                                    <div>
                                        <button type="button" id="bulkDeleteBtn" class="btn btn-danger btn-sm" disabled>
                                            <i class="fas fa-trash"></i> নির্বাচিত Students মুছুন
                                        </button>
                                        <button type="button" id="clearSelectionBtn" class="btn btn-outline-secondary btn-sm ms-2">
                                            <i class="fas fa-times"></i> Selection Clear করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Students Table -->
                        <div class="table-responsive">
                            <table id="studentsTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <?php if (count($displayStudents) > 0): ?>
                                            <th style="width: 40px;">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAll"
                                                           title="Select All Students">
                                                    <label class="form-check-label" for="selectAll">
                                                        <small>All</small>
                                                    </label>
                                                </div>
                                            </th>
                                        <?php endif; ?>
                                        <th>ID</th>
                                        <th>C.Code</th>
                                        <th>EIIN</th>
                                        <th>Roll No.</th>
                                        <th>Reg. No.</th>
                                        <th>Session</th>
                                        <th>Type</th>
                                        <th>Group</th>
                                        <th>Student Name</th>
                                        <th>Father Name</th>
                                        <th>Gender</th>
                                        <th>Sub 1</th>
                                        <th>Sub 2</th>
                                        <th>Sub 3</th>
                                        <th>Sub 4</th>
                                        <th>Sub 5</th>
                                        <th>Sub 6</th>
                                        <th>Sub 7</th>
                                        <th>Sub 8</th>
                                        <th>Sub 9</th>
                                        <th>Sub 10</th>
                                        <th>Sub 11</th>
                                        <th>Sub 12</th>
                                        <th>Sub 13</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($displayStudents as $s): ?>
                                        <tr>
                                            <?php if (count($displayStudents) > 0): ?>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input student-checkbox"
                                                               type="checkbox"
                                                               value="<?php echo $s['id']; ?>"
                                                               id="student_<?php echo $s['id']; ?>">
                                                    </div>
                                                </td>
                                            <?php endif; ?>
                                            <td><?php echo $s['id']; ?></td>
                                            <td><?php echo htmlspecialchars($s['c_code'] ?? '295'); ?></td>
                                            <td><?php echo htmlspecialchars($s['eiin'] ?? '136257'); ?></td>
                                            <td><?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['registration'] ?? $s['reg_no'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['academic_year'] ?? $s['session'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['student_type'] ?? $s['type'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($s['father_name'] ?? 'N/A'); ?></td>
                                            <td><?php echo htmlspecialchars($s['gender'] ?? 'Male'); ?></td>
                                            <?php
                                            // Handle subjects - either from subjects field or individual sub_* fields
                                            $subjects = [];
                                            if (!empty($s['subjects'])) {
                                                $subjects = explode(',', $s['subjects']);
                                            } else {
                                                // Fallback to individual subject fields
                                                for ($i = 1; $i <= 13; $i++) {
                                                    $subField = "sub_$i";
                                                    if (!empty($s[$subField])) {
                                                        $subjects[] = $s[$subField];
                                                    }
                                                }
                                            }

                                            // Ensure we have 13 subject slots
                                            $subjects = array_pad($subjects, 13, '');

                                            for ($i = 0; $i < 13; $i++): ?>
                                            <td><?php echo htmlspecialchars($subjects[$i]); ?></td>
                                            <?php endfor; ?>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="edit_student.php?id=<?php echo $s['id']; ?>"
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_student.php?id=<?php echo $s['id']; ?>"
                                                       class="btn btn-outline-danger btn-sm"
                                                       onclick="return confirm('Are you sure?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable with adjusted column order for checkbox
            const hasCheckboxes = document.querySelector('.student-checkbox') !== null;
            const orderColumn = hasCheckboxes ? 1 : 0; // Adjust for checkbox column

            $('#studentsTable').DataTable({
                "pageLength": 25,
                "order": [[ orderColumn, "desc" ]],
                "scrollX": true,
                "responsive": true,
                "columnDefs": hasCheckboxes ? [
                    { "orderable": false, "targets": 0 } // Disable sorting on checkbox column
                ] : []
            });

            // Bulk operations functionality
            let selectedStudents = [];

            // Select All functionality
            $('#selectAll').change(function() {
                const isChecked = $(this).is(':checked');
                $('.student-checkbox').prop('checked', isChecked);
                updateSelectedStudents();
            });

            // Individual checkbox functionality
            $(document).on('change', '.student-checkbox', function() {
                updateSelectedStudents();

                // Update "Select All" checkbox state
                const totalCheckboxes = $('.student-checkbox').length;
                const checkedCheckboxes = $('.student-checkbox:checked').length;

                $('#selectAll').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
                $('#selectAll').prop('checked', checkedCheckboxes === totalCheckboxes);
            });

            // Update selected students array and UI
            function updateSelectedStudents() {
                selectedStudents = [];
                $('.student-checkbox:checked').each(function() {
                    selectedStudents.push($(this).val());
                });

                $('#selectedCount').text(selectedStudents.length);

                if (selectedStudents.length > 0) {
                    $('#bulkControls').show();
                    $('#bulkDeleteBtn').prop('disabled', false);
                } else {
                    $('#bulkControls').hide();
                    $('#bulkDeleteBtn').prop('disabled', true);
                }
            }

            // Clear selection
            $('#clearSelectionBtn').click(function() {
                $('.student-checkbox, #selectAll').prop('checked', false);
                $('#selectAll').prop('indeterminate', false);
                updateSelectedStudents();
            });

            // Bulk delete functionality
            $('#bulkDeleteBtn').click(function() {
                if (selectedStudents.length === 0) {
                    alert('কোন student নির্বাচিত নেই।');
                    return;
                }

                const confirmMessage = `আপনি কি নিশ্চিত যে আপনি ${selectedStudents.length} জন student মুছে ফেলতে চান?\n\nএই অ্যাকশনটি আর ফিরিয়ে আনা যাবে না!`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit
                    const form = $('<form>', {
                        method: 'POST',
                        action: 'bulk_delete_students.php'
                    });

                    selectedStudents.forEach(function(id) {
                        form.append($('<input>', {
                            type: 'hidden',
                            name: 'student_ids[]',
                            value: id
                        }));
                    });

                    $('body').append(form);
                    form.submit();
                }
            });
        });
    </script>
</body>
</html>
