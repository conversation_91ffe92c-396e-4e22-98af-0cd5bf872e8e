<?php
require_once 'includes/teacher_db.php';

// Get selected date
$selectedDate = $_GET['date'] ?? '';

if (!$selectedDate) {
    die('তারিখ নির্বাচন করুন!');
}

// Get duty assignments
$dutyAssignments = $teacherManager->getDutyAssignments($selectedDate);

if (empty($dutyAssignments)) {
    die('এই তারিখে কোন ডিউটি বন্টন পাওয়া যায়নি!');
}

// Get signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Generate memo number
$memoNumber = 'স্মারক নং: ০১/' . date('Y') . '-' . date('m', strtotime($selectedDate));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি লেটার - <?php echo formatDateBengali($selectedDate); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: white;
            color: #000;
        }
        
        .header { 
            text-align: center; 
            margin-bottom: 40px;
            border-bottom: 3px solid #000;
            padding-bottom: 20px;
            position: relative;
        }
        
        .logo {
            position: absolute;
            left: 0;
            top: 0;
            width: 80px;
            height: 80px;
        }
        
        .header h1 {
            font-size: 28px;
            margin: 0;
            font-weight: bold;
            color: #000;
            margin-left: 100px;
        }
        
        .header p {
            font-size: 18px;
            margin: 8px 0;
            color: #333;
            margin-left: 100px;
        }
        
        .memo-section {
            margin-bottom: 20px;
        }
        
        .memo-number {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .subject {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-decoration: underline;
        }
        
        .content {
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .teacher-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .teacher-table th,
        .teacher-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        
        .teacher-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 45%;
            text-align: center;
        }
        
        .signature-image {
            max-width: 150px;
            max-height: 60px;
            margin-bottom: 10px;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            margin-top: 60px;
            padding-top: 10px;
            font-weight: bold;
        }
        
        .date-section {
            margin-top: 30px;
            text-align: left;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            font-size: 14px;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button onclick="window.print()" class="print-button no-print">
        🖨️ প্রিন্ট করুন
    </button>

    <!-- Header -->
    <div class="header">
        <?php if (file_exists('uploads/college_logo.jpg')): ?>
            <img src="uploads/college_logo.jpg" alt="College Logo" class="logo">
        <?php endif; ?>
        <h1>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h1>
        <p>দামুড়হুদা, চুয়াডাঙ্গা</p>
    </div>

    <!-- Memo Number -->
    <div class="memo-section">
        <div class="memo-number"><?php echo $memoNumber; ?></div>
        <div style="text-align: right; font-size: 14px;">
            তারিখ: <?php echo formatDateBengali(date('Y-m-d')); ?>
        </div>
    </div>

    <!-- Subject -->
    <div class="subject">
        বিষয়: HSC পরীক্ষা-২০২৫ এর ডিউটি বন্টন সংক্রান্ত।
    </div>

    <!-- Content -->
    <div class="content">
        <p>
            আগামী <strong><?php echo formatDateBengali($selectedDate); ?></strong> তারিখে অনুষ্ঠিতব্য HSC পরীক্ষা-২০২৫ এর 
            তত্ত্বাবধানের জন্য নিম্নোক্ত শিক্ষকগণকে ডিউটিতে নিয়োজিত করা হলো:
        </p>
    </div>

    <!-- Teacher Table -->
    <table class="teacher-table">
        <thead>
            <tr>
                <th style="width: 8%;">ক্রমিক</th>
                <th style="width: 30%;">শিক্ষকের নাম</th>
                <th style="width: 20%;">পদবী</th>
                <th style="width: 20%;">বিষয়</th>
                <th style="width: 15%;">মোবাইল</th>
                <th style="width: 7%;">রুম</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($dutyAssignments as $index => $assignment): ?>
                <tr>
                    <td style="text-align: center;"><?php echo $index + 1; ?></td>
                    <td><?php echo htmlspecialchars($assignment['name']); ?></td>
                    <td><?php echo htmlspecialchars($assignment['designation']); ?></td>
                    <td><?php echo htmlspecialchars($assignment['subject']); ?></td>
                    <td><?php echo htmlspecialchars($assignment['mobile']); ?></td>
                    <td style="text-align: center;">
                        <?php echo $assignment['room_number'] ? htmlspecialchars($assignment['room_number']) : '-'; ?>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <!-- Additional Instructions -->
    <div class="content">
        <p><strong>নির্দেশনা:</strong></p>
        <ul>
            <li>সংশ্লিষ্ট সকল শিক্ষক নির্ধারিত সময়ে উপস্থিত থাকবেন।</li>
            <li>পরীক্ষার নিয়মকানুন যথাযথভাবে পালন করতে হবে।</li>
            <li>কোন সমস্যা হলে তাৎক্ষণিক কর্তৃপক্ষকে অবগত করতে হবে।</li>
        </ul>
    </div>

    <!-- Date -->
    <div class="date-section">
        <p>তারিখ: <?php echo formatDateBengali(date('Y-m-d')); ?></p>
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <!-- Principal Signature -->
        <div class="signature-box">
            <?php if ($principalSignature && file_exists($principalSignature['signature_path'])): ?>
                <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>" 
                     alt="Principal Signature" class="signature-image">
            <?php endif; ?>
            <div class="signature-line">
                অধ্যক্ষ<br>
                আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                দামুড়হুদা, চুয়াডাঙ্গা
            </div>
        </div>

        <!-- Convener Signature -->
        <div class="signature-box">
            <?php if ($convenerSignature && file_exists($convenerSignature['signature_path'])): ?>
                <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>" 
                     alt="Convener Signature" class="signature-image">
            <?php endif; ?>
            <div class="signature-line">
                পরীক্ষা নিয়ন্ত্রক<br>
                (কনভেনার)<br>
                HSC পরীক্ষা-২০২৫
            </div>
        </div>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>
