<?php
require_once 'includes/teacher_db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>ডিউটি সিস্টেম টেস্ট</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Hind Siliguri', sans-serif; }</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🧪 ডিউটি সিস্টেম টেস্ট</h1>";

// Create test duty assignment
if (isset($_POST['create_test_duty'])) {
    try {
        // Get some teachers
        $teachers = $teacherManager->getAllTeachers();
        
        if (count($teachers) >= 3) {
            // Take first 3 teachers for test
            $testTeacherIds = array_slice(array_column($teachers, 'id'), 0, 3);
            $testDate = '2025-06-26';
            
            // Create duty assignment
            $teacherManager->assignDuty($testDate, $testTeacherIds);
            
            echo "<div class='alert alert-success'>";
            echo "<h5>✅ টেস্ট ডিউটি তৈরি সফল!</h5>";
            echo "<p>তারিখ: $testDate</p>";
            echo "<p>শিক্ষক IDs: " . implode(', ', $testTeacherIds) . "</p>";
            echo "</div>";
            
            // Show link to print
            echo "<div class='text-center mb-4'>";
            echo "<a href='print_duty_list.php?date=$testDate' target='_blank' class='btn btn-primary'>";
            echo "<i class='fas fa-print me-2'></i>ডিউটি লিস্ট প্রিন্ট করুন";
            echo "</a>";
            echo "</div>";
            
        } else {
            echo "<div class='alert alert-warning'>";
            echo "টেস্টের জন্য কমপক্ষে ৩ জন শিক্ষক প্রয়োজন!";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "টেস্ট ডিউটি তৈরিতে সমস্যা: " . $e->getMessage();
        echo "</div>";
    }
}

// Show current status
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'>📊 বর্তমান অবস্থা</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Count teachers
    $teachers = $teacherManager->getAllTeachers();
    echo "<p><strong>মোট শিক্ষক:</strong> " . count($teachers) . " জন</p>";
    
    // Count duty dates
    $dutyDates = $teacherManager->getAllDutyDates();
    echo "<p><strong>ডিউটি তারিখ:</strong> " . count($dutyDates) . " টি</p>";
    
    // Show duty dates
    if (!empty($dutyDates)) {
        echo "<h6>বিদ্যমান ডিউটি তারিখসমূহ:</h6>";
        echo "<ul>";
        foreach ($dutyDates as $date) {
            $assignments = $teacherManager->getDutyAssignments($date);
            echo "<li>";
            echo "<strong>" . date('d F Y', strtotime($date)) . "</strong> - " . count($assignments) . " জন শিক্ষক ";
            echo "<a href='print_duty_list.php?date=$date' target='_blank' class='btn btn-sm btn-outline-primary ms-2'>";
            echo "<i class='fas fa-print me-1'></i>প্রিন্ট";
            echo "</a>";
            echo "</li>";
        }
        echo "</ul>";
    } else {
        echo "<div class='alert alert-info'>";
        echo "কোন ডিউটি বন্টন পাওয়া যায়নি।";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "ডেটা লোড করতে সমস্যা: " . $e->getMessage();
    echo "</div>";
}

echo "</div></div>";

// Test creation form
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5 class='mb-0'>🧪 টেস্ট ডিউটি তৈরি করুন</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<p>একটি নমুনা ডিউটি বন্টন তৈরি করুন (তারিখ: 26 জুন 2025):</p>";

echo "<form method='POST'>";
echo "<button type='submit' name='create_test_duty' class='btn btn-success'>";
echo "<i class='fas fa-plus me-2'></i>টেস্ট ডিউটি তৈরি করুন";
echo "</button>";
echo "</form>";

echo "</div></div>";

// Show teachers list
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'>👥 শিক্ষক তালিকা</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $teachers = $teacherManager->getAllTeachers();
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'>";
        echo "কোন শিক্ষক পাওয়া যায়নি! প্রথমে শিক্ষক যুক্ত করুন।";
        echo "<br><br>";
        echo "<a href='teacher_duty_management.php' class='btn btn-warning'>";
        echo "<i class='fas fa-user-plus me-2'></i>শিক্ষক যুক্ত করুন";
        echo "</a>";
        echo "</div>";
    } else {
        echo "<div class='table-responsive'>";
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>নাম</th><th>বিষয়</th><th>পদবী</th><th>ডিউটি স্ট্যাটাস</th></tr></thead>";
        echo "<tbody>";
        
        foreach (array_slice($teachers, 0, 10) as $teacher) {
            echo "<tr>";
            echo "<td>" . ($teacher['id'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['name']) . "</td>";
            echo "<td>" . htmlspecialchars($teacher['subject']) . "</td>";
            echo "<td>" . htmlspecialchars($teacher['designation']) . "</td>";
            echo "<td>";
            $status = $teacher['duty_status'];
            $badgeClass = $status === 'সম সময় অন্তর্ভুক্ত' ? 'bg-success' : ($status === 'কখনো অন্তর্ভুক্ত নয়' ? 'bg-danger' : 'bg-primary');
            echo "<span class='badge $badgeClass'>$status</span>";
            echo "</td>";
            echo "</tr>";
        }
        
        if (count($teachers) > 10) {
            echo "<tr><td colspan='5' class='text-center text-muted'>... আরো " . (count($teachers) - 10) . " জন শিক্ষক</td></tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "শিক্ষক তালিকা লোড করতে সমস্যা: " . $e->getMessage();
    echo "</div>";
}

echo "</div></div>";

// Navigation
echo "<div class='text-center'>";
echo "<a href='teacher_duty_management.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users me-2'></i>শিক্ষক ব্যবস্থাপনা";
echo "</a>";
echo "<a href='duty_letter_generator.php' class='btn btn-success me-2'>";
echo "<i class='fas fa-file-alt me-2'></i>ডিউটি লেটার";
echo "</a>";
echo "<a href='debug_duty_assignment.php' class='btn btn-info'>";
echo "<i class='fas fa-bug me-2'></i>ডিবাগ টুল";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
