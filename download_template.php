<?php
// Check if vendor/autoload.php exists
if (!file_exists(__DIR__ . '/vendor/autoload.php')) {
    die('Composer dependencies not installed. Please run "composer install" first.');
}

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

try {
    // Create new spreadsheet
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set headers
    $headers = [
        'C.Code', 'EIIN', 'Roll No.', 'Reg. No.', 'Session', 'Type', 'Group',
        'Student Name', 'Father Name', 'Gender',
        'Sub 1', 'Sub 2', 'Sub 3', 'Sub 4', 'Sub 5', 'Sub 6', 'Sub 7',
        'Sub 8', 'Sub 9', 'Sub 10', 'Sub 11', 'Sub 12', 'Sub 13'
    ];
    
    // Set header row
    $column = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($column . '1', $header);
        $column++;
    }
    
    // Style the header row
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => ['rgb' => '4472C4']
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['rgb' => '000000']
            ]
        ]
    ];
    
    $sheet->getStyle('A1:W1')->applyFromArray($headerStyle);
    
    // Auto-size columns
    foreach (range('A', 'W') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Add sample data (optional)
    $sampleData = [
        ['295', '123456', '101', 'REG001', '2023-24', 'Regular', 'Science', 
         'Sample Student', 'Sample Father', 'Male',
         '101', '102', '103', '104', '105', '106', '107',
         '108', '109', '110', '111', '112', '113'],
        ['295', '123456', '102', 'REG002', '2023-24', 'Regular', 'Arts',
         'Another Student', 'Another Father', 'Female',
         '101', '102', '103', '104', '105', '', '',
         '', '', '', '', '', '']
    ];
    
    $row = 2;
    foreach ($sampleData as $data) {
        $column = 'A';
        foreach ($data as $value) {
            $sheet->setCellValue($column . $row, $value);
            $column++;
        }
        $row++;
    }
    
    // Set filename
    $filename = 'EXMM_Student_Template_' . date('Y-m-d') . '.xlsx';
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Create writer and output
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
} catch (Exception $e) {
    // If there's an error, show a simple error page
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Template Download Error - EXMM</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="alert alert-danger">
                        <h4>Template Download Error</h4>
                        <p>Unable to generate Excel template: <?php echo htmlspecialchars($e->getMessage()); ?></p>
                        <p>Please make sure Composer dependencies are installed by running:</p>
                        <code>composer install</code>
                    </div>
                    <a href="upload.php" class="btn btn-primary">Back to Upload</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
