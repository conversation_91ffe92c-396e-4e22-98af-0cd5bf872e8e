<?php
session_start();
require_once 'includes/teacher_db.php';

// Get parameters
$date = $_GET['date'] ?? '';

if (empty($date)) {
    die('তারিখ পাওয়া যায়নি! URL এ date প্যারামিটার দিন।');
}

// Get duty assignments for the selected date
$assignments = $teacherManager->getDutyAssignments($date);

if (empty($assignments)) {
    die('এই তারিখের জন্য কোনো ডিউটি বন্টন পাওয়া যায়নি! তারিখ: ' . $date);
}

// Group assignments by room number
$roomGroups = [];
foreach ($assignments as $assignment) {
    $roomNumber = $assignment['room_number'] ?: 'অনির্দিষ্ট';
    if (!isset($roomGroups[$roomNumber])) {
        $roomGroups[$roomNumber] = [];
    }
    $roomGroups[$roomNumber][] = $assignment;
}

// Sort rooms by number
ksort($roomGroups);

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Get current date for printing
$currentDate = date('Y-m-d');
$printDate = formatDateBengali($currentDate);
$dutyDate = formatDateBengali($date);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম ভিত্তিক ডিউটি উপস্থিতি - <?php echo $dutyDate; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }

        .header h1 {
            font-size: 24px;
            margin: 5px 0;
            font-weight: bold;
            color: #000;
        }

        .header p {
            font-size: 16px;
            margin: 5px 0;
            color: #000;
        }

        .duty-date {
            font-size: 18px;
            font-weight: bold;
            margin: 15px 0;
            text-align: center;
        }

        .room-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        .room-header {
            background-color: #f0f0f0;
            padding: 10px;
            font-size: 18px;
            font-weight: bold;
            border: 1px solid #000;
            text-align: center;
            margin-bottom: 0;
        }

        .attendance-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
        }

        .attendance-table th,
        .attendance-table td {
            border: 1px solid #000;
            padding: 10px 8px;
            text-align: center;
        }

        .attendance-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            font-size: 14px;
        }

        .serial-col { width: 5%; }
        .teacher-name-col { width: 25%; }
        .designation-col { width: 15%; }
        .subject-col { width: 15%; }
        .mobile-col { width: 15%; }
        .time-col { width: 10%; }
        .signature-col { width: 15%; }

        .teacher-name {
            font-weight: bold;
            text-align: left;
        }

        .designation, .subject {
            text-align: left;
            font-size: 13px;
        }

        .mobile {
            font-size: 13px;
        }

        .time-signature-cell {
            height: 40px;
        }

        .print-footer {
            margin-top: 30px;
            text-align: right;
            font-size: 12px;
            color: #666;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            font-size: 16px;
        }

        @media print {
            .print-button {
                display: none;
            }
            
            body {
                padding: 0;
            }
            
            .room-section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">প্রিন্ট করুন</button>
    
    <div class="header">
        <div style="font-size:20px; font-weight:bold;">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
        <div style="font-size:16px;">দামুড়হুদা, চুয়াডাঙ্গা</div>
        <div style="font-size:18px; font-weight:bold; margin-bottom:8px;">HSC পরীক্ষা- ২০২৫</div>
        <h1>রুম ভিত্তিক ডিউটি শিক্ষকদের উপস্থিতি তালিকা</h1>
        <p>তারিখঃ <?php echo $dutyDate; ?></p>
    </div>
    
    <?php foreach ($roomGroups as $roomNumber => $roomTeachers): ?>
    <div class="room-section">
        <h3 class="room-header">রুম নম্বরঃ <?php echo htmlspecialchars($roomNumber); ?></h3>
        
        <table class="attendance-table">
            <thead>
                <tr>
                    <th class="serial-col">ক্রম</th>
                    <th class="teacher-name-col">শিক্ষকের নাম</th>
                    <th class="designation-col">পদবী</th>
                    <th class="subject-col">বিষয়</th>
                    <th class="mobile-col">মোবাইল</th>
                    <th class="time-col">উপস্থিতির সময়</th>
                    <th class="signature-col">স্বাক্ষর</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($roomTeachers as $idx => $teacher): ?>
                <tr>
                    <td class="serial-col"><?php echo $idx + 1; ?></td>
                    <td class="teacher-name-col">
                        <div class="teacher-name"><?php echo htmlspecialchars($teacher['name']); ?></div>
                    </td>
                    <td class="designation-col">
                        <div class="designation"><?php echo htmlspecialchars($teacher['designation']); ?></div>
                    </td>
                    <td class="subject-col">
                        <div class="subject"><?php echo htmlspecialchars($teacher['subject']); ?></div>
                    </td>
                    <td class="mobile-col">
                        <div class="mobile"><?php echo htmlspecialchars($teacher['mobile']); ?></div>
                    </td>
                    <td class="time-col time-signature-cell"></td>
                    <td class="signature-col time-signature-cell"></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php endforeach; ?>
    
    <div class="print-footer">
        <p>প্রিন্ট তারিখঃ <?php echo $printDate; ?></p>
    </div>
    
    <div style="width:100%; margin-top:60px; display:flex; justify-content:space-between;">
        <div style="text-align:center; width:40%;">
            <div style="border-top:1px solid #000; width:80%; margin:0 auto 5px auto;"></div>
            <span style="font-size:16px; font-weight:bold;">অধ্যক্ষ</span>
        </div>
        <div style="text-align:center; width:40%;">
            <div style="border-top:1px solid #000; width:80%; margin:0 auto 5px auto;"></div>
            <span style="font-size:16px; font-weight:bold;">আহবায়ক</span>
        </div>
    </div>
    
    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html>