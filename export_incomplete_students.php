<?php
require_once __DIR__ . '/models/Student.php';

// Process query parameters for filtering
$groupFilter = $_GET['group'] ?? '';
$typeFilter = $_GET['type'] ?? '';
$subjectCount = isset($_GET['subject_count']) ? (int)$_GET['subject_count'] : null;
$rollNumbers = $_GET['rolls'] ?? '';

// Get all students
$student = new Student();
$allStudents = $student->getAll();

// Filter students who don't have all 13 subjects
$incompleteStudents = [];

// First, count how many are actually incomplete
$totalWithIncompleteSubjects = 0;

foreach ($allStudents as $s) {
    $subjectCount = 0;
    $hasSubjectsField = false;
    $hasOldFormat = false;
    $subjects = [];

    // Check new subjects field format first
    if (!empty($s['subjects'])) {
        $hasSubjectsField = true;
        $subjects = explode(',', $s['subjects']);
        $subjects = array_filter($subjects, function($subject) {
            return !empty(trim($subject));
        });
        $subjectCount = count($subjects);
    } else {
        // Fallback to old format for backward compatibility
        $hasOldFormat = true;
        for ($i = 1; $i <= 13; $i++) {
            $subField = 'sub_' . $i;
            if (!empty($s[$subField])) {
                $subjectCount++;
                $subjects[] = $s[$subField];
            }
        }
    }

    // Store the subject count and subjects list for later use
    $s['subject_count'] = $subjectCount;
    $s['subject_list'] = $subjects;
    $s['has_subjects_field'] = $hasSubjectsField;
    $s['has_old_format'] = $hasOldFormat;

    // If student has less than 13 subjects, add to incomplete list
    if ($subjectCount < 13) {
        $totalWithIncompleteSubjects++;
        $incompleteStudents[] = $s;
    }
}

// Create debug log file to troubleshoot
$debug = false;
if (isset($_GET['debug'])) {
    $debug = true;
    $logFile = fopen(__DIR__ . '/export_debug.log', 'w');
    fwrite($logFile, "Total incomplete students before filtering: " . count($incompleteStudents) . "\n");
    fwrite($logFile, "Total students with less than 13 subjects: $totalWithIncompleteSubjects\n");
    fwrite($logFile, "Total students in database: " . count($allStudents) . "\n");
    
    // Log all parameters for debugging
    fwrite($logFile, "Parameters received:\n");
    foreach ($_GET as $key => $value) {
        fwrite($logFile, "  $key: $value\n");
    }
    
    // Log roll numbers if provided
    if (!empty($rollNumbers)) {
        $rollsArray = explode(',', $rollNumbers);
        fwrite($logFile, "Roll numbers count: " . count($rollsArray) . "\n");
        if (count($rollsArray) > 0) {
            fwrite($logFile, "First few roll numbers: " . implode(', ', array_slice($rollsArray, 0, 5)) . "\n");
        }
    }
}

// Apply filters
if (!empty($groupFilter)) {
    $incompleteStudents = array_filter($incompleteStudents, function($s) use ($groupFilter) {
        $group = $s['department'] ?? $s['group_name'] ?? '';
        return strcasecmp($group, $groupFilter) === 0;
    });
    $incompleteStudents = array_values($incompleteStudents); // Reindex array
}

if (!empty($typeFilter)) {
    $incompleteStudents = array_filter($incompleteStudents, function($s) use ($typeFilter) {
        $type = $s['student_type'] ?? $s['type'] ?? '';
        return strcasecmp($type, $typeFilter) === 0;
    });
    $incompleteStudents = array_values($incompleteStudents); // Reindex array
}

if ($subjectCount !== null) {
    // Make sure we're using the correct count
    $incompleteStudents = array_filter($incompleteStudents, function($s) use ($subjectCount) {
        // Always calculate the subject count to be sure it's correct
        $count = 0;
        if (!empty($s['subjects'])) {
            $subjects = explode(',', $s['subjects']);
            $count = count(array_filter($subjects, function($subject) {
                return !empty(trim($subject));
            }));
        } else {
            for ($i = 1; $i <= 13; $i++) {
                $subField = 'sub_' . $i;
                if (!empty($s[$subField])) {
                    $count++;
                }
            }
        }
        
        // Double-check against the stored subject_count
        if (isset($s['subject_count']) && $s['subject_count'] != $count) {
            // If there's a mismatch, use the calculated count
            $s['subject_count'] = $count;
        }
        
        return $s['subject_count'] === $subjectCount;
    });
    $incompleteStudents = array_values($incompleteStudents); // Reindex array
}

// Roll number filtering
$filteredByRolls = false;
if (!empty($rollNumbers)) {
    $rollsArray = explode(',', $rollNumbers);
    $rollsArray = array_map('trim', $rollsArray);
    
    if ($debug) {
        fwrite($logFile, "Roll numbers requested: " . implode(', ', $rollsArray) . "\n");
        fwrite($logFile, "Number of roll numbers: " . count($rollsArray) . "\n");
        
        // Log the structure of the first few students
        fwrite($logFile, "First 3 students before filtering:\n");
        for ($i = 0; $i < min(3, count($incompleteStudents)); $i++) {
            $student = $incompleteStudents[$i];
            fwrite($logFile, "Student $i: " . 
                "ID: " . ($student['id'] ?? 'N/A') . ", " .
                "Roll: '" . ($student['roll'] ?? 'N/A') . "', " . 
                "Roll_no: '" . ($student['roll_no'] ?? 'N/A') . "'\n");
        }
    }
    
    // Store original count
    $beforeCount = count($incompleteStudents);
    
    // Create a lookup array for faster searching
    $rollNumbersLookup = array_flip($rollsArray);
    
    // Modified filter to handle both string and numeric roll numbers
    $filtered = [];
    foreach ($incompleteStudents as $s) {
        // Try both roll and roll_no fields
        $studentRoll = (string)($s['roll'] ?? '');
        $studentRollNo = (string)($s['roll_no'] ?? '');
        
        // Check if either roll or roll_no is in our requested list
        if (isset($rollNumbersLookup[$studentRoll]) || isset($rollNumbersLookup[$studentRollNo])) {
            $filtered[] = $s;
            if ($debug) {
                fwrite($logFile, "Match found for roll: " . ($studentRoll ?: $studentRollNo) . "\n");
            }
        }
    }
    
    // If we found no matches with direct lookup, try a more flexible approach
    if (empty($filtered)) {
        if ($debug) {
            fwrite($logFile, "No direct matches found, trying flexible comparison...\n");
        }
        
        foreach ($incompleteStudents as $s) {
            $studentRoll = (string)($s['roll'] ?? '');
            $studentRollNo = (string)($s['roll_no'] ?? '');
            
            foreach ($rollsArray as $searchRoll) {
                // Compare as strings to avoid type mismatches
                if ($studentRoll === $searchRoll || $studentRollNo === $searchRoll) {
                    $filtered[] = $s;
                    if ($debug) {
                        fwrite($logFile, "Flexible match found for roll: $searchRoll\n");
                    }
                    break;
                }
            }
        }
    }
    
    $incompleteStudents = $filtered;
    $filteredByRolls = true;
    
    if ($debug) {
        fwrite($logFile, "Students after roll filtering: " . count($incompleteStudents) . 
            " (before: $beforeCount)\n");
        
        if (count($incompleteStudents) === 0) {
            fwrite($logFile, "WARNING: No students matched the roll numbers!\n");
            fwrite($logFile, "Roll numbers requested: " . implode(', ', $rollsArray) . "\n");
            
            // Try to find these roll numbers in ALL students (not just incomplete)
            fwrite($logFile, "Checking if these rolls exist in complete students...\n");
            $allStudentsCount = count($allStudents);
            $foundInAll = 0;
            
            foreach ($rollsArray as $searchRoll) {
                foreach ($allStudents as $s) {
                    $studentRoll = (string)($s['roll'] ?? '');
                    $studentRollNo = (string)($s['roll_no'] ?? '');
                    
                    if ($studentRoll === $searchRoll || $studentRollNo === $searchRoll) {
                        $foundInAll++;
                        $subjectCount = $s['subject_count'] ?? -1;
                        if ($subjectCount >= 13) {
                            fwrite($logFile, "Roll $searchRoll found but has all 13 subjects\n");
                        } else {
                            fwrite($logFile, "Roll $searchRoll found with $subjectCount subjects but didn't match filter\n");
                        }
                        break;
                    }
                }
            }
            
            fwrite($logFile, "Found $foundInAll of " . count($rollsArray) . " rolls in the complete database of $allStudentsCount students\n");
        }
    }
}

// Sort by roll number
usort($incompleteStudents, function($a, $b) {
    $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
    $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
    return $rollA - $rollB;
});

if ($debug) {
    fwrite($logFile, "Final student count after all filtering: " . count($incompleteStudents) . "\n");
}

// Prepare filename components
$filename = 'EXMM_Incomplete_Students';

if (!empty($groupFilter)) {
    $filename .= '_Group_' . $groupFilter;
}
if (!empty($typeFilter)) {
    $filename .= '_' . $typeFilter;
}
if ($subjectCount !== null) {
    $filename .= '_SubjectCount_' . $subjectCount;
}
if (!empty($rollNumbers)) {
    $filename .= '_SelectedRolls';
}

$filename .= '_' . date('Y-m-d_H-i-s') . '.csv';

// If in debug mode, write to log instead of outputting
if ($debug) {
    fwrite($logFile, "Filename: $filename\n");
    fwrite($logFile, "Headers would be sent now\n");
    fwrite($logFile, "Student data to export:\n");
    foreach ($incompleteStudents as $index => $student) {
        fwrite($logFile, "[$index] Roll: " . ($student['roll'] ?? $student['roll_no'] ?? 'N/A') . 
            ", Name: " . ($student['student_name'] ?? $student['name'] ?? 'N/A') . "\n");
    }
    fclose($logFile);
    
    // Output debug info as HTML
    echo "<!DOCTYPE html><html><head><title>CSV Export Debug</title>";
    echo "<style>body{font-family:'Hind Siliguri',Arial,sans-serif;margin:20px;} pre{background:#f5f5f5;padding:10px;border-radius:5px;} 
        table{border-collapse:collapse;width:100%;margin-bottom:20px;} 
        th,td{border:1px solid #ddd;padding:8px;text-align:left;}
        th{background-color:#f2f2f2;}
        .highlight{background-color:#fffacd;}
        .success{color:green;font-weight:bold;}
        .warning{color:orange;font-weight:bold;}
        .error{color:red;font-weight:bold;}
        .btn{display:inline-block;padding:6px 12px;background:#4472C4;color:white;text-decoration:none;border-radius:4px;margin:5px 0;}
        </style></head><body>";
    echo "<h2>🐛 CSV এক্সপোর্ট ডিবাগ তথ্য</h2>";
    
    echo "<h3>তথ্যের সারাংশ</h3>";
    echo "<ul>";
    echo "<li>ডাটাবেসে মোট শিক্ষার্থী: <strong>" . count($allStudents) . "</strong></li>";
    echo "<li>অসম্পূর্ণ বিষয়ের শিক্ষার্থী: <strong>" . $totalWithIncompleteSubjects . "</strong></li>";
    echo "<li>বর্তমান ফিল্টার অনুযায়ী শিক্ষার্থী: <strong>" . count($incompleteStudents) . "</strong></li>";
    echo "</ul>";
    
    echo "<h3>অনুরোধকৃত ফিল্টার</h3>";
    echo "<ul>";
    if (!empty($rollNumbers)) echo "<li>রোল নম্বর: <strong>" . htmlspecialchars($rollNumbers) . "</strong></li>";
    if (!empty($groupFilter)) echo "<li>গ্রুপ: <strong>" . htmlspecialchars($groupFilter) . "</strong></li>";
    if (!empty($typeFilter)) echo "<li>ধরন: <strong>" . htmlspecialchars($typeFilter) . "</strong></li>";
    if ($subjectCount !== null) echo "<li>বিষয় সংখ্যা: <strong>" . htmlspecialchars($subjectCount) . "</strong></li>";
    echo "</ul>";
    
    if (!empty($rollNumbers)) {
        echo "<div class='highlight' style='padding:10px;border-radius:5px;border:1px solid #ccc;'>";
        echo "<h3>অনুরোধকৃত রোল নম্বর</h3>";
        $rollArray = explode(',', $rollNumbers);
        $rollArray = array_map('trim', $rollArray);
        echo "<p>মোট রোল: <strong>" . count($rollArray) . "</strong></p>";
        
        echo "<table>";
        echo "<tr><th>রোল</th><th>ডাটাবেসে আছে?</th><th>বিষয় সংখ্যা</th><th>শিক্ষার্থীর নাম</th></tr>";
        
        foreach ($rollArray as $roll) {
            $found = false;
            $studentInfo = null;
            $subCount = 0;
            
            // Search in all students
            foreach ($allStudents as $student) {
                $sRoll = (string)($student['roll'] ?? '');
                $sRollNo = (string)($student['roll_no'] ?? '');
                
                if ($sRoll === $roll || $sRollNo === $roll) {
                    $found = true;
                    $studentInfo = $student;
                    $subCount = $student['subject_count'] ?? 0;
                    break;
                }
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($roll) . "</td>";
            echo "<td>" . ($found ? "<span class='success'>হ্যাঁ</span>" : "<span class='error'>না</span>") . "</td>";
            
            if ($found) {
                echo "<td>" . $subCount . "/13";
                echo $subCount < 13 ? " <span class='warning'>(অসম্পূর্ণ)</span>" : " <span class='success'>(সম্পূর্ণ)</span>";
                echo "</td>";
                echo "<td>" . htmlspecialchars($studentInfo['name'] ?? $studentInfo['student_name'] ?? 'N/A') . "</td>";
            } else {
                echo "<td colspan='2'><span class='error'>শিক্ষার্থী খুঁজে পাওয়া যায়নি</span></td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
    if (count($incompleteStudents) === 0) {
        echo "<div class='error' style='padding:15px;background:#ffeeee;border-radius:5px;margin:20px 0;'>";
        echo "<h3>⚠️ আপনার ফিল্টার অনুযায়ী কোন শিক্ষার্থী পাওয়া যায়নি!</h3>";
        echo "<p>এর কারণ হতে পারে:</p>";
        echo "<ul>";
        if (!empty($rollNumbers)) {
            echo "<li>অনুরোধকৃত রোল নম্বরগুলি ডাটাবেসে নেই</li>";
            echo "<li>এই রোল নম্বরের শিক্ষার্থীদের সবার ১৩টি বিষয় সম্পূর্ণ আছে</li>";
            echo "<li>রোল নম্বরের ফরম্যাটে সমস্যা (যেমন, শূন্য বা স্পেস সহ)</li>";
        } else {
            echo "<li>আপনার ফিল্টার ক্রাইটেরিয়া অনুযায়ী কোন শিক্ষার্থী নেই</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>সমাধান</h3>";
        echo "<p>নিচের অপশনগুলি ব্যবহার করুন:</p>";
        echo "<a href='incomplete_subject_students.php' class='btn'>অসম্পূর্ণ শিক্ষার্থী তালিকায় ফিরে যান</a> ";
        echo "<a href='export_incomplete_students.php' class='btn'>সকল অসম্পূর্ণ শিক্ষার্থী এক্সপোর্ট</a>";
    } else {
        echo "<h3>এক্সপোর্ট হবে এমন শিক্ষার্থী (" . count($incompleteStudents) . ")</h3>";
        echo "<table>";
        echo "<tr><th>রোল</th><th>নাম</th><th>বিষয় সংখ্যা</th><th>বিষয়সমূহ</th></tr>";
        foreach ($incompleteStudents as $student) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($student['student_name'] ?? $student['name'] ?? 'N/A') . "</td>";
            echo "<td>" . ($student['subject_count'] ?? 0) . "/13</td>";
            echo "<td>" . htmlspecialchars(implode(', ', $student['subject_list'] ?? [])) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>" . count($incompleteStudents) . "</strong> জন শিক্ষার্থীর তথ্য এক্সপোর্ট হবে।</p>";
        echo "<a href='export_incomplete_students.php" . 
            (!empty($rollNumbers) ? "?rolls=".urlencode($rollNumbers) : "") . 
            (!empty($groupFilter) ? (empty($rollNumbers) ? "?" : "&") . "group=".urlencode($groupFilter) : "") .
            (!empty($typeFilter) ? (empty($rollNumbers) && empty($groupFilter) ? "?" : "&") . "type=".urlencode($typeFilter) : "") .
            "' class='btn'>CSV এক্সপোর্ট ডাউনলোড করুন</a> ";
        echo "<a href='incomplete_subject_students.php' class='btn'>অসম্পূর্ণ শিক্ষার্থী তালিকায় ফিরে যান</a>";
    }
    
    echo "<p style='margin-top:30px;font-size:12px;color:#666;'>ডিবাগ লগ লোকেশন: export_debug.log</p>";
    echo "</body></html>";
    exit;
}

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Pragma: no-cache');
header('Expires: 0');

// Create output stream
$output = fopen('php://output', 'w');

// Add BOM for UTF-8 (helps with Bengali characters in Excel)
fwrite($output, "\xEF\xBB\xBF");

// Write CSV header
$headers = [
    'ID', 
    'Roll No', 
    'Registration No', 
    'Student Name',
    'Father Name',
    'Type',
    'Group',
    'Session',
    'Gender',
    'Subject Count',
    'Sub 1', 'Sub 2', 'Sub 3', 'Sub 4', 'Sub 5', 'Sub 6', 'Sub 7',
    'Sub 8', 'Sub 9', 'Sub 10', 'Sub 11', 'Sub 12', 'Sub 13'
];

fputcsv($output, $headers);

// Write student data
foreach ($incompleteStudents as $student) {
    // Extract subjects
    $subjects = [];
    if (!empty($student['subjects'])) {
        $subjectArray = explode(',', $student['subjects']);
        $subjectArray = array_map('trim', $subjectArray);
        $subjects = array_pad($subjectArray, 13, ''); // Pad to 13 elements
    } else {
        // Use old format
        for ($i = 1; $i <= 13; $i++) {
            $subjects[] = $student["sub_$i"] ?? '';
        }
    }

    $row = [
        $student['id'] ?? '',
        $student['roll'] ?? $student['roll_no'] ?? '',
        $student['reg_no'] ?? $student['registration'] ?? '',
        $student['student_name'] ?? $student['name'] ?? '',
        $student['father_name'] ?? '',
        $student['student_type'] ?? $student['type'] ?? '',
        $student['department'] ?? $student['group_name'] ?? '',
        $student['session'] ?? $student['academic_year'] ?? '',
        $student['gender'] ?? '',
        $student['subject_count'] ?? 0
    ];

    // Add the subjects to the row
    foreach ($subjects as $subject) {
        $row[] = $subject;
    }

    fputcsv($output, $row);
}

fclose($output);
exit; 