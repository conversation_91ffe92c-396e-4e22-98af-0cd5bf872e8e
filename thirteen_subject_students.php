<?php
require_once 'models/Student.php';

// Get all students with exactly 13 subjects
$student = new Student();
$allStudents = $student->getAll();

$thirteenSubjectStudents = [];
foreach ($allStudents as $s) {
    $subjectCount = 0;

    // Check new subjects field format first
    if (!empty($s['subjects'])) {
        $subjects = explode(',', $s['subjects']);
        $subjectCount = count(array_filter($subjects, function($subject) {
            return !empty(trim($subject));
        }));
    } else {
        // Fallback to old format for backward compatibility
        for ($i = 1; $i <= 13; $i++) {
            if (!empty($s["sub_$i"])) {
                $subjectCount++;
            }
        }
    }

    if ($subjectCount == 13) {
        $thirteenSubjectStudents[] = $s;
    }
}

// Sort by roll number
usort($thirteenSubjectStudents, function($a, $b) {
    $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
    $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
    return $rollA - $rollB;
});

$totalStudents = count($thirteenSubjectStudents);

// Group by type
$regularStudents = array_filter($thirteenSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Regular';
});
$irregularStudents = array_filter($thirteenSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Irregular';
});
$improvementStudents = array_filter($thirteenSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Improvement';
});

// Group by group_name
$groupStats = [];
foreach ($thirteenSubjectStudents as $s) {
    $group = $s['department'] ?? $s['group_name'] ?? 'Unknown';
    if (!isset($groupStats[$group])) {
        $groupStats[$group] = [];
    }
    $groupStats[$group][] = $s;
}

// Sort groups
ksort($groupStats);

// Show all students (DataTables will handle pagination)
$studentsToShow = $thirteenSubjectStudents;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>১৩ বিষয়ের শিক্ষার্থী - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
        }
        
        .pagination .page-link {
            border-radius: 10px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h3 class="mb-0">
                                        <i class="fas fa-graduation-cap me-2"></i>
                                        ১৩ বিষয়ের শিক্ষার্থী
                                    </h3>
                                    <p class="mb-0 text-white-50">যে সকল শিক্ষার্থীর ১৩টি বিষয় রয়েছে</p>
                                </div>
                                <div class="col-auto">
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-success btn-lg dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-id-card me-2"></i>সীট কার্ড প্রিন্ট
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="working_seat_cards.php?complete_only=1" target="_blank">
                                                <i class="fas fa-users me-2"></i>সকল সম্পূর্ণ সীটকার্ড
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">গ্রুপ ভিত্তিক</h6></li>
                                            <?php foreach ($groupStats as $group => $students): ?>
                                                <li><a class="dropdown-item" href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&complete_only=1" target="_blank">
                                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?> জন)
                                                </a></li>
                                            <?php endforeach; ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">টাইপ ভিত্তিক</h6></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Regular&complete_only=1" target="_blank">
                                                <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularStudents); ?> জন)
                                            </a></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Irregular&complete_only=1" target="_blank">
                                                <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularStudents); ?> জন)
                                            </a></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Improvement&complete_only=1" target="_blank">
                                                <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementStudents); ?> জন)
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="seat_card_generator.php">
                                                <i class="fas fa-cogs me-2"></i>কাস্টম সীটকার্ড জেনারেটর
                                            </a></li>
                                        </ul>
                                    </div>
                                    <a href="index.php" class="btn btn-light">
                                        <i class="fas fa-home me-2"></i>হোম পেজ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $totalStudents; ?></div>
                        <h6 class="text-white-50 mb-0">মোট শিক্ষার্থী</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%);">
                        <div class="stats-number"><?php echo count($regularStudents); ?></div>
                        <h6 class="text-white-50 mb-0">নিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);">
                        <div class="stats-number"><?php echo count($irregularStudents); ?></div>
                        <h6 class="text-white-50 mb-0">অনিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);">
                        <div class="stats-number"><?php echo count($improvementStudents); ?></div>
                        <h6 class="text-white-50 mb-0">উন্নতি</h6>
                    </div>
                </div>
            </div>

            <!-- Group Statistics -->
            <?php if (!empty($groupStats)): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>গ্রুপ ভিত্তিক পরিসংখ্যান (সম্পূর্ণ ১৩ বিষয়)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($groupStats as $group => $students): ?>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <div class="card border-success">
                                            <div class="card-body text-center">
                                                <h4 class="text-success"><?php echo count($students); ?></h4>
                                                <h6 class="card-title"><?php echo htmlspecialchars($group); ?></h6>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&complete_only=1"
                                                       class="btn btn-outline-success btn-sm" target="_blank">
                                                        <i class="fas fa-id-card"></i> সীট কার্ড
                                                    </a>
                                                    <button class="btn btn-outline-info btn-sm" onclick="showGroupDetails('<?php echo htmlspecialchars($group); ?>')">
                                                        <i class="fas fa-eye"></i> বিস্তারিত
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 mb-2">
                                    <a href="complete_group_seat_card_generator.php" class="btn btn-outline-info w-100">
                                        <i class="fas fa-layer-group me-2"></i>সম্পূর্ণ গ্রুপ জেনারেটর
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="working_seat_cards.php?complete_only=1" class="btn btn-outline-success w-100" target="_blank">
                                        <i class="fas fa-users me-2"></i>সকল সম্পূর্ণ সীট কার্ড
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="working_seat_cards.php?type=Regular&complete_only=1" class="btn btn-outline-primary w-100" target="_blank">
                                        <i class="fas fa-user-check me-2"></i>নিয়মিত সম্পূর্ণ
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="working_seat_cards.php?type=Irregular&complete_only=1" class="btn btn-outline-danger w-100" target="_blank">
                                        <i class="fas fa-user-times me-2"></i>অনিয়মিত সম্পূর্ণ
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="working_seat_cards.php?type=Improvement&complete_only=1" class="btn btn-outline-warning w-100" target="_blank">
                                        <i class="fas fa-user-graduate me-2"></i>উন্নতি সম্পূর্ণ
                                    </a>
                                </div>
                                <div class="col-md-2 mb-2">
                                    <a href="seat_card_generator.php" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-cogs me-2"></i>কাস্টম জেনারেটর
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <ul class="nav nav-pills nav-fill" id="studentTypeTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                                        <i class="fas fa-users me-2"></i>সকল শিক্ষার্থী (<?php echo $totalStudents; ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="regular-tab" data-bs-toggle="pill" data-bs-target="#regular" type="button" role="tab">
                                        <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularStudents); ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="irregular-tab" data-bs-toggle="pill" data-bs-target="#irregular" type="button" role="tab">
                                        <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularStudents); ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="improvement-tab" data-bs-toggle="pill" data-bs-target="#improvement" type="button" role="tab">
                                        <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementStudents); ?>)
                                    </button>
                                </li>
                                <?php foreach ($groupStats as $group => $students): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="group-<?php echo strtolower(str_replace(' ', '-', $group)); ?>-tab"
                                            data-bs-toggle="pill" data-bs-target="#group-<?php echo strtolower(str_replace(' ', '-', $group)); ?>"
                                            type="button" role="tab">
                                        <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?>)
                                    </button>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students List -->
            <div class="row">
                <div class="col-12">
                    <div class="tab-content" id="studentTypeTabsContent">
                        <!-- All Students Tab -->
                        <div class="tab-pane fade show active" id="all" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-users me-2"></i>সকল শিক্ষার্থী
                                                <span class="badge bg-primary ms-2"><?php echo $totalStudents; ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?complete_only=1" class="btn btn-success" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>সকল সম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <?php if (empty($thirteenSubjectStudents)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">কোন শিক্ষার্থী পাওয়া যায়নি</h5>
                                        </div>
                                    <?php else: ?>
                                        <div class="mb-3">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary" onclick="selectAllStudents('all-students-table')">
                                                    <i class="fas fa-check-square me-1"></i>সব নির্বাচন
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary" onclick="deselectAllStudents('all-students-table')">
                                                    <i class="fas fa-square me-1"></i>সব বাতিল
                                                </button>
                                                <button class="btn btn-sm btn-success" onclick="printSelectedSeatCards('all-students-table')">
                                                    <i class="fas fa-print me-1"></i>নির্বাচিত সীটকার্ড প্রিন্ট
                                                </button>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0 students-table" id="all-students-table">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>
                                                            <input type="checkbox" class="form-check-input" onchange="toggleAllCheckboxes('all-students-table', this)">
                                                        </th>
                                                        <th>Roll No.</th>
                                                        <th>Student Name</th>
                                                        <th>Type</th>
                                                        <th>Group</th>
                                                        <th>Session</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($thirteenSubjectStudents as $student): ?>
                                                        <tr>
                                                            <td>
                                                                <input type="checkbox" class="form-check-input student-checkbox" value="<?php echo htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? ''); ?>">
                                                            </td>
                                                            <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? ''); ?></strong></td>
                                                            <td><strong><?php echo htmlspecialchars($student['name'] ?? $student['student_name'] ?? ''); ?></strong></td>
                                                            <td>
                                                                <?php
                                                                $type = $student['student_type'] ?? $student['type'] ?? '';
                                                                $badgeClass = '';
                                                                switch($type) {
                                                                    case 'Regular': $badgeClass = 'bg-success'; break;
                                                                    case 'Irregular': $badgeClass = 'bg-danger'; break;
                                                                    case 'Improvement': $badgeClass = 'bg-warning'; break;
                                                                    default: $badgeClass = 'bg-secondary';
                                                                }
                                                                ?>
                                                                <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($type); ?></span>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($student['department'] ?? $student['group_name'] ?? ''); ?></td>
                                                            <td><?php echo htmlspecialchars($student['academic_year'] ?? $student['session'] ?? ''); ?></td>
                                                            <td>
                                                                <div class="btn-group" role="group">
                                                                    <a href="working_seat_cards.php?search=<?php echo urlencode($student['name'] ?? $student['student_name'] ?? ''); ?>"
                                                                       class="btn btn-sm btn-primary" target="_blank">
                                                                        <i class="fas fa-id-card"></i> Seat Card
                                                                    </a>
                                                                    <a href="working_seat_cards.php?rolls=<?php echo urlencode($student['roll'] ?? $student['roll_no'] ?? ''); ?>&complete_only=1"
                                                                       class="btn btn-sm btn-success" target="_blank">
                                                                        <i class="fas fa-print"></i> Print
                                                                    </a>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Regular Students Tab -->
                        <div class="tab-pane fade" id="regular" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-check me-2"></i>নিয়মিত শিক্ষার্থী
                                                <span class="badge bg-light text-success ms-2"><?php echo count($regularStudents); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Regular&complete_only=1" class="btn btn-light" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>নিয়মিত সম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php
                                    $regularStudentsTab = array_filter($thirteenSubjectStudents, function($s) {
                                        return ($s['student_type'] ?? $s['type'] ?? '') == 'Regular';
                                    });
                                    ?>
                                    <?php if (empty($regularStudentsTab)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">কোন নিয়মিত শিক্ষার্থী পাওয়া যায়নি</h5>
                                        </div>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0 students-table">
                                                <thead class="table-success">
                                                    <tr>
                                                        <th>Roll No.</th>
                                                        <th>Student Name</th>
                                                        <th>Group</th>
                                                        <th>Session</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($regularStudents as $student): ?>
                                                        <tr>
                                                            <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll_no']); ?></strong></td>
                                                            <td><strong><?php echo htmlspecialchars($student['student_name']); ?></strong></td>
                                                            <td><?php echo htmlspecialchars($student['group_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($student['session']); ?></td>
                                                            <td>
                                                                <a href="working_seat_cards.php?search=<?php echo urlencode($student['student_name']); ?>"
                                                                   class="btn btn-sm btn-success" target="_blank">
                                                                    <i class="fas fa-id-card"></i> Seat Card
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Irregular Students Tab -->
                        <div class="tab-pane fade" id="irregular" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-times me-2"></i>অনিয়মিত শিক্ষার্থী
                                                <span class="badge bg-light text-danger ms-2"><?php echo count(array_filter($thirteenSubjectStudents, function($s) { return ($s['type'] ?? '') == 'Irregular'; })); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Irregular&complete_only=1" class="btn btn-light" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>অনিয়মিত সম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php
                                    $irregularStudents = array_filter($thirteenSubjectStudents, function($s) {
                                        return ($s['type'] ?? '') == 'Irregular';
                                    });
                                    ?>
                                    <?php if (empty($irregularStudents)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-user-times fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">কোন অনিয়মিত শিক্ষার্থী পাওয়া যায়নি</h5>
                                        </div>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0 students-table">
                                                <thead class="table-danger">
                                                    <tr>
                                                        <th>Roll No.</th>
                                                        <th>Student Name</th>
                                                        <th>Group</th>
                                                        <th>Session</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($irregularStudents as $student): ?>
                                                        <tr>
                                                            <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll_no']); ?></strong></td>
                                                            <td><strong><?php echo htmlspecialchars($student['student_name']); ?></strong></td>
                                                            <td><?php echo htmlspecialchars($student['group_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($student['session']); ?></td>
                                                            <td>
                                                                <a href="working_seat_cards.php?search=<?php echo urlencode($student['student_name']); ?>"
                                                                   class="btn btn-sm btn-danger" target="_blank">
                                                                    <i class="fas fa-id-card"></i> Seat Card
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Improvement Students Tab -->
                        <div class="tab-pane fade" id="improvement" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-graduate me-2"></i>উন্নতি শিক্ষার্থী
                                                <span class="badge bg-dark ms-2"><?php echo count(array_filter($thirteenSubjectStudents, function($s) { return ($s['type'] ?? '') == 'Improvement'; })); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Improvement&complete_only=1" class="btn btn-dark" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>উন্নতি সম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php
                                    $improvementStudents = array_filter($thirteenSubjectStudents, function($s) {
                                        return ($s['type'] ?? '') == 'Improvement';
                                    });
                                    ?>
                                    <?php if (empty($improvementStudents)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">কোন উন্নতি শিক্ষার্থী পাওয়া যায়নি</h5>
                                        </div>
                                    <?php else: ?>
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover mb-0 students-table">
                                                <thead class="table-warning">
                                                    <tr>
                                                        <th>Roll No.</th>
                                                        <th>Student Name</th>
                                                        <th>Group</th>
                                                        <th>Session</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($improvementStudents as $student): ?>
                                                        <tr>
                                                            <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll_no']); ?></strong></td>
                                                            <td><strong><?php echo htmlspecialchars($student['student_name']); ?></strong></td>
                                                            <td><?php echo htmlspecialchars($student['group_name']); ?></td>
                                                            <td><?php echo htmlspecialchars($student['session']); ?></td>
                                                            <td>
                                                                <a href="working_seat_cards.php?search=<?php echo urlencode($student['student_name']); ?>"
                                                                   class="btn btn-sm btn-warning" target="_blank">
                                                                    <i class="fas fa-id-card"></i> Seat Card
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Group-wise Tabs -->
                        <?php foreach ($groupStats as $group => $students): ?>
                            <?php $groupId = strtolower(str_replace(' ', '-', $group)); ?>
                            <div class="tab-pane fade" id="group-<?php echo $groupId; ?>" role="tabpanel">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <div class="row align-items-center">
                                            <div class="col">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> - সম্পূর্ণ ১ৃ বিষয়
                                                    <span class="badge bg-dark ms-2"><?php echo count($students); ?></span>
                                                </h5>
                                            </div>
                                            <div class="col-auto">
                                                <div class="btn-group" role="group">
                                                    <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&complete_only=1"
                                                       class="btn btn-light" target="_blank">
                                                        <i class="fas fa-id-card me-2"></i><?php echo htmlspecialchars($group); ?> সম্পূর্ণ সীটকার্ড
                                                    </a>
                                                    <button class="btn btn-outline-light" onclick="printGroupSeatCards('<?php echo htmlspecialchars($group); ?>')">
                                                        <i class="fas fa-print me-2"></i>প্রিন্ট
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <?php if (empty($students)): ?>
                                            <div class="text-center py-5">
                                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                                <h5 class="text-success"><?php echo htmlspecialchars($group); ?> গ্রুপে কোন সম্পূর্ণ শিক্ষার্থী নেই!</h5>
                                            </div>
                                        <?php else: ?>
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover mb-0 students-table">
                                                    <thead class="table-success">
                                                        <tr>
                                                            <th>
                                                                <input type="checkbox" class="form-check-input" onchange="toggleAllCheckboxes('group-<?php echo $groupId; ?>-table', this)">
                                                            </th>
                                                            <th>Roll No.</th>
                                                            <th>Student Name</th>
                                                            <th>Type</th>
                                                            <th>Session</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($students as $student): ?>
                                                            <tr>
                                                                <td>
                                                                    <input type="checkbox" class="form-check-input student-checkbox" value="<?php echo htmlspecialchars($student['roll_no']); ?>">
                                                                </td>
                                                                <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll_no']); ?></strong></td>
                                                                <td><strong><?php echo htmlspecialchars($student['student_name']); ?></strong></td>
                                                                <td>
                                                                    <?php
                                                                    $type = $student['type'] ?? '';
                                                                    $badgeClass = '';
                                                                    switch($type) {
                                                                        case 'Regular': $badgeClass = 'bg-success'; break;
                                                                        case 'Irregular': $badgeClass = 'bg-danger'; break;
                                                                        case 'Improvement': $badgeClass = 'bg-warning'; break;
                                                                        default: $badgeClass = 'bg-secondary';
                                                                    }
                                                                    ?>
                                                                    <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($type); ?></span>
                                                                </td>
                                                                <td><?php echo htmlspecialchars($student['session']); ?></td>
                                                                <td>
                                                                    <div class="btn-group" role="group">
                                                                        <a href="working_seat_cards.php?search=<?php echo urlencode($student['student_name']); ?>"
                                                                           class="btn btn-sm btn-success" target="_blank">
                                                                            <i class="fas fa-id-card"></i> Seat Card
                                                                        </a>
                                                                        <a href="working_seat_cards.php?rolls=<?php echo urlencode($student['roll_no']); ?>&complete_only=1"
                                                                           class="btn btn-sm btn-primary" target="_blank">
                                                                            <i class="fas fa-print"></i> Print
                                                                        </a>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTables for all student tables
            $('.students-table').DataTable({
                "pageLength": 25,
                "order": [[ 0, "asc" ]], // Sort by Roll No. column
                "scrollX": true,
                "responsive": true,
                "language": {
                    "lengthMenu": "প্রতি পৃষ্ঠায় _MENU_ টি রেকর্ড দেখান",
                    "zeroRecords": "কোন রেকর্ড পাওয়া যায়নি",
                    "info": "_START_ থেকে _END_ পর্যন্ত _TOTAL_ টি রেকর্ডের মধ্যে",
                    "infoEmpty": "০ টি রেকর্ড",
                    "infoFiltered": "(মোট _MAX_ টি রেকর্ড থেকে ফিল্টার করা হয়েছে)",
                    "search": "খুঁজুন:",
                    "paginate": {
                        "first": "প্রথম",
                        "last": "শেষ",
                        "next": "পরবর্তী",
                        "previous": "পূর্ববর্তী"
                    }
                }
            });

            // Add custom styling for nav pills
            $('.nav-pills .nav-link').on('click', function() {
                // Remove active class from all
                $('.nav-pills .nav-link').removeClass('active');
                // Add active class to clicked
                $(this).addClass('active');
            });
        });

        // Show group details
        function showGroupDetails(groupName) {
            // Switch to the group tab
            const groupId = groupName.toLowerCase().replace(/\s+/g, '-');
            const tabButton = document.getElementById('group-' + groupId + '-tab');
            if (tabButton) {
                tabButton.click();
            }
        }

        // Print group seat cards
        function printGroupSeatCards(groupName) {
            const url = 'working_seat_cards.php?group=' + encodeURIComponent(groupName) + '&complete_only=1';
            window.open(url, '_blank');
        }

        // Bulk actions
        function selectAllStudents(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
        }

        function deselectAllStudents(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
        }

        function printSelectedSeatCards(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox:checked');
            const rollNumbers = [];

            checkboxes.forEach(cb => {
                rollNumbers.push(cb.value);
            });

            if (rollNumbers.length === 0) {
                alert('কোন শিক্ষার্থী নির্বাচন করা হয়নি!');
                return;
            }

            const url = 'working_seat_cards.php?rolls=' + rollNumbers.join(',') + '&complete_only=1';
            window.open(url, '_blank');
        }

        // Toggle all checkboxes
        function toggleAllCheckboxes(tableId, masterCheckbox) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = masterCheckbox.checked;
            });
        }
    </script>
</body>
</html>
