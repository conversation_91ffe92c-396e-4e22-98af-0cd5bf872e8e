<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'exmm');

class Database {
    private $host = DB_HOST;
    private $username = DB_USERNAME;
    private $password = DB_PASSWORD;
    private $database = DB_NAME;
    private $connection;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        $maxRetries = 3;
        $retryDelay = 2; // seconds

        for ($i = 0; $i < $maxRetries; $i++) {
            try {
                // First try to connect to MySQL server without database
                $tempConnection = new PDO(
                    "mysql:host={$this->host};charset=utf8mb4",
                    $this->username,
                    $this->password,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );

                // Check if database exists, create if not
                $stmt = $tempConnection->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '{$this->database}'");
                if (!$stmt->fetch()) {
                    $tempConnection->exec("CREATE DATABASE `{$this->database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                }

                // Now connect to the specific database
                $this->connection = new PDO(
                    "mysql:host={$this->host};dbname={$this->database};charset=utf8mb4",
                    $this->username,
                    $this->password,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false,
                        PDO::ATTR_TIMEOUT => 10
                    ]
                );

                // Create students table if it doesn't exist
                $this->createStudentsTableIfNotExists();

                return; // Success, exit retry loop

            } catch (PDOException $e) {
                if ($i === $maxRetries - 1) {
                    // Last retry failed
                    $this->handleConnectionError($e);
                } else {
                    // Wait before retrying
                    sleep($retryDelay);
                }
            }
        }
    }

    private function createStudentsTableIfNotExists() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS `students` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `father_name` varchar(255) DEFAULT NULL,
                `gender` enum('Male','Female','Other') DEFAULT 'Male',
                `roll` varchar(50) NOT NULL,
                `registration` varchar(100) DEFAULT NULL,
                `department` varchar(100) DEFAULT NULL,
                `academic_year` varchar(20) DEFAULT NULL,
                `student_type` enum('Regular','Irregular','Improvement') DEFAULT 'Regular',
                `subjects` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_roll` (`roll`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            $this->connection->exec($sql);
        } catch (PDOException $e) {
            // Table creation failed, but don't stop the application
            error_log("Failed to create students table: " . $e->getMessage());
        }
    }

    private function handleConnectionError($e) {
        $errorMessage = "Database connection failed: " . $e->getMessage();
        $troubleshootingInfo = "
        <div style='font-family: Arial, sans-serif; padding: 20px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px;'>
            <h3 style='color: #dc3545;'>🚨 Database Connection Error</h3>
            <p><strong>Error:</strong> {$errorMessage}</p>
            <h4>🔧 Quick Solutions:</h4>
            <ol>
                <li><strong>Start XAMPP:</strong> Open XAMPP Control Panel and start Apache & MySQL services</li>
                <li><strong>Check MySQL Port:</strong> Ensure MySQL is running on port 3306</li>
                <li><strong>Run Auto-Fix:</strong> <a href='auto_startup_fix.bat' style='color: #007bff;'>Click here to download auto-fix script</a></li>
                <li><strong>Manual Check:</strong> <a href='system_startup_check.php' style='color: #007bff;'>Open system check page</a></li>
            </ol>
            <p><a href='db_connection_test.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔍 Test Database Connection</a></p>
        </div>";

        die($troubleshootingInfo);
    }

    public function getConnection() {
        return $this->connection;
    }

    public function closeConnection() {
        $this->connection = null;
    }
}
?>
