<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';
$studentInfo = null;

// Get student ID from URL
$studentId = $_GET['id'] ?? null;

if (!$studentId) {
    header('Location: view_students.php');
    exit;
}

// Get student information
$student = new Student();
$studentInfo = $student->getById($studentId);

if (!$studentInfo) {
    header('Location: view_students.php');
    exit;
}

// If subjects field exists but individual sub_* fields are empty, populate them
if (!empty($studentInfo['subjects']) && empty($studentInfo['sub_1'])) {
    $subjects = explode(',', $studentInfo['subjects']);
    for ($i = 0; $i < count($subjects) && $i < 13; $i++) {
        $studentInfo["sub_" . ($i + 1)] = trim($subjects[$i]);
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $data = [
            'c_code' => $_POST['c_code'] ?? '',
            'eiin' => $_POST['eiin'] ?? '',
            'roll_no' => $_POST['roll_no'] ?? '',
            'reg_no' => $_POST['reg_no'] ?? '',
            'session' => $_POST['session'] ?? '',
            'type' => $_POST['type'] ?? '',
            'group_name' => $_POST['group_name'] ?? '',
            'student_name' => $_POST['student_name'] ?? '',
            'father_name' => $_POST['father_name'] ?? '',
            'gender' => $_POST['gender'] ?? '',
            'sub_1' => $_POST['sub_1'] ?? '',
            'sub_2' => $_POST['sub_2'] ?? '',
            'sub_3' => $_POST['sub_3'] ?? '',
            'sub_4' => $_POST['sub_4'] ?? '',
            'sub_5' => $_POST['sub_5'] ?? '',
            'sub_6' => $_POST['sub_6'] ?? '',
            'sub_7' => $_POST['sub_7'] ?? '',
            'sub_8' => $_POST['sub_8'] ?? '',
            'sub_9' => $_POST['sub_9'] ?? '',
            'sub_10' => $_POST['sub_10'] ?? '',
            'sub_11' => $_POST['sub_11'] ?? '',
            'sub_12' => $_POST['sub_12'] ?? '',
            'sub_13' => $_POST['sub_13'] ?? ''
        ];
        
        if (empty($data['student_name'])) {
            throw new Exception('Student name is required');
        }
        
        if ($student->update($studentId, $data)) {
            $message = 'স্টুডেন্ট তথ্য সফলভাবে আপডেট হয়েছে!';
            $messageType = 'success';
            // Refresh student info
            $studentInfo = $student->getById($studentId);
        } else {
            $message = 'স্টুডেন্ট তথ্য আপডেট করতে সমস্যা হয়েছে।';
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্টুডেন্ট এডিট করুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-edit"></i> স্টুডেন্ট এডিট করুন</h3>
                            <div>
                                <a href="view_students.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> ফিরে যান
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                    <?php echo htmlspecialchars($message); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <form method="POST">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="c_code" class="form-label">C.Code</label>
                                        <input type="text" class="form-control" id="c_code" name="c_code"
                                               value="<?php echo htmlspecialchars($studentInfo['c_code'] ?? '295'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="eiin" class="form-label">EIIN</label>
                                        <input type="text" class="form-control" id="eiin" name="eiin"
                                               value="<?php echo htmlspecialchars($studentInfo['eiin'] ?? '136257'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="roll_no" class="form-label">Roll No.</label>
                                        <input type="text" class="form-control" id="roll_no" name="roll_no"
                                               value="<?php echo htmlspecialchars($studentInfo['roll'] ?? $studentInfo['roll_no'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="reg_no" class="form-label">Reg. No.</label>
                                        <input type="text" class="form-control" id="reg_no" name="reg_no"
                                               value="<?php echo htmlspecialchars($studentInfo['registration'] ?? $studentInfo['reg_no'] ?? ''); ?>">
                                    </div>
                                </div>

                                <div class="row g-3 mt-2">
                                    <div class="col-md-3">
                                        <label for="session" class="form-label">Session</label>
                                        <input type="text" class="form-control" id="session" name="session"
                                               value="<?php echo htmlspecialchars($studentInfo['academic_year'] ?? $studentInfo['session'] ?? '2024'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="type" class="form-label">Type</label>
                                        <input type="text" class="form-control" id="type" name="type"
                                               value="<?php echo htmlspecialchars($studentInfo['student_type'] ?? $studentInfo['type'] ?? 'Regular'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="group_name" class="form-label">Group</label>
                                        <input type="text" class="form-control" id="group_name" name="group_name"
                                               value="<?php echo htmlspecialchars($studentInfo['department'] ?? $studentInfo['group_name'] ?? 'Science'); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="gender" class="form-label">Gender</label>
                                        <select class="form-control" id="gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="Male" <?php echo (($studentInfo['gender'] ?? 'Male') === 'Male') ? 'selected' : ''; ?>>Male</option>
                                            <option value="Female" <?php echo (($studentInfo['gender'] ?? 'Male') === 'Female') ? 'selected' : ''; ?>>Female</option>
                                            <option value="Other" <?php echo (($studentInfo['gender'] ?? 'Male') === 'Other') ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="row g-3 mt-2">
                                    <div class="col-md-6">
                                        <label for="student_name" class="form-label">Student Name *</label>
                                        <input type="text" class="form-control" id="student_name" name="student_name" required
                                               value="<?php echo htmlspecialchars($studentInfo['name'] ?? $studentInfo['student_name'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label for="father_name" class="form-label">Father Name</label>
                                        <input type="text" class="form-control" id="father_name" name="father_name"
                                               value="<?php echo htmlspecialchars($studentInfo['father_name'] ?? ''); ?>">
                                    </div>
                                </div>

                                <h5 class="mt-4">Subjects</h5>
                                <div class="row g-3">
                                    <?php for ($i = 1; $i <= 13; $i++): ?>
                                        <div class="col-md-4">
                                            <label for="sub_<?php echo $i; ?>" class="form-label">Subject <?php echo $i; ?></label>
                                            <input type="text" class="form-control" id="sub_<?php echo $i; ?>" name="sub_<?php echo $i; ?>"
                                                   value="<?php echo htmlspecialchars($studentInfo["sub_$i"] ?? ''); ?>">
                                        </div>
                                    <?php endfor; ?>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save"></i> আপডেট করুন
                                    </button>
                                    <a href="view_students.php" class="btn btn-secondary btn-lg">
                                        <i class="fas fa-times"></i> বাতিল করুন
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
