<?php
require_once 'models/Student.php';

// Get all students with exactly 4 subjects
$student = new Student();
$allStudents = $student->getAll();

$fourSubjectStudents = [];
foreach ($allStudents as $s) {
    $subjectCount = 0;

    // Check new subjects field format first
    if (!empty($s['subjects'])) {
        $subjects = explode(',', $s['subjects']);
        $subjectCount = count(array_filter($subjects, function($subject) {
            return !empty(trim($subject));
        }));
    } else {
        // Fallback to old format for backward compatibility
        for ($i = 1; $i <= 13; $i++) {
            if (!empty($s["sub_$i"])) {
                $subjectCount++;
            }
        }
    }

    if ($subjectCount == 4) {
        $s['subject_count'] = $subjectCount;
        $fourSubjectStudents[] = $s;
    }
}

// Sort by roll number
usort($fourSubjectStudents, function($a, $b) {
    $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
    $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
    return $rollA - $rollB;
});

$totalStudents = count($fourSubjectStudents);

// Group by type
$regularStudents = array_filter($fourSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Regular';
});
$irregularStudents = array_filter($fourSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Irregular';
});
$improvementStudents = array_filter($fourSubjectStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Improvement';
});

// Group by group_name
$groupStats = [];
foreach ($fourSubjectStudents as $s) {
    $group = $s['department'] ?? $s['group_name'] ?? 'Unknown';
    if (!isset($groupStats[$group])) {
        $groupStats[$group] = [];
    }
    $groupStats[$group][] = $s;
}

// Sort groups
ksort($groupStats);

// Show all students (DataTables will handle pagination)
$studentsToShow = $fourSubjectStudents;
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>৪ বিষয়ের শিক্ষার্থী - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }
        
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }
        
        .btn {
            border-radius: 10px;
            font-weight: 500;
        }
        
        .pagination .page-link {
            border-radius: 10px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        
        .badge-subject-count {
            font-size: 0.9rem;
            padding: 0.5em 0.8em;
        }
        
        .subject-list {
            font-size: 0.85rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h3 class="mb-0">
                                        <i class="fas fa-user-graduate me-2"></i>
                                        ৪ বিষয়ের শিক্ষার্থী
                                    </h3>
                                    <p class="mb-0 text-white-50">যে সকল শিক্ষার্থীর ৪টি বিষয় রয়েছে</p>
                                </div>
                                <div class="col-auto">
                                    <div class="btn-group me-2" role="group">
                                        <button type="button" class="btn btn-success btn-lg dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-id-card me-2"></i>সীট কার্ড প্রিন্ট
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="working_seat_cards.php?subject_count=4" target="_blank">
                                                <i class="fas fa-users me-2"></i>সকল ৪ বিষয়ের সীটকার্ড
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">গ্রুপ ভিত্তিক</h6></li>
                                            <?php foreach ($groupStats as $group => $students): ?>
                                                <li><a class="dropdown-item" href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&subject_count=4" target="_blank">
                                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?> জন)
                                                </a></li>
                                            <?php endforeach; ?>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">টাইপ ভিত্তিক</h6></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Regular&subject_count=4" target="_blank">
                                                <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularStudents); ?> জন)
                                            </a></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Irregular&subject_count=4" target="_blank">
                                                <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularStudents); ?> জন)
                                            </a></li>
                                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Improvement&subject_count=4" target="_blank">
                                                <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementStudents); ?> জন)
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="seat_card_generator.php">
                                                <i class="fas fa-cogs me-2"></i>কাস্টম সীটকার্ড জেনারেটর
                                            </a></li>
                                        </ul>
                                    </div>
                                    <a href="index.php" class="btn btn-light">
                                        <i class="fas fa-home me-2"></i>হোম পেজ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $totalStudents; ?></div>
                        <h6 class="text-white-50 mb-0">মোট শিক্ষার্থী</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%);">
                        <div class="stats-number"><?php echo count($regularStudents); ?></div>
                        <h6 class="text-white-50 mb-0">নিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);">
                        <div class="stats-number"><?php echo count($irregularStudents); ?></div>
                        <h6 class="text-white-50 mb-0">অনিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);">
                        <div class="stats-number"><?php echo count($improvementStudents); ?></div>
                        <h6 class="text-white-50 mb-0">উন্নতি</h6>
                    </div>
                </div>
            </div>

            <!-- Group Statistics -->
            <?php if (!empty($groupStats)): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>গ্রুপ ভিত্তিক পরিসংখ্যান (৪ বিষয়)
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($groupStats as $group => $students): ?>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h4 class="text-primary"><?php echo count($students); ?></h4>
                                                <h6 class="card-title"><?php echo htmlspecialchars($group); ?></h6>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&subject_count=4"
                                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                                        <i class="fas fa-id-card"></i> সীট কার্ড
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Students List -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="row align-items-center">
                                <div class="col">
                                    <h5 class="mb-0">
                                        <i class="fas fa-users me-2"></i>৪ বিষয়ের শিক্ষার্থী তালিকা
                                        <span class="badge bg-primary ms-2"><?php echo $totalStudents; ?></span>
                                    </h5>
                                </div>
                                <div class="col-auto">
                                    <a href="working_seat_cards.php?subject_count=4" class="btn btn-success" target="_blank">
                                        <i class="fas fa-id-card me-2"></i>সকল সীটকার্ড
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($fourSubjectStudents)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">কোন শিক্ষার্থী পাওয়া যায়নি</h5>
                                    <p class="text-muted">৪ বিষয়ের কোন শিক্ষার্থী খুঁজে পাওয়া যায়নি।</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover mb-0" id="studentsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Roll No.</th>
                                                <th>Student Name</th>
                                                <th>Type</th>
                                                <th>Group</th>
                                                <th>বিষয় সংখ্যা</th>
                                                <th>বিষয়সমূহ</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($fourSubjectStudents as $student): ?>
                                                <tr>
                                                    <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? ''); ?></strong></td>
                                                    <td><strong><?php echo htmlspecialchars($student['name'] ?? $student['student_name'] ?? ''); ?></strong></td>
                                                    <td>
                                                        <?php
                                                        $type = $student['student_type'] ?? $student['type'] ?? '';
                                                        $badgeClass = '';
                                                        switch($type) {
                                                            case 'Regular': $badgeClass = 'bg-success'; break;
                                                            case 'Irregular': $badgeClass = 'bg-danger'; break;
                                                            case 'Improvement': $badgeClass = 'bg-warning'; break;
                                                            default: $badgeClass = 'bg-secondary';
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($type); ?></span>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($student['department'] ?? $student['group_name'] ?? ''); ?></td>
                                                    <td>
                                                        <span class="badge badge-subject-count bg-primary">৪/১৩</span>
                                                    </td>
                                                    <td>
                                                        <div class="subject-list">
                                                            <?php
                                                            $subjects = [];

                                                            // Check new subjects field format first
                                                            if (!empty($student['subjects'])) {
                                                                $subjects = explode(',', $student['subjects']);
                                                                $subjects = array_filter($subjects, function($s) {
                                                                    return !empty(trim($s));
                                                                });
                                                            } else {
                                                                // Fallback to old format
                                                                for ($i = 1; $i <= 13; $i++) {
                                                                    $subField = 'sub_' . $i;
                                                                    if (!empty($student[$subField])) {
                                                                        $subjects[] = $student[$subField];
                                                                    }
                                                                }
                                                            }

                                                            echo htmlspecialchars(implode(', ', $subjects));
                                                            ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <a href="working_seat_cards.php?search=<?php echo urlencode($student['name'] ?? $student['student_name'] ?? ''); ?>"
                                                               class="btn btn-sm btn-primary" target="_blank">
                                                                <i class="fas fa-id-card"></i> Seat Card
                                                            </a>
                                                            <a href="working_seat_cards.php?rolls=<?php echo urlencode($student['roll'] ?? $student['roll_no'] ?? ''); ?>&subject_count=4"
                                                               class="btn btn-sm btn-success" target="_blank">
                                                                <i class="fas fa-print"></i> Print
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#studentsTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.11.5/i18n/bn.json"
                },
                "pageLength": 25,
                "order": [[ 0, "asc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": [6] }
                ]
            });
        });
    </script>
</body>
</html>
