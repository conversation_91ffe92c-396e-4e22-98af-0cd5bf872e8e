<?php
// update_duty_assignments.php
// Assigned Duty Update Page (Frontend Only)

session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
$redirectUrl = 'duty_assignments_management.php?tab=bulk-update';

// Validate form submission
if (!isset($_POST['duty_date']) || empty($_POST['duty_date'])) {
    $_SESSION['message'] = 'তারিখ নির্বাচন করুন!';
    $_SESSION['message_type'] = 'danger';
    header("Location: $redirectUrl");
    exit;
}

if (!isset($_POST['bulk_action']) || empty($_POST['bulk_action'])) {
    $_SESSION['message'] = 'অ্যাকশন নির্বাচন করুন!';
    $_SESSION['message_type'] = 'danger';
    header("Location: $redirectUrl");
    exit;
}

if (!isset($_POST['selected_teachers']) || empty($_POST['selected_teachers'])) {
    $_SESSION['message'] = 'কমপক্ষে একজন শিক্ষক নির্বাচন করুন!';
    $_SESSION['message_type'] = 'danger';
    header("Location: $redirectUrl");
    exit;
}

// Get form data
$dutyDate = $_POST['duty_date'];
$bulkAction = $_POST['bulk_action'];
$selectedTeachers = $_POST['selected_teachers'];
$roomAssignments = $_POST['room_assignments'] ?? [];

try {
    $pdo->beginTransaction();
    
    // Process based on action
    switch ($bulkAction) {
        case 'add':
            // Add teachers to duty
            foreach ($selectedTeachers as $teacherId) {
                $roomNumber = $roomAssignments[$teacherId] ?? null;
                
                // Check if teacher already has duty on this date
                if (!$teacherManager->checkTeacherDutyExists($teacherId, $dutyDate)) {
                    $teacherManager->assignTeacherToDuty($teacherId, $dutyDate, $roomNumber);
                } else {
                    // Update room if teacher already has duty
                    $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomNumber);
                }
            }
            $message = 'ডিউটি সফলভাবে সংরক্ষণ করা হয়েছে!';
            break;
            
        case 'remove':
            // Remove teachers from duty
            foreach ($selectedTeachers as $teacherId) {
                $teacherManager->removeTeacherFromDuty($teacherId, $dutyDate);
            }
            $message = 'ডিউটি সফলভাবে বাদ দেয়া হয়েছে!';
            break;
            
        case 'update_room':
            // Update room numbers only
            foreach ($selectedTeachers as $teacherId) {
                $roomNumber = $roomAssignments[$teacherId] ?? null;
                
                // Check if teacher already has duty on this date
                if ($teacherManager->checkTeacherDutyExists($teacherId, $dutyDate)) {
                    $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomNumber);
                } else {
                    // Assign duty if teacher doesn't have one
                    $teacherManager->assignTeacherToDuty($teacherId, $dutyDate, $roomNumber);
                }
            }
            $message = 'রুম নম্বর সফলভাবে আপডেট করা হয়েছে!';
            break;
            
        default:
            throw new Exception('অবৈধ অ্যাকশন!');
    }
    
    $pdo->commit();
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = 'success';
    
} catch (Exception $e) {
    $pdo->rollBack();
    $_SESSION['message'] = 'ত্রুটি: ' . $e->getMessage();
    $_SESSION['message_type'] = 'danger';
}

// Redirect back
header("Location: $redirectUrl");
exit;
?> 