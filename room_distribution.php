<?php
session_start();
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

// Get all duty dates for dropdown
$dutyDates = $teacherManager->getAllDutyDates();

// Handle room distribution form submission
if (isset($_POST['distribute_rooms'])) {
    $dutyDate = $_POST['duty_date'];
    $roomDistributionMethod = $_POST['distribution_method'];
    $startingRoom = $_POST['starting_room'] ?? '';
    $manualRoomAssignments = $_POST['room_assignments'] ?? [];
    $customRoomPrefix = $_POST['room_prefix'] ?? '';
    
    try {
        // Get all assignments for the selected date
        $assignments = $teacherManager->getDutyAssignments($dutyDate);
        
        if (empty($assignments)) {
            throw new Exception("এই তারিখে কোন শিক্ষক ডিউটিতে নিয়োজিত নেই!");
        }
        
        // Handle different room distribution methods
        if ($roomDistributionMethod === 'sequential') {
            // Sequential room numbers (e.g., 101, 102, 103...)
            $roomNumber = (int)$startingRoom;
            
            foreach ($assignments as $assignment) {
                $teacherId = $assignment['teacher_id'];
                $roomAssignment = $customRoomPrefix . $roomNumber;
                $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomAssignment);
                $roomNumber++;
            }
            
            $message = "সিকুয়েন্সিয়াল রুম বন্টন সফলভাবে সম্পন্ন হয়েছে!";
            $messageType = "success";
        } 
        elseif ($roomDistributionMethod === 'manual') {
            // Manual room assignment for each teacher
            foreach ($manualRoomAssignments as $teacherId => $roomNumber) {
                if (!empty($roomNumber)) {
                    $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomNumber);
                }
            }
            
            $message = "ম্যানুয়াল রুম বন্টন সফলভাবে সম্পন্ন হয়েছে!";
            $messageType = "success";
        }
        elseif ($roomDistributionMethod === 'odd_even') {
            // Odd-even room numbers (e.g., odd rooms on first floor, even on second)
            $roomNumber = (int)$startingRoom;
            
            foreach ($assignments as $assignment) {
                $teacherId = $assignment['teacher_id'];
                $roomAssignment = $customRoomPrefix . $roomNumber;
                $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomAssignment);
                $roomNumber += 2; // Increment by 2 for odd-even pattern
            }
            
            $message = "বিজোড়-জোড় রুম বন্টন সফলভাবে সম্পন্ন হয়েছে!";
            $messageType = "success";
        }
        elseif ($roomDistributionMethod === 'block') {
            // Block distribution (e.g., rooms 101-110, 201-210)
            $roomNumber = (int)$startingRoom;
            $blockSize = 10;
            $currentBlock = 1;
            $counter = 0;
            
            foreach ($assignments as $assignment) {
                $teacherId = $assignment['teacher_id'];
                $roomAssignment = $customRoomPrefix . $roomNumber;
                $teacherManager->updateTeacherRoom($teacherId, $dutyDate, $roomAssignment);
                
                $counter++;
                $roomNumber++;
                
                // Move to next block after blockSize teachers
                if ($counter % $blockSize === 0) {
                    $currentBlock++;
                    $roomNumber = ((int)($startingRoom / 100) + $currentBlock) * 100 + 1;
                }
            }
            
            $message = "ব্লক ভিত্তিক রুম বন্টন সফলভাবে সম্পন্ন হয়েছে!";
            $messageType = "success";
        }
        elseif ($roomDistributionMethod === 'clear') {
            // Clear all room assignments
            foreach ($assignments as $assignment) {
                $teacherId = $assignment['teacher_id'];
                $teacherManager->updateTeacherRoom($teacherId, $dutyDate, null);
            }
            
            $message = "সব রুম বন্টন পরিষ্কার করা হয়েছে!";
            $messageType = "warning";
        }
    } catch (Exception $e) {
        $message = "ত্রুটি: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Get selected date's assignments
$selectedDate = $_GET['date'] ?? '';
$assignments = [];

if (!empty($selectedDate)) {
    $assignments = $teacherManager->getDutyAssignments($selectedDate);
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>তারিখ ভিত্তিক রুম বন্টন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .distribution-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .distribution-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .distribution-card.active {
            border-color: #764ba2;
            background-color: #f0f4ff;
        }
        
        .date-selector {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .teacher-info {
            display: flex;
            align-items: center;
        }
        
        .teacher-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 10px;
            border: 2px solid #dee2e6;
        }
        
        .photo-placeholder {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-door-open me-3"></i>তারিখ ভিত্তিক রুম বন্টন
                </h1>
                <p class="text-white-50">নির্দিষ্ট তারিখের জন্য শিক্ষকদের রুম বন্টন করুন</p>
            </div>
            
            <!-- Message Display -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <strong><?php echo $message; ?></strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Date Selector -->
            <div class="date-selector">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">
                            <i class="fas fa-calendar-day me-2"></i>তারিখ নির্বাচন করুন
                        </h4>
                        <p class="mb-0">যে তারিখের জন্য রুম বন্টন করতে চান সেই তারিখ নির্বাচন করুন</p>
                    </div>
                    <div class="col-md-4">
                        <form method="GET" class="d-flex">
                            <select name="date" class="form-select me-2" required>
                                <option value="">তারিখ নির্বাচন করুন</option>
                                <?php foreach ($dutyDates as $date): ?>
                                    <option value="<?php echo $date; ?>" <?php echo ($date == $selectedDate) ? 'selected' : ''; ?>>
                                        <?php echo formatDateBengali($date); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <?php if ($selectedDate): ?>
                <?php if (empty($assignments)): ?>
                    <div class="main-card">
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <h5>কোন শিক্ষক পাওয়া যায়নি!</h5>
                            <p>এই তারিখে কোন শিক্ষক ডিউটিতে নিয়োজিত নেই। প্রথমে শিক্ষক নিয়োগ করুন।</p>
                            <a href="date_wise_duty_assignment.php?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-warning">
                                <i class="fas fa-users me-2"></i>শিক্ষক নিয়োগ করুন
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Room Distribution Options -->
                    <div class="main-card">
                        <h4 class="mb-4">
                            <i class="fas fa-door-open text-primary me-2"></i>
                            <?php echo formatDateBengali($selectedDate); ?> তারিখের রুম বন্টন পদ্ধতি
                        </h4>
                        
                        <form method="POST">
                            <input type="hidden" name="duty_date" value="<?php echo $selectedDate; ?>">
                            
                            <div class="row">
                                <!-- Sequential Distribution -->
                                <div class="col-md-6 mb-3">
                                    <div class="distribution-card" id="sequential-card" onclick="selectDistributionMethod('sequential')">
                                        <h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       id="sequential" value="sequential" checked>
                                                <label class="form-check-label" for="sequential">
                                                    <i class="fas fa-sort-numeric-up me-2"></i>সিকুয়েন্সিয়াল রুম বন্টন
                                                </label>
                                            </div>
                                        </h5>
                                        <p class="text-muted small">ক্রমানুসারে রুম নম্বর বন্টন করুন (১০১, ১০২, ১০৩...)</p>
                                        
                                        <div class="sequential-options">
                                            <div class="mb-3">
                                                <label for="starting_room" class="form-label">শুরুর রুম নম্বর</label>
                                                <input type="number" class="form-control" id="starting_room" 
                                                       name="starting_room" value="101" min="1">
                                            </div>
                                            <div class="mb-3">
                                                <label for="room_prefix" class="form-label">রুম প্রিফিক্স (ঐচ্ছিক)</label>
                                                <input type="text" class="form-control" id="room_prefix" 
                                                       name="room_prefix" placeholder="যেমন: B-">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Odd-Even Distribution -->
                                <div class="col-md-6 mb-3">
                                    <div class="distribution-card" id="odd-even-card" onclick="selectDistributionMethod('odd_even')">
                                        <h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       id="odd_even" value="odd_even">
                                                <label class="form-check-label" for="odd_even">
                                                    <i class="fas fa-random me-2"></i>বিজোড়-জোড় রুম বন্টন
                                                </label>
                                            </div>
                                        </h5>
                                        <p class="text-muted small">বিজোড়-জোড় রুম নম্বর বন্টন (১০১, ১০৩, ১০৫... বা ১০২, ১০৪, ১০৬...)</p>
                                        
                                        <div class="odd-even-options">
                                            <div class="mb-3">
                                                <label for="starting_room_odd" class="form-label">শুরুর রুম নম্বর</label>
                                                <input type="number" class="form-control" id="starting_room_odd" 
                                                       value="101" min="1" disabled
                                                       onchange="document.getElementById('starting_room').value = this.value">
                                                <div class="form-text">বিজোড় সংখ্যা দিয়ে শুরু করলে বিজোড় রুম, জোড় সংখ্যা দিয়ে শুরু করলে জোড় রুম</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Block Distribution -->
                                <div class="col-md-6 mb-3">
                                    <div class="distribution-card" id="block-card" onclick="selectDistributionMethod('block')">
                                        <h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       id="block" value="block">
                                                <label class="form-check-label" for="block">
                                                    <i class="fas fa-th-large me-2"></i>ব্লক ভিত্তিক রুম বন্টন
                                                </label>
                                            </div>
                                        </h5>
                                        <p class="text-muted small">ব্লক অনুযায়ী রুম নম্বর (১০১-১১০, ২০১-২১০...)</p>
                                        
                                        <div class="block-options">
                                            <div class="mb-3">
                                                <label for="starting_room_block" class="form-label">শুরুর রুম নম্বর</label>
                                                <input type="number" class="form-control" id="starting_room_block" 
                                                       value="101" min="1" disabled
                                                       onchange="document.getElementById('starting_room').value = this.value">
                                                <div class="form-text">প্রতি ব্লকে ১০টি রুম থাকবে (যেমন: ১০১-১১০, ২০১-২১০)</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Manual Distribution -->
                                <div class="col-md-6 mb-3">
                                    <div class="distribution-card" id="manual-card" onclick="selectDistributionMethod('manual')">
                                        <h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       id="manual" value="manual">
                                                <label class="form-check-label" for="manual">
                                                    <i class="fas fa-edit me-2"></i>ম্যানুয়াল রুম বন্টন
                                                </label>
                                            </div>
                                        </h5>
                                        <p class="text-muted small">প্রত্যেক শিক্ষকের জন্য আলাদাভাবে রুম নম্বর বরাদ্দ করুন</p>
                                        <div id="manual-toggle-btn">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleManualAssignment()">
                                                <i class="fas fa-edit me-2"></i>ম্যানুয়াল অ্যাসাইনমেন্ট দেখুন
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Clear All Rooms -->
                                <div class="col-md-12 mb-3">
                                    <div class="distribution-card" id="clear-card" onclick="selectDistributionMethod('clear')">
                                        <h5>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="distribution_method" 
                                                       id="clear" value="clear">
                                                <label class="form-check-label" for="clear">
                                                    <i class="fas fa-trash me-2"></i>সব রুম পরিষ্কার করুন
                                                </label>
                                            </div>
                                        </h5>
                                        <p class="text-muted small text-danger">সাবধান: এই অপশন বেছে নিলে এই তারিখের সব শিক্ষকের রুম বরাদ্দ বাতিল হয়ে যাবে</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Manual Assignment Table (Hidden by Default) -->
                            <div id="manual-assignment-section" class="mt-4" style="display: none;">
                                <h5 class="mb-3">
                                    <i class="fas fa-user-edit me-2"></i>শিক্ষক অনুসারে রুম বরাদ্দ করুন
                                </h5>
                                
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>শিক্ষক</th>
                                                <th>বিষয়</th>
                                                <th>বর্তমান রুম</th>
                                                <th>নতুন রুম নম্বর</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $i = 1; foreach ($assignments as $assignment): ?>
                                                <tr>
                                                    <td><?php echo $i++; ?></td>
                                                    <td>
                                                        <div class="teacher-info">
                                                            <?php if (!empty($assignment['photo']) && file_exists($assignment['photo'])): ?>
                                                                <img src="<?php echo htmlspecialchars($assignment['photo']); ?>" 
                                                                     class="teacher-photo" alt="Teacher Photo">
                                                            <?php else: ?>
                                                                <div class="photo-placeholder">
                                                                    <i class="fas fa-user text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            <div>
                                                                <?php echo htmlspecialchars($assignment['teacher_name']); ?>
                                                                <div class="small text-muted"><?php echo htmlspecialchars($assignment['designation']); ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($assignment['subject']); ?></td>
                                                    <td>
                                                        <?php if (!empty($assignment['room_number'])): ?>
                                                            <span class="badge bg-info"><?php echo htmlspecialchars($assignment['room_number']); ?></span>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">রুম বরাদ্দ নেই</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <input type="text" name="room_assignments[<?php echo $assignment['teacher_id']; ?>]" 
                                                               class="form-control" placeholder="রুম নম্বর লিখুন" 
                                                               value="<?php echo htmlspecialchars($assignment['room_number'] ?? ''); ?>">
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button type="submit" name="distribute_rooms" class="btn btn-lg btn-custom">
                                    <i class="fas fa-check-circle me-2"></i>রুম বন্টন করুন
                                </button>
                                <a href="date_wise_duty.php?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-eye me-2"></i>বর্তমান বন্টন দেখুন
                                </a>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="main-card">
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-day fa-4x text-muted mb-3"></i>
                        <h4>তারিখ নির্বাচন করুন</h4>
                        <p class="text-muted">রুম বন্টন করার জন্য উপরে থেকে একটি তারিখ নির্বাচন করুন।</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectDistributionMethod(method) {
            // Reset all cards
            document.querySelectorAll('.distribution-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // Disable all method-specific input fields
            document.getElementById('starting_room_odd').disabled = true;
            document.getElementById('starting_room_block').disabled = true;
            
            // Select the clicked card and radio button
            document.getElementById(method + '-card').classList.add('active');
            document.getElementById(method).checked = true;
            
            // Enable method-specific input fields
            if (method === 'odd_even') {
                document.getElementById('starting_room_odd').disabled = false;
            } else if (method === 'block') {
                document.getElementById('starting_room_block').disabled = false;
            }
            
            // Toggle manual assignment section
            if (method === 'manual') {
                document.getElementById('manual-assignment-section').style.display = 'block';
            } else {
                document.getElementById('manual-assignment-section').style.display = 'none';
            }
        }
        
        function toggleManualAssignment() {
            const manualSection = document.getElementById('manual-assignment-section');
            const toggleButton = document.getElementById('manual-toggle-btn').querySelector('button');
            
            if (manualSection.style.display === 'none') {
                manualSection.style.display = 'block';
                toggleButton.innerHTML = '<i class="fas fa-times me-2"></i>ম্যানুয়াল অ্যাসাইনমেন্ট লুকান';
                selectDistributionMethod('manual');
            } else {
                manualSection.style.display = 'none';
                toggleButton.innerHTML = '<i class="fas fa-edit me-2"></i>ম্যানুয়াল অ্যাসাইনমেন্ট দেখুন';
            }
        }
        
        // Initialize the first distribution method as active
        document.addEventListener('DOMContentLoaded', function() {
            selectDistributionMethod('sequential');
        });
    </script>
</body>
</html> 