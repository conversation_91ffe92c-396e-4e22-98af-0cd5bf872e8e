<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['student_ids']) && is_array($_POST['student_ids'])) {
        try {
            $student = new Student();
            $studentIds = $_POST['student_ids'];
            
            if ($student->bulkDelete($studentIds)) {
                $count = count($studentIds);
                $message = "{$count} জন স্টুডেন্ট সফলভাবে মুছে ফেলা হয়েছে!";
                $messageType = 'success';
            } else {
                $message = 'স্টুডেন্ট মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'কোন স্টুডেন্ট নির্বাচিত নেই।';
        $messageType = 'warning';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulk Delete Result - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .result-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="card result-card">
                        <div class="card-header bg-<?php echo $messageType; ?> text-white text-center">
                            <h3>
                                <?php if ($messageType === 'success'): ?>
                                    <i class="fas fa-check-circle"></i> সফল
                                <?php elseif ($messageType === 'danger'): ?>
                                    <i class="fas fa-exclamation-triangle"></i> ত্রুটি
                                <?php else: ?>
                                    <i class="fas fa-info-circle"></i> তথ্য
                                <?php endif; ?>
                            </h3>
                        </div>
                        <div class="card-body text-center">
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <h5><?php echo htmlspecialchars($message); ?></h5>
                            </div>

                            <div class="mt-4">
                                <a href="view_students.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-left"></i> স্টুডেন্ট লিস্টে ফিরে যান
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto redirect after 3 seconds
        setTimeout(function() {
            window.location.href = 'view_students.php';
        }, 3000);
    </script>
</body>
</html>
