<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';
$uploadResult = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        $file = $_FILES['csv_file'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error occurred.');
        }
        
        // Check file size (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            throw new Exception('File size too large. Maximum 10MB allowed.');
        }
        
        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'csv') {
            throw new Exception('Only CSV files are allowed for this upload method.');
        }
        
        // Move uploaded file
        $uploadDir = __DIR__ . '/uploads/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        $fileName = time() . '_' . $file['name'];
        $filePath = $uploadDir . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            // Process CSV file
            $students = [];
            $errors = [];
            $rowNumber = 1;
            
            if (($handle = fopen($filePath, 'r')) !== FALSE) {
                // Skip header row
                $header = fgetcsv($handle);
                $rowNumber = 2;
                
                while (($row = fgetcsv($handle)) !== FALSE) {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        $rowNumber++;
                        continue;
                    }
                    
                    // Map CSV columns to database fields
                    $studentData = [
                        'c_code' => trim($row[0] ?? ''),
                        'eiin' => trim($row[1] ?? ''),
                        'roll_no' => trim($row[2] ?? ''),
                        'reg_no' => trim($row[3] ?? ''),
                        'session' => trim($row[4] ?? ''),
                        'type' => trim($row[5] ?? ''),
                        'group_name' => trim($row[6] ?? ''),
                        'student_name' => trim($row[7] ?? ''),
                        'father_name' => trim($row[8] ?? ''),
                        'gender' => trim($row[9] ?? ''),
                        'sub_1' => trim($row[10] ?? ''),
                        'sub_2' => trim($row[11] ?? ''),
                        'sub_3' => trim($row[12] ?? ''),
                        'sub_4' => trim($row[13] ?? ''),
                        'sub_5' => trim($row[14] ?? ''),
                        'sub_6' => trim($row[15] ?? ''),
                        'sub_7' => trim($row[16] ?? ''),
                        'sub_8' => trim($row[17] ?? ''),
                        'sub_9' => trim($row[18] ?? ''),
                        'sub_10' => trim($row[19] ?? ''),
                        'sub_11' => trim($row[20] ?? ''),
                        'sub_12' => trim($row[21] ?? ''),
                        'sub_13' => trim($row[22] ?? '')
                    ];
                    
                    // Validate required fields
                    if (empty($studentData['student_name'])) {
                        $errors[] = "Row {$rowNumber}: Student name is required.";
                    } else {
                        // Validate gender
                        if (!empty($studentData['gender']) && !in_array($studentData['gender'], ['Male', 'Female', 'Other'])) {
                            $errors[] = "Row {$rowNumber}: Invalid gender value. Use 'Male', 'Female', or 'Other'.";
                        }
                        
                        if (empty($errors)) {
                            $students[] = $studentData;
                        }
                    }
                    
                    $rowNumber++;
                }
                fclose($handle);
            }
            
            // Save students to database
            if (!empty($students)) {
                $student = new Student();
                if ($student->bulkInsert($students)) {
                    $count = count($students);
                    $message = "সফলভাবে {$count} জন স্টুডেন্ট আপলোড হয়েছে!";
                    $messageType = 'success';
                    
                    if (!empty($errors)) {
                        $message .= '<br><br>কিছু সমস্যা ছিল:<br>' . implode('<br>', $errors);
                        $messageType = 'warning';
                    }
                } else {
                    $message = 'ডেটাবেসে স্টুডেন্ট সেভ করতে সমস্যা হয়েছে।';
                    $messageType = 'danger';
                }
            } else {
                $message = 'কোন valid স্টুডেন্ট ডেটা পাওয়া যায়নি।<br>' . implode('<br>', $errors);
                $messageType = 'danger';
            }
            
            // Clean up uploaded file
            unlink($filePath);
        } else {
            throw new Exception('ফাইল আপলোড করতে সমস্যা হয়েছে।');
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Upload - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .upload-area {
            border: 3px dashed #28a745;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(40,167,69,0.1);
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #1e7e34;
            background: rgba(40,167,69,0.2);
        }
        .upload-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-file-csv"></i> CSV File Upload (Simple)</h3>
                            <div>
                                <a href="download_csv_template.php" class="btn btn-info">
                                    <i class="fas fa-download"></i> Download CSV Template
                                </a>
                                <a href="upload.php" class="btn btn-warning">
                                    <i class="fas fa-file-excel"></i> Excel Upload
                                </a>
                                <a href="view_students.php" class="btn btn-secondary">
                                    <i class="fas fa-list"></i> View Students
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                    <?php echo $message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> CSV Upload (No Dependencies Required)</h5>
                                <p>এই পদ্ধতিতে শুধুমাত্র CSV ফাইল আপলোড করা যায়। Excel ফাইলের জন্য Composer dependencies প্রয়োজন।</p>
                            </div>

                            <form method="POST" enctype="multipart/form-data">
                                <div class="upload-area">
                                    <div class="upload-icon">
                                        <i class="fas fa-file-csv"></i>
                                    </div>
                                    <h4>CSV File Upload</h4>
                                    <p class="text-muted">Select CSV file to upload</p>
                                    <input type="file" name="csv_file" class="form-control mb-3" 
                                           accept=".csv" required>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-upload"></i> Upload CSV
                                    </button>
                                </div>
                            </form>

                            <!-- Instructions -->
                            <div class="row mt-5">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h5><i class="fas fa-check-circle"></i> CSV Requirements</h5>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <li><i class="fas fa-check text-success"></i> CSV files only (.csv)</li>
                                                <li><i class="fas fa-check text-success"></i> Maximum file size: 10MB</li>
                                                <li><i class="fas fa-check text-success"></i> First row should contain headers</li>
                                                <li><i class="fas fa-check text-success"></i> Student Name is required</li>
                                                <li><i class="fas fa-check text-success"></i> No Composer dependencies needed</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h5><i class="fas fa-list"></i> Column Order</h5>
                                        </div>
                                        <div class="card-body">
                                            <small>
                                                C.Code, EIIN, Roll No., Reg. No., Session, Type, Group, 
                                                Student Name, Father Name, Gender, Sub 1-13
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
