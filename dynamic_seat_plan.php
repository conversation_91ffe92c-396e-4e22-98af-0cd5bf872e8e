<?php
session_start();

// Include database connection
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
$uploadedStudents = [];

// Get PDO instance from teacherManager or global
if (isset($teacherManager) && property_exists($teacherManager, 'pdo')) {
    $pdo = $teacherManager->pdo;
} elseif (isset($pdo)) {
    // $pdo is already set from includes/teacher_db.php
} else {
    die('PDO instance not found.');
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        $file = $_FILES['csv_file'];

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('ফাইল আপলোড এরর হয়েছে।');
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'csv') {
            throw new Exception('শুধুমাত্র CSV ফাইল আপলোড করুন।');
        }

        // Process CSV file
        if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
            $header = fgetcsv($handle); // Skip header
            $students = [];

            while (($row = fgetcsv($handle)) !== FALSE) {
                if (!empty($row[0])) { // Assuming first column is roll number
                    $students[] = [
                        'roll_no' => trim($row[0]),
                        'student_name' => isset($row[1]) ? trim($row[1]) : '',
                        'reg_no' => isset($row[2]) ? trim($row[2]) : '',
                        'group_name' => isset($row[3]) ? trim($row[3]) : ''
                    ];
                }
            }
            fclose($handle);

            // Sort by roll number
            usort($students, function($a, $b) {
                return (int)$a['roll_no'] - (int)$b['roll_no'];
            });

            $uploadedStudents = $students;
            $_SESSION['uploaded_students'] = $students;
            $message = count($students) . ' জন শিক্ষার্থীর তথ্য সফলভাবে আপলোড হয়েছে!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
    }
}

// Get uploaded students from session
if (empty($uploadedStudents) && isset($_SESSION['uploaded_students'])) {
    $uploadedStudents = $_SESSION['uploaded_students'];
}

// Handle room configuration
if (isset($_POST['room_config'])) {
    $_SESSION['room_config'] = $_POST['room_config'];
}

$roomConfig = $_SESSION['room_config'] ?? [
    'room_name' => 'পরীক্ষা হল - ১',
    'total_columns' => 4,
    'arrangement_type' => 'serial'
];

// Handle column configurations
if (isset($_POST['column_configs'])) {
    $_SESSION['column_configs'] = $_POST['column_configs'];
}

$columnConfigs = $_SESSION['column_configs'] ?? [];

// Initialize default column configs if empty
if (empty($columnConfigs)) {
    for ($i = 0; $i < $roomConfig['total_columns']; $i++) {
        $columnConfigs[$i] = [
            'benches' => 8,
            'bench_type' => 'double',
            'small_benches' => ''
        ];
    }
}

// Handle seat plan generation
$seatPlan = [];
if (isset($_POST['generate_seat_plan']) && !empty($uploadedStudents)) {
    $seatPlan = generateSeatPlan($uploadedStudents, $roomConfig, $columnConfigs);
    $_SESSION['seat_plan'] = $seatPlan;
}

// Get seat plan from session
if (empty($seatPlan) && isset($_SESSION['seat_plan'])) {
    $seatPlan = $_SESSION['seat_plan'];
}

function generateSeatPlan($students, $roomConfig, $columnConfigs) {
    $plan = [];
    switch ($roomConfig['arrangement_type']) {
        case 'serial':
            $plan = generateSerialArrangement($students, $columnConfigs);
            break;
        case 'zigzag':
            $plan = generateZigzagArrangement($students, $columnConfigs);
            break;
        case 'diagonal':
            $plan = generateDiagonalArrangement($students, $columnConfigs);
            break;
        case 'alternate':
            $plan = generateAlternateArrangement($students, $columnConfigs);
            break;
        case 'custom_pattern':
            $plan = generateCustomPatternArrangement($students, $columnConfigs);
            break;
        default:
            $plan = generateSerialArrangement($students, $columnConfigs);
    }
    return $plan;
}

function generateSerialArrangement($students, $columnConfigs) {
    $plan = [];
    $studentIndex = 0;
    $totalStudents = count($students);

    foreach ($columnConfigs as $colIndex => $config) {
        $plan[$colIndex] = [];
        $defaultSeatsPerBench = ($config['bench_type'] === 'single') ? 1 : 2;
        
        // Process small_benches field if it exists and is not empty
        $smallBenches = [];
        if (!empty($config['small_benches'])) {
            $smallBenchesArray = explode(',', $config['small_benches']);
            foreach ($smallBenchesArray as $benchNum) {
                $benchNum = trim($benchNum);
                if (is_numeric($benchNum) && $benchNum > 0 && $benchNum <= $config['benches']) {
                    // Convert to zero-based index
                    $smallBenches[] = (int)$benchNum - 1;
                }
            }
        }

        for ($bench = 0; $bench < $config['benches']; $bench++) {
            $plan[$colIndex][$bench] = [];
            
            // Determine seats per bench - check if this bench should be different from default
            $seatsPerBench = $defaultSeatsPerBench;
            if (in_array($bench, $smallBenches)) {
                // If default is double, make this single; if default is single, make this double
                $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
            }

            for ($seat = 0; $seat < $seatsPerBench; $seat++) {
                if ($studentIndex < $totalStudents) {
                    $plan[$colIndex][$bench][$seat] = $students[$studentIndex];
                    $studentIndex++;
                } else {
                    $plan[$colIndex][$bench][$seat] = null;
                }
            }
        }
    }

    return $plan;
}

function generateZigzagArrangement($students, $columnConfigs) {
    // Similar to serial but alternates direction for each column
    $plan = [];
    $studentIndex = 0;
    $totalStudents = count($students);

    foreach ($columnConfigs as $colIndex => $config) {
        $plan[$colIndex] = [];
        $defaultSeatsPerBench = ($config['bench_type'] === 'single') ? 1 : 2;
        $reverse = ($colIndex % 2 === 1); // Reverse every other column
        
        // Process small_benches field if it exists and is not empty
        $smallBenches = [];
        if (!empty($config['small_benches'])) {
            $smallBenchesArray = explode(',', $config['small_benches']);
            foreach ($smallBenchesArray as $benchNum) {
                $benchNum = trim($benchNum);
                if (is_numeric($benchNum) && $benchNum > 0 && $benchNum <= $config['benches']) {
                    // Convert to zero-based index
                    $smallBenches[] = (int)$benchNum - 1;
                }
            }
        }

        $benchOrder = range(0, $config['benches'] - 1);
        if ($reverse) {
            $benchOrder = array_reverse($benchOrder);
        }

        foreach ($benchOrder as $bench) {
            $plan[$colIndex][$bench] = [];
            
            // Determine seats per bench - check if this bench should be different from default
            $seatsPerBench = $defaultSeatsPerBench;
            if (in_array($bench, $smallBenches)) {
                // If default is double, make this single; if default is single, make this double
                $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
            }

            for ($seat = 0; $seat < $seatsPerBench; $seat++) {
                if ($studentIndex < $totalStudents) {
                    $plan[$colIndex][$bench][$seat] = $students[$studentIndex];
                    $studentIndex++;
                } else {
                    $plan[$colIndex][$bench][$seat] = null;
                }
            }
        }
    }

    return $plan;
}

function generateDiagonalArrangement($students, $columnConfigs) {
    // Diagonal arrangement - students placed diagonally across columns
    $plan = [];
    $studentIndex = 0;
    $totalStudents = count($students);
    
    // Initialize all positions and process small_benches
    $smallBenchesMap = [];
    foreach ($columnConfigs as $colIndex => $config) {
        $plan[$colIndex] = [];
        $defaultSeatsPerBench = ($config['bench_type'] === 'single') ? 1 : 2;
        
        // Process small_benches field if it exists and is not empty
        $smallBenches = [];
        if (!empty($config['small_benches'])) {
            $smallBenchesArray = explode(',', $config['small_benches']);
            foreach ($smallBenchesArray as $benchNum) {
                $benchNum = trim($benchNum);
                if (is_numeric($benchNum) && $benchNum > 0 && $benchNum <= $config['benches']) {
                    // Convert to zero-based index
                    $smallBenches[] = (int)$benchNum - 1;
                }
            }
        }
        $smallBenchesMap[$colIndex] = $smallBenches;
        
        for ($bench = 0; $bench < $config['benches']; $bench++) {
            $plan[$colIndex][$bench] = [];
            
            // Determine seats per bench
            $seatsPerBench = $defaultSeatsPerBench;
            if (in_array($bench, $smallBenches)) {
                // If default is double, make this single; if default is single, make this double
                $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
            }
            
            for ($seat = 0; $seat < $seatsPerBench; $seat++) {
                $plan[$colIndex][$bench][$seat] = null;
            }
        }
    }
    
    // Fill diagonally
    $maxBenches = max(array_column($columnConfigs, 'benches'));
    $totalColumns = count($columnConfigs);
    
    for ($diagonal = 0; $diagonal < $maxBenches + $totalColumns - 1; $diagonal++) {
        for ($col = 0; $col < $totalColumns; $col++) {
            $bench = $diagonal - $col;
            
            if ($bench >= 0 && $bench < $columnConfigs[$col]['benches']) {
                $defaultSeatsPerBench = ($columnConfigs[$col]['bench_type'] === 'single') ? 1 : 2;
                
                // Determine seats per bench
                $seatsPerBench = $defaultSeatsPerBench;
                if (in_array($bench, $smallBenchesMap[$col])) {
                    // If default is double, make this single; if default is single, make this double
                    $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
                }
                
                for ($seat = 0; $seat < $seatsPerBench; $seat++) {
                    if ($studentIndex < $totalStudents) {
                        $plan[$col][$bench][$seat] = $students[$studentIndex];
                        $studentIndex++;
                    }
                }
            }
        }
    }
    
    return $plan;
}

function generateAlternateArrangement($students, $columnConfigs) {
    // Alternate arrangement - ensures consecutive roll numbers are not on same bench
    $plan = [];
    $studentIndex = 0;
    $totalStudents = count($students);
    
    // Initialize all positions and process small_benches
    $smallBenchesMap = [];
    foreach ($columnConfigs as $colIndex => $config) {
        $plan[$colIndex] = [];
        $defaultSeatsPerBench = ($config['bench_type'] === 'single') ? 1 : 2;
        
        // Process small_benches field if it exists and is not empty
        $smallBenches = [];
        if (!empty($config['small_benches'])) {
            $smallBenchesArray = explode(',', $config['small_benches']);
            foreach ($smallBenchesArray as $benchNum) {
                $benchNum = trim($benchNum);
                if (is_numeric($benchNum) && $benchNum > 0 && $benchNum <= $config['benches']) {
                    // Convert to zero-based index
                    $smallBenches[] = (int)$benchNum - 1;
                }
            }
        }
        $smallBenchesMap[$colIndex] = $smallBenches;
        
        for ($bench = 0; $bench < $config['benches']; $bench++) {
            $plan[$colIndex][$bench] = [];
            
            // Determine seats per bench
            $seatsPerBench = $defaultSeatsPerBench;
            if (in_array($bench, $smallBenches)) {
                // If default is double, make this single; if default is single, make this double
                $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
            }
            
            for ($seat = 0; $seat < $seatsPerBench; $seat++) {
                $plan[$colIndex][$bench][$seat] = null;
            }
        }
    }
    
    // Fill first seat of each bench first, then second seat
    for ($seatPosition = 0; $seatPosition < 2; $seatPosition++) {
        foreach ($columnConfigs as $colIndex => $config) {
            $defaultSeatsPerBench = ($config['bench_type'] === 'single') ? 1 : 2;
            
            for ($bench = 0; $bench < $config['benches']; $bench++) {
                // Determine seats per bench
                $seatsPerBench = $defaultSeatsPerBench;
                if (in_array($bench, $smallBenchesMap[$colIndex])) {
                    // If default is double, make this single; if default is single, make this double
                    $seatsPerBench = ($defaultSeatsPerBench === 2) ? 1 : 2;
                }
                
                if ($seatPosition < $seatsPerBench) {
                    if ($studentIndex < $totalStudents) {
                        $plan[$colIndex][$bench][$seatPosition] = $students[$studentIndex];
                        $studentIndex++;
                    }
                }
            }
        }
    }
    
    return $plan;
}

function generateCustomPatternArrangement($students, $columnConfigs) {
    $plan = [];
    $colCount = count($columnConfigs);
    $totalStudents = count($students);
    $studentIndex = 0;

    for ($col = 0; $col < $colCount; $col++) {
        $plan[$col] = [];
        $rowCount = $columnConfigs[$col]['benches'];
        $defaultPerBench = ($columnConfigs[$col]['bench_type'] === 'single') ? 1 : 2;
        
        // Process small_benches field if it exists and is not empty
        $smallBenches = [];
        if (!empty($columnConfigs[$col]['small_benches'])) {
            $smallBenchesArray = explode(',', $columnConfigs[$col]['small_benches']);
            foreach ($smallBenchesArray as $benchNum) {
                $benchNum = trim($benchNum);
                if (is_numeric($benchNum) && $benchNum > 0 && $benchNum <= $rowCount) {
                    // Convert to zero-based index
                    $smallBenches[] = (int)$benchNum - 1;
                }
            }
        }
        
        // Calculate total seats in this column considering mixed bench types
        $totalSeatsInColumn = 0;
        for ($row = 0; $row < $rowCount; $row++) {
            $perBench = $defaultPerBench;
            if (in_array($row, $smallBenches)) {
                $perBench = ($defaultPerBench === 2) ? 1 : 2;
            }
            $totalSeatsInColumn += $perBench;
        }
        
        // Initialize the plan with null values
        for ($row = 0; $row < $rowCount; $row++) {
            $plan[$col][$row] = [];
            $perBench = $defaultPerBench;
            if (in_array($row, $smallBenches)) {
                $perBench = ($defaultPerBench === 2) ? 1 : 2;
            }
            
            for ($seat = 0; $seat < $perBench; $seat++) {
                $plan[$col][$row][$seat] = null;
            }
        }
        
        // Fill seats in custom pattern
        $seatsFilled = 0;
        $maxSeats = min($totalSeatsInColumn, $totalStudents - $studentIndex);
        
        // First pass: fill first seat of each bench
        for ($row = 0; $row < $rowCount && $seatsFilled < $maxSeats; $row++) {
            $perBench = $defaultPerBench;
            if (in_array($row, $smallBenches)) {
                $perBench = ($defaultPerBench === 2) ? 1 : 2;
            }
            
            $plan[$col][$row][0] = $students[$studentIndex];
            $studentIndex++;
            $seatsFilled++;
        }
        
        // Second pass: fill second seat of double benches
        for ($row = 0; $row < $rowCount && $seatsFilled < $maxSeats; $row++) {
            $perBench = $defaultPerBench;
            if (in_array($row, $smallBenches)) {
                $perBench = ($defaultPerBench === 2) ? 1 : 2;
            }
            
            if ($perBench > 1) {
                $plan[$col][$row][1] = $students[$studentIndex];
                $studentIndex++;
                $seatsFilled++;
            }
        }
    }
    return $plan;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাইনামিক সীট প্লান - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .config-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .step-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .arrangement-option {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        .arrangement-option:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .arrangement-option.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #e9ecef;
        }
        .upload-area.dragover {
            border-color: #495057;
            background: #e9ecef;
        }
        .column-config {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .seat-plan-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .seat-plan-grid {
            display: flex;
            justify-content: center;
            gap: 32px;
            flex-wrap: nowrap;
        }
        .column-wrapper {
            flex: 0 0 22%;
            max-width: 22%;
            min-width: 150px;
            display: flex;
            flex-direction: column;
            align-items: stretch;
        }
        .column-header {
            background: #4f6ef7;
            color: #fff;
            font-size: 18px;
            font-weight: bold;
            padding: 8px;
            margin-bottom: 18px;
            border-radius: 8px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        .bench-row {
            display: flex;
            align-items: center;
            margin-bottom: 14px;
            padding: 3px 0;
            justify-content: center;
            width: 100%;
        }
        .bench-seats {
            display: flex;
            gap: 8px;
            justify-content: center;
            width: 100%;
        }
        .seat-box {
            min-width: 0;
            width: 100%;
            height: 48px;
            font-size: 22px;
            font-weight: 700;
            border: 3px solid #0057ff;
            background: #fff;
            color: #000;
            margin: 4px 0;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            box-sizing: border-box;
            white-space: nowrap;
            letter-spacing: 1px;
            box-shadow: none;
            text-align: center;
        }
        .seat-box.occupied:hover {
            box-shadow: 0 4px 16px 0 rgba(79,110,247,0.18);
        }
        .seat-box.empty {
            background: #fff;
            color: #ccc;
            border-style: dashed;
        }
        .bench-row small {
            font-size: 14px;
            font-weight: bold;
            min-width: 30px;
            color: #000;
            margin-right: 6px;
        }
        .row-numbers-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            margin-right: 18px;
        }
        .row-number-box {
            min-width: 36px;
            height: 40px;
            font-size: 18px;
            font-weight: bold;
            color: #444;
            background: #f2f2f2;
            border: 2px solid #bbb;
            border-radius: 6px;
            margin: 2px 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        @media print {
            /* Hide everything except the seat plan area */
            body * {
                visibility: hidden !important;
            }
            .print-area, .print-area * {
                visibility: visible !important;
            }
            .print-area {
                position: absolute !important;
                left: 0; right: 0; top: 0;
                width: 100vw !important;
                margin: 0 auto !important;
                background: #fff !important;
                z-index: 9999 !important;
                padding: 0 !important;
            }
            /* Keep seat plan grid layout and style */
            .seat-plan-grid {
                display: flex !important;
                flex-wrap: nowrap !important;
                gap: 20px !important;
                justify-content: center !important;
                align-items: flex-start !important;
                width: 100% !important;
                page-break-inside: avoid !important;
            }
            .column-wrapper {
                flex: 0 0 22% !important;
                max-width: 22% !important;
                min-width: 150px !important;
                display: flex !important;
                flex-direction: column !important;
                align-items: stretch !important;
                page-break-inside: avoid !important;
            }
            .column-header {
                background: #4f6ef7 !important;
                color: #fff !important;
                font-size: 18px !important;
                font-weight: bold !important;
                padding: 8px !important;
                margin-bottom: 18px !important;
                border-radius: 8px !important;
                text-align: center !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
            .bench-row {
                display: flex !important;
                align-items: center !important;
                margin-bottom: 14px !important;
                padding: 3px 0 !important;
                justify-content: center !important;
                width: 100% !important;
            }
            .bench-seats {
                display: flex !important;
                gap: 8px !important;
                justify-content: center !important;
                width: 100% !important;
            }
            .seat-box {
                min-width: 0 !important;
                width: 100% !important;
                height: 48px !important;
                font-size: 22px !important;
                font-weight: 700 !important;
                border: 3px solid #0057ff !important;
                background: #fff !important;
                color: #000 !important;
                margin: 4px 0 !important;
                border-radius: 14px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 0 !important;
                box-sizing: border-box !important;
                white-space: nowrap !important;
                letter-spacing: 1px !important;
                box-shadow: none !important;
                text-align: center !important;
            }
            .seat-box.empty {
                background: #fff !important;
                color: #ccc !important;
                border-style: dashed !important;
            }
            .bench-row small {
                font-size: 14px !important;
                font-weight: bold !important;
                min-width: 30px;
                color: #000 !important;
                margin-right: 6px !important;
            }
            body, html {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                background: #fff !important;
            }
            @page {
                margin: 4mm !important;
            }
            .seat-box::before, .seat-box::after {
                display: none !important;
                content: none !important;
            }
            .seat-box * {
                display: inline !important;
            }
            .row-numbers-column {
                margin-right: 12px !important;
            }
            .row-number-box {
                min-width: 36px !important;
                height: 40px !important;
                font-size: 18px !important;
                color: #222 !important;
                background: #fff !important;
                border: 2px solid #bbb !important;
                border-radius: 6px !important;
                margin: 2px 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="config-card">
                        <div class="text-center">
                            <h1><i class="fas fa-th-large text-primary"></i> ডাইনামিক সীট প্লান সিস্টেম</h1>
                            <p class="text-muted mb-0">রুম অনুযায়ী সীট বিন্যাস, জিগজ্যাগ, ডায়াগোনাল</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Step 1: CSV Upload -->
            <div class="config-card">
                <div class="step-header">
                    <h4><i class="fas fa-upload me-2"></i> ধাপ ১: শিক্ষার্থীদের CSV ফাইল আপলোড করুন</h4>
                </div>

                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="uploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                        <h5>CSV ফাইল এখানে ড্রাগ করুন অথবা ক্লিক করে নির্বাচন করুন</h5>
                        <p class="text-muted">ফাইল ফরম্যাট: রোল নং, নাম, রেজি. নং, গ্রুপ</p>
                        <input type="file" name="csv_file" id="csvFile" accept=".csv" style="display: none;" required>
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFile').click()">
                            <i class="fas fa-file-csv me-2"></i> ফাইল নির্বাচন করুন
                        </button>
                    </div>

                    <div id="fileInfo" style="display: none;" class="mt-3">
                        <div class="alert alert-info">
                            <strong>নির্বাচিত ফাইল:</strong> <span id="fileName"></span><br>
                            <strong>ফাইল সাইজ:</strong> <span id="fileSize"></span>
                        </div>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload me-2"></i> আপলোড করুন
                        </button>
                    </div>
                </form>

                <?php if (!empty($uploadedStudents)): ?>
                    <div class="mt-4">
                        <h6><i class="fas fa-check-circle text-success me-2"></i> আপলোড সম্পন্ন: <?php echo count($uploadedStudents); ?> জন শিক্ষার্থী</h6>
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>রোল</th>
                                        <th>নাম</th>
                                        <th>রেজি. নং</th>
                                        <th>গ্রুপ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($uploadedStudents, 0, 10) as $student): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($student['roll_no']); ?></td>
                                            <td><?php echo htmlspecialchars($student['student_name']); ?></td>
                                            <td><?php echo htmlspecialchars($student['reg_no']); ?></td>
                                            <td><?php echo htmlspecialchars($student['group_name']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <?php if (count($uploadedStudents) > 10): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">
                                                ... আরো <?php echo count($uploadedStudents) - 10; ?> জন শিক্ষার্থী
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Step 2: Room Configuration -->
            <?php if (!empty($uploadedStudents)): ?>
                <div class="config-card">
                    <div class="step-header">
                        <h4><i class="fas fa-cog me-2"></i> ধাপ ২: রুম কনফিগারেশন</h4>
                    </div>

                    <form method="POST" id="roomConfigForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">রুমের নাম</label>
                                <input type="text" class="form-control" name="room_config[room_name]"
                                       value="<?php echo htmlspecialchars($roomConfig['room_name']); ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">মোট কলাম সংখ্যা</label>
                                <select class="form-select" name="room_config[total_columns]" id="totalColumns" required>
                                    <?php for ($i = 2; $i <= 8; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php echo ($roomConfig['total_columns'] == $i) ? 'selected' : ''; ?>>
                                            <?php echo $i; ?> টি কলাম
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <label class="form-label">সীট বিন্যাস প্যাটার্ন</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="arrangement-option <?php echo ($roomConfig['arrangement_type'] == 'serial') ? 'selected' : ''; ?>"
                                             onclick="selectArrangement('serial')">
                                            <i class="fas fa-arrow-right fa-2x mb-2"></i>
                                            <h6>সিরিয়াল</h6>
                                            <small>১, ২, ৩, ৪...</small>
                                        </div>
                                        <input type="radio" name="room_config[arrangement_type]" value="serial"
                                               <?php echo ($roomConfig['arrangement_type'] == 'serial') ? 'checked' : ''; ?> style="display: none;">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="arrangement-option <?php echo ($roomConfig['arrangement_type'] == 'zigzag') ? 'selected' : ''; ?>"
                                             onclick="selectArrangement('zigzag')">
                                            <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                                            <h6>জিগজ্যাগ</h6>
                                            <small>↓↑↓↑ প্যাটার্ন</small>
                                        </div>
                                        <input type="radio" name="room_config[arrangement_type]" value="zigzag"
                                               <?php echo ($roomConfig['arrangement_type'] == 'zigzag') ? 'checked' : ''; ?> style="display: none;">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="arrangement-option <?php echo ($roomConfig['arrangement_type'] == 'diagonal') ? 'selected' : ''; ?>"
                                             onclick="selectArrangement('diagonal')">
                                            <i class="fas fa-slash fa-2x mb-2"></i>
                                            <h6>ডায়াগোনাল</h6>
                                            <small>তির্যক বিন্যাস</small>
                                        </div>
                                        <input type="radio" name="room_config[arrangement_type]" value="diagonal"
                                               <?php echo ($roomConfig['arrangement_type'] == 'diagonal') ? 'checked' : ''; ?> style="display: none;">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="arrangement-option <?php echo ($roomConfig['arrangement_type'] == 'alternate') ? 'selected' : ''; ?>"
                                             onclick="selectArrangement('alternate')">
                                            <i class="fas fa-random fa-2x mb-2"></i>
                                            <h6>বিকল্প</h6>
                                            <small>একই বেঞ্চে পরপর রোল নং নয়</small>
                                        </div>
                                        <input type="radio" name="room_config[arrangement_type]" value="alternate"
                                               <?php echo ($roomConfig['arrangement_type'] == 'alternate') ? 'checked' : ''; ?> style="display: none;">
                                    </div>
                                    <div class="col-md-3">
                                        <div class="arrangement-option <?php echo ($roomConfig['arrangement_type'] == 'custom_pattern') ? 'selected' : ''; ?>"
                                             onclick="selectArrangement('custom_pattern')">
                                            <i class="fas fa-random fa-2x mb-2"></i>
                                            <h6>কাস্টম প্যাটার্ন</h6>
                                            <small>বেঞ্চ i: i, i+rowCount, next col: i+2*rowCount, i+3*rowCount, etc</small>
                                        </div>
                                        <input type="radio" name="room_config[arrangement_type]" value="custom_pattern"
                                               <?php echo ($roomConfig['arrangement_type'] == 'custom_pattern') ? 'checked' : ''; ?> style="display: none;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary mt-3">
                            <i class="fas fa-save me-2"></i> কনফিগারেশন সেভ করুন
                        </button>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Step 3: Column Configuration -->
            <?php if (!empty($uploadedStudents)): ?>
                <div class="config-card">
                    <div class="step-header">
                        <h4><i class="fas fa-columns me-2"></i> ধাপ ৩: কলাম কনফিগারেশন</h4>
                    </div>

                    <form method="POST" id="columnConfigForm">
                        <div id="columnConfigs">
                            <?php for ($i = 0; $i < $roomConfig['total_columns']; $i++): ?>
                                <div class="column-config">
                                    <h6><i class="fas fa-grip-vertical me-2"></i> কলাম <?php echo $i + 1; ?></h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label class="form-label">বেঞ্চ সংখ্যা</label>
                                            <input type="number" class="form-control"
                                                   name="column_configs[<?php echo $i; ?>][benches]"
                                                   value="<?php echo $columnConfigs[$i]['benches'] ?? 8; ?>"
                                                   min="1" max="20" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">বেঞ্চের ধরন</label>
                                            <select class="form-select" name="column_configs[<?php echo $i; ?>][bench_type]" required>
                                                <option value="single" <?php echo (($columnConfigs[$i]['bench_type'] ?? 'double') == 'single') ? 'selected' : ''; ?>>
                                                    একক (১ জন)
                                                </option>
                                                <option value="double" <?php echo (($columnConfigs[$i]['bench_type'] ?? 'double') == 'double') ? 'selected' : ''; ?>>
                                                    দ্বিক (২ জন)
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">ছোট বেঞ্চ (ঐচ্ছিক)</label>
                                            <input type="text" class="form-control"
                                                   name="column_configs[<?php echo $i; ?>][small_benches]"
                                                   value="<?php echo $columnConfigs[$i]['small_benches'] ?? ''; ?>"
                                                   placeholder="যেমন: 1,3,5">
                                            <small class="text-muted">কমা দিয়ে বেঞ্চ নম্বর আলাদা করুন। এই বেঞ্চগুলি ডিফল্ট থেকে ভিন্ন হবে (১ সিট/২ সিট)</small>
                                        </div>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> কলাম কনফিগারেশন সেভ করুন
                        </button>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Step 4: Generate Seat Plan -->
            <?php if (!empty($uploadedStudents) && !empty($columnConfigs)): ?>
                <div class="config-card">
                    <div class="step-header">
                        <h4><i class="fas fa-magic me-2"></i> ধাপ ৪: সীট প্লান তৈরি করুন</h4>
                    </div>

                    <form method="POST">
                        <div class="text-center">
                            <button type="submit" name="generate_seat_plan" class="btn btn-success btn-lg">
                                <i class="fas fa-magic me-2"></i> সীট প্লান তৈরি করুন
                            </button>
                        </div>
                    </form>

                    <?php if (!empty($seatPlan)): ?>
                        <div class="seat-plan-container mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-3 no-print">
                                <h5><i class="fas fa-eye me-2"></i> সীট প্লান প্রিভিউ</h5>
                                <div>
                                <button onclick="window.print()" class="btn btn-primary">
                                    <i class="fas fa-print me-2"></i> প্রিন্ট করুন
                                </button>
                                </div>
                            </div>

                            <!-- Print Area Start -->
                            <div class="print-area">
                                <div class="print-header" style="display: none;">
                                    <h3><?php echo htmlspecialchars($roomConfig['room_name']); ?></h3>
                                    <p>মোট শিক্ষার্থী: <?php echo count($uploadedStudents); ?> জন |
                                       বিন্যাস: <?php
                                       $patterns = [
                                           'serial' => 'সিরিয়াল',
                                           'zigzag' => 'জিগজ্যাগ',
                                           'diagonal' => 'ডায়াগোনাল',
                                           'alternate' => 'বিকল্প',
                                           'custom_pattern' => 'কাস্টম প্যাটার্ন'
                                       ];
                                       echo $patterns[$roomConfig['arrangement_type']] ?? 'সিরিয়াল';
                                       ?></p>
                                </div>
                                <div class="text-center mb-3">
                                    <h4><?php echo htmlspecialchars($roomConfig['room_name']); ?></h4>
                                    <p class="text-muted">মোট শিক্ষার্থী: <?php echo count($uploadedStudents); ?> জন</p>
                                </div>
                                <div class="seat-plan-grid">
                                    <?php foreach ($seatPlan as $colIndex => $column): ?>
                                        <div class="column-wrapper">
                                            <div class="column-header">
                                                কলাম <?php echo $colIndex + 1; ?>
                                            </div>
                                            <?php foreach ($column as $benchIndex => $bench): ?>
                                                <div class="bench-row">
                                                    <div class="bench-seats">
                                                        <?php foreach ($bench as $seatIndex => $student): ?>
                                                            <div class="seat-box <?php echo $student ? 'occupied' : 'empty'; ?>">
                                                                <?php echo $student ? htmlspecialchars($student['roll_no']) : '-'; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <!-- Print Area End -->
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- এক নজরে সীট প্লান Button -->
            <button id="glanceViewBtn" class="btn btn-info no-print" style="margin-bottom: 18px; font-size: 16px; font-weight: 600;">এক নজরে সীট প্লান দেখুন</button>
            <!-- এক নজরে সীট প্লান Modal/Section (Dynamic & Customizable) -->
            <div id="glanceViewModal" style="display:none; position:fixed; top:0; left:0; width:100vw; height:100vh; background:rgba(0,0,0,0.12); z-index:99999; align-items:center; justify-content:center;">
              <div style="background:#fff; border-radius:12px; max-width:98vw; max-height:98vh; overflow:auto; box-shadow:0 8px 32px rgba(0,0,0,0.18); margin:auto; padding:32px 24px 24px 24px; position:relative; min-width:600px;">
                <button onclick="document.getElementById('glanceViewModal').style.display='none'" style="position:absolute; top:12px; right:18px; font-size:22px; background:none; border:none; color:#333; cursor:pointer;">&times;</button>
                <div style="text-align:center; margin-bottom:10px;">
                  <input id="glanceBuildingName" type="text" value="পুরাতন ভবন" style="font-size:30px; font-weight:700; color:#388e3c; border:none; border-bottom:2px solid #388e3c; text-align:center; width:320px; margin-bottom:4px;">
                  <div style="font-size:22px; font-weight:700; color:#0057ff; margin-bottom:8px;">এক নজরে সীট প্লান</div>
                </div>
                <div style="display:flex; gap:18px; flex-wrap:wrap; justify-content:center; align-items:center; margin-bottom:12px;">
                  <div>
                    <label style="font-weight:600;">প্রতিষ্ঠান:</label>
                    <input id="glanceInstituteName" type="text" value="আপনার প্রতিষ্ঠানের নাম" style="font-size:16px; padding:2px 8px; border-radius:5px; border:1px solid #bbb; min-width:160px;">
                  </div>
                  <div>
                    <label style="font-weight:600;">বিষয়:</label>
                    <input id="glanceExamSubject" type="text" value="পরীক্ষার বিষয়" style="font-size:16px; padding:2px 8px; border-radius:5px; border:1px solid #bbb; min-width:120px;">
                  </div>
                  <div>
                    <label style="font-weight:600;">তারিখ:</label>
                    <input id="glanceExamDate" type="date" value="<?php echo date('Y-m-d'); ?>" style="font-size:16px; padding:2px 8px; border-radius:5px; border:1px solid #bbb; min-width:120px;">
                  </div>
                </div>
                <div style="overflow-x:auto; margin-bottom:12px;">
                  <table id="glanceDynamicTable" class="table table-bordered table-striped" style="min-width:900px; text-align:center;">
                    <thead style="background:#fffbe7;">
                      <tr>
                        <th>ক্রম নং</th>
                        <th>কক্ষ নম্বর</th>
                        <th>কলেজের নাম</th>
                        <th>বিভাগ/শাখা</th>
                        <th>রোল নম্বর (থেকে-পর্যন্ত)</th>
                        <th>ছাত্র/ছাত্রী সংখ্যা</th>
                        <th>ডিলিট</th>
                      </tr>
                    </thead>
                    <tbody id="glanceDynamicTableBody">
                      <tr>
                        <td>1</td>
                        <td><input type="text" value="110" style="width:60px;"></td>
                        <td><input type="text" value="দামুড়হুদা পাইলট গার্লস স্কুল এন্ড কলেজ" style="width:180px;"></td>
                        <td><input type="text" value="বিজ্ঞান" style="width:80px;"></td>
                        <td><input type="text" value="502121-502126" style="width:120px;"></td>
                        <td><input type="number" value="46" style="width:60px;"></td>
                        <td><button onclick="this.closest('tr').remove();updateGlanceTableSerials();" style="color:#fff;background:#e53935;border:none;border-radius:4px;padding:2px 8px;">✖</button></td>
                      </tr>
                    </tbody>
                  </table>
                  <button onclick="addGlanceTableRow();" style="margin-top:8px; font-size:15px; font-weight:600; color:#fff; background:#388e3c; border:none; border-radius:5px; padding:4px 18px;">+ নতুন সারি যোগ করুন</button>
                </div>
                <button onclick="printGlanceTable();" class="btn btn-primary" style="margin-top:10px; font-size:16px;">🖨️ প্রিন্ট করুন</button>
              </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File upload handling
        document.getElementById('csvFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = (file.size / 1024).toFixed(2) + ' KB';
                document.getElementById('fileInfo').style.display = 'block';
            }
        });

        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('csvFile').files = files;
                document.getElementById('csvFile').dispatchEvent(new Event('change'));
            }
        });

        // Arrangement selection
        function selectArrangement(type) {
            // Remove selected class from all options
            document.querySelectorAll('.arrangement-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            event.target.closest('.arrangement-option').classList.add('selected');

            // Check the corresponding radio button
            document.querySelector(`input[value="${type}"]`).checked = true;
        }

        // Dynamic column configuration
        document.getElementById('totalColumns').addEventListener('change', function() {
            // Auto-submit form when columns change
            document.getElementById('roomConfigForm').submit();
        });

        // Auto print if ?autoprint=1 in URL
        if (window.location.search.includes('autoprint=1')) {
            window.onload = function() {
                setTimeout(() => window.print(), 300);
            };
        }

        document.getElementById('glanceViewBtn').onclick = function() {
            document.getElementById('glanceViewModal').style.display = 'flex';
        };

        function addGlanceTableRow() {
          const tbody = document.getElementById('glanceDynamicTableBody');
          const row = document.createElement('tr');
          row.innerHTML = `
            <td></td>
            <td><input type="text" value="" style="width:60px;"></td>
            <td><input type="text" value="" style="width:180px;"></td>
            <td><input type="text" value="" style="width:80px;"></td>
            <td><input type="text" value="" style="width:120px;"></td>
            <td><input type="number" value="" style="width:60px;"></td>
            <td><button onclick="this.closest('tr').remove();updateGlanceTableSerials();" style="color:#fff;background:#e53935;border:none;border-radius:4px;padding:2px 8px;">✖</button></td>
          `;
          tbody.appendChild(row);
          updateGlanceTableSerials();
        }
        function updateGlanceTableSerials() {
          const rows = document.querySelectorAll('#glanceDynamicTableBody tr');
          rows.forEach((tr, idx) => { tr.children[0].textContent = idx+1; });
        }
        function printGlanceTable() {
          const modal = document.getElementById('glanceViewModal');
          const printContents = modal.innerHTML;
          const win = window.open('', '', 'width=900,height=700');
          win.document.write('<html><head><title>এক নজরে সীট প্লান</title>');
          win.document.write('<style>body{font-family:sutonny,solaimanlipi,nikosh,arial,sans-serif;} table{border-collapse:collapse;width:100%;} th,td{border:1.5px solid #444;padding:6px 8px;font-size:16px;} th{background:#fffbe7;} input{border:none;background:transparent;font-size:16px;text-align:center;} h3{margin-bottom:10px;} @media print{@page{margin:4mm;}}</style>');
          win.document.write('</head><body>');
          win.document.write(printContents);
          win.document.write('</body></html>');
          win.document.close();
          setTimeout(()=>{win.print();win.close();}, 400);
        }
        updateGlanceTableSerials();
    </script>
</body>
</html>
