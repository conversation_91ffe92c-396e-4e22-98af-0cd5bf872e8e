<?php
require_once __DIR__ . '/models/Student.php';

// Get parameters
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$searchName = $_GET['search'] ?? '';
$subjectCode = $_GET['subject'] ?? '';
$groupName = $_GET['group'] ?? '';

// Validate cards per page
if ($cardsPerPage < 1 || $cardsPerPage > 30) {
    $cardsPerPage = 12;
}

// Get students
$student = new Student();
$students = [];
$error = '';

try {
    // Start with all students
    $students = $student->getAll();

    // Apply subject filter
    if (!empty($subjectCode)) {
        $students = $student->getStudentsWithSubjectCode($subjectCode);
    }

    // Apply additional filters
    if (!empty($searchName)) {
        $students = array_filter($students, function($s) use ($searchName) {
            return stripos($s['student_name'], $searchName) !== false;
        });
    }

    if (!empty($groupName)) {
        $students = array_filter($students, function($s) use ($groupName) {
            return stripos($s['group_name'], $groupName) !== false;
        });
    }

    // Convert filtered array back to indexed array
    $students = array_values($students);

} catch (Exception $e) {
    $error = $e->getMessage();
    $students = [];
}

$totalStudents = count($students);
$totalPages = ceil($totalStudents / $cardsPerPage);


?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seat Cards - HSC Exam 2025</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 8mm;
            height: 277mm;
        }
        
        .seat-card {
            border: 2px solid #333;
            border-radius: 8px;
            padding: 8px;
            background: #fafafa;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
            height: 65mm;
        }
        
        .card-header {
            border-bottom: 1px solid #666;
            padding-bottom: 4px;
            margin-bottom: 6px;
        }
        
        .exam-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
        }
        
        .college-info {
            font-size: 10px;
            color: #666;
        }
        
        .student-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 8px 0;
            line-height: 1.2;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .roll-number {
            background: #f0f0f0;
            border: 2px solid #333;
            border-radius: 6px;
            padding: 8px;
            margin: 8px 0;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        
        .student-details {
            font-size: 11px;
            color: #555;
            line-height: 1.3;
        }
        
        .detail-row {
            margin: 2px 0;
        }
        
        .label {
            font-weight: 600;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                padding: 10mm;
                box-shadow: none;
            }
            
            .seat-card {
                border: 2px solid #000 !important;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .exam-title, .roll-number {
                color: #000 !important;
            }
        }
        
        @page {
            size: A4;
            margin: 0;
        }
    </style>
</head>
<body>
    <?php if ($totalStudents > 0): ?>
        <?php for ($page = 0; $page < $totalPages; $page++): ?>
            <div class="page">
                <div class="cards-container">
                    <?php 
                    $startIndex = $page * $cardsPerPage;
                    $endIndex = min($startIndex + $cardsPerPage, $totalStudents);
                    
                    for ($i = $startIndex; $i < $endIndex; $i++): 
                        $s = $students[$i];
                    ?>
                        <div class="seat-card">
                            <div class="card-header">
                                <div class="exam-title">HSC Exam-2025</div>
                                <div class="college-info">দামুড়হুদা, কোড. 295</div>
                            </div>
                            
                            <div class="student-name">
                                <?php echo htmlspecialchars($s['student_name']); ?>
                            </div>
                            
                            <div class="roll-number">
                                <?php echo htmlspecialchars($s['roll_no']); ?>
                            </div>
                            
                            <div class="student-details">
                                <div class="detail-row">
                                    <span class="label">রেজিঃ</span> <?php echo htmlspecialchars($s['reg_no']); ?>
                                </div>
                                <div class="detail-row">
                                    <span class="label">বিভাগ:</span> <?php echo htmlspecialchars($s['group_name']); ?>
                                </div>
                                <div class="detail-row">
                                    <span class="label">শিক্ষাবর্ষ:</span> <?php echo htmlspecialchars($s['session']); ?>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                    
                    <?php 
                    // Fill remaining slots with empty cards if needed
                    $remainingSlots = $cardsPerPage - ($endIndex - $startIndex);
                    for ($j = 0; $j < $remainingSlots; $j++): 
                    ?>
                        <div class="seat-card" style="border: 1px dashed #ccc; background: #f9f9f9;">
                            <div style="color: #ccc; font-size: 14px; margin: auto;">Empty</div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        <?php endfor; ?>
    <?php else: ?>
        <div class="page">
            <div style="text-align: center; margin-top: 100px; font-size: 18px; color: #666;">
                কোন শিক্ষার্থী পাওয়া যায়নি
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
