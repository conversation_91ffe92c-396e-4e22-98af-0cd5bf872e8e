<?php
require_once 'includes/teacher_db.php';

// Get selected date and teacher ID
$selectedDate = $_GET['date'] ?? '';
$teacherId = $_GET['teacher_id'] ?? '';

if (!$selectedDate) {
    die('তারিখ নির্বাচন করুন!');
}

// Get duty assignments
$dutyAssignments = $teacherManager->getDutyAssignments($selectedDate);

if (empty($dutyAssignments)) {
    die('এই তারিখে কোন ডিউটি বন্টন পাওয়া যায়নি!');
}

// Filter for specific teacher if requested
if ($teacherId) {
    $dutyAssignments = array_filter($dutyAssignments, function($assignment) use ($teacherId) {
        return $assignment['teacher_id'] == $teacherId;
    });
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক আইডি কার্ড - <?php echo formatDateBengali($selectedDate); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 10mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 10px;
            background: white;
            color: #000;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            width: 100%;
        }
        
        .id-card {
            width: 85mm;
            height: 54mm;
            border: 2px solid #000;
            border-radius: 8px;
            padding: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
            overflow: hidden;
            page-break-inside: avoid;
            box-sizing: border-box;
        }
        
        .id-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="2"/></svg>');
            opacity: 0.3;
            z-index: 1;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 6px;
        }
        
        .card-header h1 {
            font-size: 11px;
            margin: 0;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .card-header h2 {
            font-size: 9px;
            margin: 2px 0;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .card-header p {
            font-size: 7px;
            margin: 1px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .card-body {
            display: flex;
            align-items: center;
            flex-grow: 1;
            margin-bottom: 4px;
        }
        
        .photo-section {
            width: 35px;
            height: 35px;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .teacher-photo {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .photo-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .teacher-info {
            flex-grow: 1;
            min-width: 0;
        }
        
        .teacher-name {
            font-size: 10px;
            font-weight: bold;
            margin: 0 0 2px 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            line-height: 1.1;
        }
        
        .teacher-designation {
            font-size: 7px;
            margin: 0 0 1px 0;
            opacity: 0.9;
            line-height: 1.1;
        }
        
        .teacher-subject {
            font-size: 7px;
            margin: 0;
            opacity: 0.9;
            line-height: 1.1;
        }
        
        .card-footer {
            text-align: center;
            border-top: 1px solid rgba(255,255,255,0.3);
            padding-top: 3px;
        }
        
        .exam-date {
            font-size: 7px;
            margin: 0;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .room-info {
            font-size: 8px;
            margin: 1px 0 0 0;
            font-weight: bold;
            background: rgba(255,255,255,0.2);
            padding: 1px 4px;
            border-radius: 3px;
            display: inline-block;
        }
        
        .college-logo {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 20px;
            height: 20px;
            opacity: 0.7;
            z-index: 3;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .cards-container {
                gap: 10px;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            font-size: 14px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button onclick="window.print()" class="print-button no-print">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="cards-container">
        <?php foreach ($dutyAssignments as $assignment): 
            // Get full teacher details
            $teacher = $teacherManager->getTeacherById($assignment['teacher_id']);
        ?>
            <div class="id-card">
                <!-- College Logo -->
                <?php if (file_exists('uploads/college_logo.jpg')): ?>
                    <img src="uploads/college_logo.jpg" alt="College Logo" class="college-logo">
                <?php endif; ?>
                
                <div class="card-content">
                    <!-- Header -->
                    <div class="card-header">
                        <h1>HSC পরীক্ষা-২০২৫</h1>
                        <h2>তত্ত্বাবধায়ক আইডি কার্ড</h2>
                        <p>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</p>
                        <p>দামুড়হুদা, চুয়াডাঙ্গা</p>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <!-- Photo -->
                        <div class="photo-section">
                            <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" 
                                     alt="Teacher Photo" class="teacher-photo">
                            <?php else: ?>
                                <div class="photo-placeholder">
                                    <span style="font-size: 12px;">👤</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Teacher Info -->
                        <div class="teacher-info">
                            <div class="teacher-name"><?php echo htmlspecialchars($assignment['name']); ?></div>
                            <div class="teacher-designation"><?php echo htmlspecialchars($assignment['designation']); ?></div>
                            <div class="teacher-subject"><?php echo htmlspecialchars($assignment['subject']); ?></div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="card-footer">
                        <div class="exam-date">তারিখ: <?php echo formatDateBengali($selectedDate); ?></div>
                        <?php if ($assignment['room_number']): ?>
                            <div class="room-info">রুম: <?php echo htmlspecialchars($assignment['room_number']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>
