<?php
// Test page for dynamic seat plan system
require_once __DIR__ . '/config/database.php';

// Check if we have students in database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM students");
    $totalStudents = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($totalStudents == 0) {
        // Add some test data with subject codes
        $testStudents = [
            ['roll_no' => '101', 'student_name' => 'আব্দুল করিম', 'reg_no' => '2023001', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '102', 'student_name' => 'ফাতেমা খাতুন', 'reg_no' => '2023002', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '103', 'student_name' => 'মোহাম্মদ রহিম', 'reg_no' => '2023003', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '104', 'student_name' => 'সালমা বেগম', 'reg_no' => '2023004', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '105', 'student_name' => 'আহমেদ হাসান', 'reg_no' => '2023005', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '106', 'student_name' => 'রাশিদা আক্তার', 'reg_no' => '2023006', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '107', 'student_name' => 'মোস্তফা কামাল', 'reg_no' => '2023007', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '108', 'student_name' => 'নাসরিন সুলতানা', 'reg_no' => '2023008', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '109', 'student_name' => 'জাকির হোসেন', 'reg_no' => '2023009', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '110', 'student_name' => 'রুমানা আক্তার', 'reg_no' => '2023010', 'group_name' => 'Science', 'subjects' => ['101', '107', '174', '176', '178', '265', '275', '129']],
            ['roll_no' => '111', 'student_name' => 'আনিসুর রহমান', 'reg_no' => '2023011', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '112', 'student_name' => 'শাহানা পারভীন', 'reg_no' => '2023012', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '113', 'student_name' => 'তানভীর আহমেদ', 'reg_no' => '2023013', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '114', 'student_name' => 'সুমাইয়া খাতুন', 'reg_no' => '2023014', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '115', 'student_name' => 'রফিকুল ইসলাম', 'reg_no' => '2023015', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '116', 'student_name' => 'নার্গিস আক্তার', 'reg_no' => '2023016', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '117', 'student_name' => 'শাহরিয়ার কবির', 'reg_no' => '2023017', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '118', 'student_name' => 'রোকেয়া বেগম', 'reg_no' => '2023018', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '119', 'student_name' => 'মাহবুবুর রহমান', 'reg_no' => '2023019', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '120', 'student_name' => 'সাবিনা ইয়াসমিন', 'reg_no' => '2023020', 'group_name' => 'Arts', 'subjects' => ['101', '107', '109', '121', '117', '249', '271', '304']],
            ['roll_no' => '121', 'student_name' => 'আব্দুল মালেক', 'reg_no' => '2023021', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '122', 'student_name' => 'রেহানা খাতুন', 'reg_no' => '2023022', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '123', 'student_name' => 'সাইফুল ইসলাম', 'reg_no' => '2023023', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '124', 'student_name' => 'তাহমিনা আক্তার', 'reg_no' => '2023024', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '125', 'student_name' => 'নুরুল আমিন', 'reg_no' => '2023025', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '126', 'student_name' => 'শামীমা বেগম', 'reg_no' => '2023026', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '127', 'student_name' => 'হাবিবুর রহমান', 'reg_no' => '2023027', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '128', 'student_name' => 'ফরিদা পারভীন', 'reg_no' => '2023028', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '129', 'student_name' => 'আলমগীর হোসেন', 'reg_no' => '2023029', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']],
            ['roll_no' => '130', 'student_name' => 'সুলতানা রাজিয়া', 'reg_no' => '2023030', 'group_name' => 'Commerce', 'subjects' => ['101', '107', '253', '292', '277', '286', '109', '129']]
        ];

        $sql = "INSERT INTO students (c_code, eiin, roll_no, reg_no, session, type, group_name, student_name, father_name, gender, sub_1, sub_2, sub_3, sub_4, sub_5, sub_6, sub_7, sub_8) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $db->prepare($sql);

        foreach ($testStudents as $student) {
            $subjects = $student['subjects'];
            $stmt->execute([
                '295',
                '123456',
                $student['roll_no'],
                $student['reg_no'],
                '2023-24',
                'Regular',
                $student['group_name'],
                $student['student_name'],
                'পিতার নাম',
                'Male',
                $subjects[0] ?? '',
                $subjects[1] ?? '',
                $subjects[2] ?? '',
                $subjects[3] ?? '',
                $subjects[4] ?? '',
                $subjects[5] ?? '',
                $subjects[6] ?? '',
                $subjects[7] ?? ''
            ]);
        }
        
        $message = "✅ " . count($testStudents) . " টি টেস্ট ডেটা যোগ করা হয়েছে!";
    } else {
        $message = "✅ ডাটাবেসে " . $totalStudents . " জন শিক্ষার্থীর তথ্য রয়েছে।";
    }
    
} catch (Exception $e) {
    $message = "❌ ডাটাবেস এরর: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সীট প্লান সিস্টেম টেস্ট - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-body {
            padding: 40px;
        }
        
        .feature-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 20px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="mb-0">
                <i class="fas fa-cogs me-3"></i>
                ডাইনামিক সীট প্লান সিস্টেম
            </h1>
            <p class="mb-0 mt-3">সিস্টেম টেস্ট ও ডেমো</p>
        </div>
        
        <div class="test-body">
            <!-- Status -->
            <div class="status-badge <?php echo strpos($message, '✅') !== false ? 'status-success' : 'status-error'; ?>">
                <?php echo $message; ?>
            </div>
            
            <!-- Features -->
            <div class="row">
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-th-large"></i>
                        </div>
                        <h5>ডাইনামিক কনফিগারেশন</h5>
                        <p>রুম সংখ্যা, সীট বিন্যাস, এবং রোল রেঞ্জ কাস্টমাইজ করুন।</p>
                        <a href="dynamic_seat_plan.php" class="btn-test">
                            <i class="fas fa-play me-2"></i>
                            শুরু করুন
                        </a>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-wave-square"></i>
                        </div>
                        <h5>বিভিন্ন প্যাটার্ন</h5>
                        <p>সিরিয়াল, ঝিকঝাক, কোনাকোনি, এবং বিকল্প প্যাটার্ন।</p>
                        <span class="btn-test" style="opacity: 0.7; cursor: default;">
                            <i class="fas fa-check me-2"></i>
                            অন্তর্ভুক্ত
                        </span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-print"></i>
                        </div>
                        <h5>প্রিন্ট অপ্টিমাইজড</h5>
                        <p>A4 সাইজে অপ্টিমাইজড প্রিন্ট লেআউট।</p>
                        <span class="btn-test" style="opacity: 0.7; cursor: default;">
                            <i class="fas fa-check me-2"></i>
                            প্রস্তুত
                        </span>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h5>রেসপন্সিভ ডিজাইন</h5>
                        <p>মোবাইল এবং ডেস্কটপ উভয়ে কাজ করে।</p>
                        <span class="btn-test" style="opacity: 0.7; cursor: default;">
                            <i class="fas fa-check me-2"></i>
                            সম্পূর্ণ
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="alert alert-info alert-custom">
                <h6><i class="fas fa-rocket me-2"></i>দ্রুত শুরু করুন:</h6>
                <div class="d-flex gap-3 flex-wrap mt-3">
                    <a href="dynamic_seat_plan.php" class="btn-test">
                        <i class="fas fa-cog me-2"></i>
                        সীট প্লান কনফিগার করুন
                    </a>
                    <a href="view_students.php" class="btn-test">
                        <i class="fas fa-users me-2"></i>
                        শিক্ষার্থী দেখুন
                    </a>
                    <a href="index.php" class="btn-test">
                        <i class="fas fa-home me-2"></i>
                        হোম পেজ
                    </a>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="mt-4">
                <h6>ব্যবহারের নির্দেশনা:</h6>
                <ol class="mt-3">
                    <li>প্রথমে <strong>"সীট প্লান কনফিগার করুন"</strong> বাটনে ক্লিক করুন</li>
                    <li>আপনার পছন্দের সীটিং প্যাটার্ন নির্বাচন করুন</li>
                    <li>রুম সংখ্যা এবং কনফিগারেশন সেট করুন</li>
                    <li><strong>"সীট প্লান জেনারেট করুন"</strong> বাটনে ক্লিক করুন</li>
                    <li>প্রিন্ট করতে <strong>"প্রিন্ট করুন"</strong> বাটন ব্যবহার করুন</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
