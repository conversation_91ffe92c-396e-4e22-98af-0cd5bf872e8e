<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কাস্টম সীট কার্ড জেনারেটর - HSC Exam 2025</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .customization-section {
            padding: 30px;
        }
        
        .option-group {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .color-option.selected {
            border-color: #333;
            transform: scale(1.1);
        }
        
        .preview-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            background: white;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .btn-generate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="main-container">
            <!-- Header -->
            <div class="header">
                <h1><i class="fas fa-palette me-3"></i>কাস্টম সীট কার্ড জেনারেটর</h1>
                <p>আপনার পছন্দ অনুযায়ী সীট কার্ড ডিজাইন করুন</p>

                <!-- Quick Presets -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <button type="button" class="btn btn-light btn-sm w-100" onclick="applyPreset('modern')">
                            <i class="fas fa-star me-1"></i>আধুনিক ডিজাইন
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-light btn-sm w-100" onclick="applyPreset('classic')">
                            <i class="fas fa-university me-1"></i>ক্লাসিক ডিজাইন
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-light btn-sm w-100" onclick="applyPreset('colorful')">
                            <i class="fas fa-rainbow me-1"></i>রঙিন ডিজাইন
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-light btn-sm w-100" onclick="applyPreset('minimal')">
                            <i class="fas fa-minus me-1"></i>মিনিমাল ডিজাইন
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6 text-center">
                        <button type="button" class="btn btn-success btn-sm" onclick="applyOptimalSettings()">
                            <i class="fas fa-magic me-1"></i>A4 সর্বোত্তম সেটিংস
                        </button>
                    </div>
                    <div class="col-6 text-center">
                        <button type="button" class="btn btn-warning btn-sm" onclick="applyCompactSettings()">
                            <i class="fas fa-compress me-1"></i>কমপ্যাক্ট মোড (১২টি কার্ড)
                        </button>
                    </div>
                </div>
            </div>

            <form id="customForm" method="GET" action="custom_seat_cards.php">
                <input type="hidden" name="preview" value="1">
                
                <div class="customization-section">
                    <div class="row">
                        <!-- Design Options -->
                        <div class="col-md-6">
                            <div class="option-group">
                                <h5><i class="fas fa-paint-brush me-2"></i>ডিজাইন থিম</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">থিম স্টাইল</label>
                                        <select name="theme" class="form-select">
                                            <option value="default">ডিফল্ট</option>
                                            <option value="modern">আধুনিক</option>
                                            <option value="shadow">ছায়া সহ</option>
                                            <option value="bold">বোল্ড</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">বর্ডার স্টাইল</label>
                                        <select name="border" class="form-select">
                                            <option value="solid">সলিড</option>
                                            <option value="dashed">ড্যাশড</option>
                                            <option value="dotted">ডটেড</option>
                                            <option value="double">ডাবল</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="option-group">
                                <h5><i class="fas fa-palette me-2"></i>রঙের স্কিম</h5>
                                <div class="d-flex flex-wrap">
                                    <div class="color-option selected" data-color="blue" style="background: #4a90e2;" title="নীল"></div>
                                    <div class="color-option" data-color="green" style="background: #27ae60;" title="সবুজ"></div>
                                    <div class="color-option" data-color="purple" style="background: #8e44ad;" title="বেগুনি"></div>
                                    <div class="color-option" data-color="red" style="background: #e74c3c;" title="লাল"></div>
                                    <div class="color-option" data-color="orange" style="background: #f39c12;" title="কমলা"></div>
                                    <div class="color-option" data-color="teal" style="background: #1abc9c;" title="টিল"></div>
                                </div>
                                <input type="hidden" name="color" id="colorInput" value="blue">
                            </div>

                            <div class="option-group">
                                <h5><i class="fas fa-font me-2"></i>ফন্ট ও সাইজ</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">ফন্ট সাইজ</label>
                                        <select name="font_size" class="form-select">
                                            <option value="small">ছোট</option>
                                            <option value="medium" selected>মাঝারি</option>
                                            <option value="large">বড়</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">হেডার স্টাইল</label>
                                        <select name="header" class="form-select">
                                            <option value="gradient" selected>গ্রেডিয়েন্ট</option>
                                            <option value="solid">সলিড</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Layout Options -->
                        <div class="col-md-6">
                            <div class="option-group">
                                <h5><i class="fas fa-th me-2"></i>লেআউট সেটিংস</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <label class="form-label">প্রতি পৃষ্ঠায় কার্ড</label>
                                        <select name="cards_per_page" class="form-select">
                                            <option value="4">৪টি (২×২)</option>
                                            <option value="6">৬টি (৩×২)</option>
                                            <option value="9">৯টি (৩×৩)</option>
                                            <option value="12" selected>১২টি (৩×৪)</option>
                                            <option value="15">১৫টি (৫×৩)</option>
                                            <option value="16">১৬টি (৪×৪)</option>
                                            <option value="20">২০টি (৫×৪)</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">কাগজের সাইজ</label>
                                        <select name="paper_size" class="form-select">
                                            <option value="a4" selected>A4</option>
                                            <option value="letter">Letter</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="option-group">
                                <h5><i class="fas fa-edit me-2"></i>কাস্টম টেক্সট</h5>
                                <div class="mb-3">
                                    <label class="form-label">পরীক্ষার নাম</label>
                                    <input type="text" name="custom_title" class="form-control" value="HSC Exam-2025">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">কলেজ তথ্য</label>
                                    <input type="text" name="custom_subtitle" class="form-control" value="দামুড়হুদা, কোড. 295">
                                </div>
                            </div>

                            <div class="option-group">
                                <h5><i class="fas fa-plus me-2"></i>অতিরিক্ত ফিচার</h5>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="show_photo" id="showPhoto">
                                    <label class="form-check-label" for="showPhoto">
                                        ছবির স্থান দেখান
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" name="show_qr" id="showQR">
                                    <label class="form-check-label" for="showQR">
                                        QR কোড দেখান
                                    </label>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">লোগো অবস্থান</label>
                                    <select name="logo" class="form-select">
                                        <option value="left" selected>বাম</option>
                                        <option value="right">ডান</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Options -->
                    <div class="option-group">
                        <h5><i class="fas fa-filter me-2"></i>শিক্ষার্থী ফিল্টার (ঐচ্ছিক)</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">নাম অনুসন্ধান</label>
                                <input type="text" name="search" class="form-control" placeholder="শিক্ষার্থীর নাম">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">বিষয় কোড</label>
                                <input type="text" name="subject" class="form-control" placeholder="যেমন: 101">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">বিভাগ</label>
                                <input type="text" name="group" class="form-control" placeholder="যেমন: Science">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">শিক্ষার্থীর ধরন</label>
                                <select name="type" class="form-select">
                                    <option value="">সব ধরন</option>
                                    <option value="Regular">নিয়মিত</option>
                                    <option value="Improvement">উন্নতি</option>
                                    <option value="Irregular">অনিয়মিত</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <label class="form-label">লিঙ্গ</label>
                                <select name="gender" class="form-select">
                                    <option value="">সব</option>
                                    <option value="Male">পুরুষ</option>
                                    <option value="Female">মহিলা</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">রোল নম্বর</label>
                                <input type="text" name="rolls" class="form-control" placeholder="যেমন: 101,102,103">
                            </div>
                            <div class="col-md-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="complete_only" id="completeOnly">
                                    <label class="form-check-label" for="completeOnly">
                                        শুধু সম্পূর্ণ বিষয়
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="incomplete_only" id="incompleteOnly">
                                    <label class="form-check-label" for="incompleteOnly">
                                        শুধু অসম্পূর্ণ বিষয়
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Live Preview -->
                    <div class="option-group">
                        <h5><i class="fas fa-eye me-2"></i>লাইভ প্রিভিউ</h5>
                        <div class="row">
                            <div class="col-md-8">
                                <div id="previewCard" class="preview-card">
                                    <div class="card-header-preview" style="background: #4a90e2; color: white; padding: 8px; margin: -15px -15px 10px -15px; border-radius: 8px 8px 0 0;">
                                        <div style="font-weight: bold; font-size: 14px;">HSC Exam-2025</div>
                                        <div style="font-size: 10px; opacity: 0.9;">দামুড়হুদা, কোড. 295</div>
                                    </div>
                                    <div style="font-size: 16px; font-weight: 600; margin: 10px 0;">শিক্ষার্থীর নাম</div>
                                    <div style="font-size: 24px; font-weight: bold; color: #4a90e2; background: rgba(74, 144, 226, 0.1); padding: 8px; border-radius: 6px; margin: 10px 0;">১০১</div>
                                    <div style="font-size: 11px; color: #666;">
                                        <div><strong>রেজিঃ</strong> ১২৩৪৫৬৭৮৯০</div>
                                        <div><strong>বিভাগ:</strong> বিজ্ঞান</div>
                                        <div><strong>শিক্ষাবর্ষ:</strong> ২০২৩-২৪</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>প্রিভিউ তথ্য</h6>
                                    <ul class="mb-0" style="font-size: 0.9rem;">
                                        <li>রিয়েল-টাইম ডিজাইন পরিবর্তন</li>
                                        <li>রঙ ও ফন্ট প্রিভিউ</li>
                                        <li>লেআউট দেখুন</li>
                                        <li>প্রিন্ট-রেডি ডিজাইন</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Print Settings Alert -->
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-print me-2"></i>প্রিন্ট সেটিংস গুরুত্বপূর্ণ</h6>
                        <ul class="mb-0" style="font-size: 0.9rem;">
                            <li><strong>কাগজের সাইজ:</strong> A4 (210 × 297 mm) নির্বাচন করুন</li>
                            <li><strong>মার্জিন:</strong> Minimum বা Custom (5mm) সেট করুন</li>
                            <li><strong>স্কেল:</strong> 100% রাখুন, Fit to page ব্যবহার করবেন না</li>
                            <li><strong>ব্যাকগ্রাউন্ড:</strong> Print backgrounds চালু করুন</li>
                        </ul>
                    </div>

                    <!-- Generate Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-generate btn-lg">
                            <i class="fas fa-magic me-2"></i>কাস্টম সীট কার্ড জেনারেট করুন
                        </button>
                        <br><small class="text-muted mt-2 d-block">নতুন ট্যাবে খুলবে এবং প্রিন্ট ডায়ালগ দেখাবে</small>

                        <div class="mt-3">
                            <a href="bulk_seat_cards.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-layer-group me-2"></i>বাল্ক জেনারেটর
                            </a>
                            <a href="seat_card_generator.php" class="btn btn-outline-info me-2">
                                <i class="fas fa-cog me-2"></i>সাধারণ জেনারেটর
                            </a>
                            <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#helpModal">
                                <i class="fas fa-question-circle me-2"></i>সাহায্য
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel"><i class="fas fa-question-circle me-2"></i>কাস্টম সীট কার্ড জেনারেটর সাহায্য</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-paint-brush me-2"></i>ডিজাইন অপশন</h6>
                            <ul>
                                <li><strong>থিম স্টাইল:</strong> কার্ডের সামগ্রিক চেহারা</li>
                                <li><strong>রঙের স্কিম:</strong> কার্ডের প্রধান রঙ</li>
                                <li><strong>ফন্ট সাইজ:</strong> টেক্সটের আকার</li>
                                <li><strong>বর্ডার স্টাইল:</strong> কার্ডের বর্ডার</li>
                            </ul>

                            <h6><i class="fas fa-th me-2"></i>লেআউট সেটিংস</h6>
                            <ul>
                                <li><strong>প্রতি পৃষ্ঠায় কার্ড:</strong> একটি পৃষ্ঠায় কতটি কার্ড</li>
                                <li><strong>কাগজের সাইজ:</strong> A4 বা Letter</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-filter me-2"></i>ফিল্টার অপশন</h6>
                            <ul>
                                <li><strong>নাম অনুসন্ধান:</strong> নির্দিষ্ট নামের শিক্ষার্থী</li>
                                <li><strong>বিষয় কোড:</strong> নির্দিষ্ট বিষয়ের শিক্ষার্থী</li>
                                <li><strong>বিভাগ:</strong> নির্দিষ্ট বিভাগের শিক্ষার্থী</li>
                                <li><strong>রোল নম্বর:</strong> কমা দিয়ে আলাদা করুন</li>
                            </ul>

                            <h6><i class="fas fa-star me-2"></i>প্রিসেট</h6>
                            <ul>
                                <li><strong>আধুনিক:</strong> নীল রঙ, গ্রেডিয়েন্ট হেডার</li>
                                <li><strong>ক্লাসিক:</strong> বেগুনি রঙ, বড় ফন্ট</li>
                                <li><strong>রঙিন:</strong> কমলা রঙ, ছায়া সহ</li>
                                <li><strong>মিনিমাল:</strong> টিল রঙ, ছোট ফন্ট</li>
                            </ul>
                        </div>
                    </div>

                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-lightbulb me-2"></i>টিপস</h6>
                        <ul class="mb-0">
                            <li>লাইভ প্রিভিউ দেখে ডিজাইন পরিবর্তন করুন</li>
                            <li>প্রিসেট ব্যবহার করে দ্রুত ডিজাইন করুন</li>
                            <li>ফিল্টার ব্যবহার করে নির্দিষ্ট শিক্ষার্থী নির্বাচন করুন</li>
                            <li>জেনারেট করার পর প্রিন্ট ডায়ালগ স্বয়ংক্রিয়ভাবে খুলবে</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Color schemes for preview
        const colorSchemes = {
            'blue': { primary: '#4a90e2', secondary: '#357abd', bg: '#f8fbff' },
            'green': { primary: '#27ae60', secondary: '#219a52', bg: '#f8fff8' },
            'purple': { primary: '#8e44ad', secondary: '#7d3c98', bg: '#faf8ff' },
            'red': { primary: '#e74c3c', secondary: '#c0392b', bg: '#fff8f8' },
            'orange': { primary: '#f39c12', secondary: '#e67e22', bg: '#fffaf8' },
            'teal': { primary: '#1abc9c', secondary: '#16a085', bg: '#f8ffff' }
        };

        // Update preview
        function updatePreview() {
            const color = document.getElementById('colorInput').value;
            const theme = document.querySelector('select[name="theme"]').value;
            const fontSize = document.querySelector('select[name="font_size"]').value;
            const headerStyle = document.querySelector('select[name="header"]').value;
            const customTitle = document.querySelector('input[name="custom_title"]').value;
            const customSubtitle = document.querySelector('input[name="custom_subtitle"]').value;

            const colors = colorSchemes[color];
            const previewCard = document.getElementById('previewCard');
            const headerPreview = previewCard.querySelector('.card-header-preview');

            // Update colors
            previewCard.style.background = colors.bg;
            previewCard.style.borderColor = colors.primary;

            if (headerStyle === 'gradient') {
                headerPreview.style.background = `linear-gradient(135deg, ${colors.primary} 0%, ${colors.secondary} 100%)`;
            } else {
                headerPreview.style.background = colors.primary;
            }

            // Update border radius for theme
            if (theme === 'modern') {
                previewCard.style.borderRadius = '12px';
                headerPreview.style.borderRadius = '10px 10px 0 0';
            } else {
                previewCard.style.borderRadius = '8px';
                headerPreview.style.borderRadius = '6px 6px 0 0';
            }

            // Update shadow for theme
            if (theme === 'shadow') {
                previewCard.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            } else {
                previewCard.style.boxShadow = 'none';
            }

            // Update font sizes
            const fontSizes = {
                'small': { title: '12px', name: '14px', roll: '22px', details: '10px' },
                'medium': { title: '14px', name: '16px', roll: '24px', details: '11px' },
                'large': { title: '16px', name: '18px', roll: '26px', details: '12px' }
            };

            const fonts = fontSizes[fontSize];
            headerPreview.querySelector('div:first-child').style.fontSize = fonts.title;
            headerPreview.querySelector('div:last-child').style.fontSize = fonts.details;
            previewCard.querySelector('div:nth-child(2)').style.fontSize = fonts.name;
            previewCard.querySelector('div:nth-child(3)').style.fontSize = fonts.roll;
            previewCard.querySelector('div:nth-child(3)').style.color = colors.primary;
            previewCard.querySelector('div:nth-child(3)').style.background = `rgba(${parseInt(colors.primary.slice(1,3), 16)}, ${parseInt(colors.primary.slice(3,5), 16)}, ${parseInt(colors.primary.slice(5,7), 16)}, 0.1)`;

            // Update custom text
            headerPreview.querySelector('div:first-child').textContent = customTitle;
            headerPreview.querySelector('div:last-child').textContent = customSubtitle;
        }

        // Color selection
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('colorInput').value = this.dataset.color;
                updatePreview();
            });
        });

        // Update preview on form changes
        document.querySelectorAll('select, input[type="text"]').forEach(element => {
            element.addEventListener('change', updatePreview);
            element.addEventListener('input', updatePreview);
        });

        // Prevent both complete and incomplete from being selected
        document.getElementById('completeOnly').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('incompleteOnly').checked = false;
            }
        });

        document.getElementById('incompleteOnly').addEventListener('change', function() {
            if (this.checked) {
                document.getElementById('completeOnly').checked = false;
            }
        });

        // Form submission
        document.getElementById('customForm').addEventListener('submit', function(e) {
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>জেনারেট হচ্ছে...';
            button.disabled = true;

            // Reset button after a delay
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        });

        // Preset functions
        function applyPreset(presetName) {
            const presets = {
                'modern': {
                    theme: 'modern',
                    color: 'blue',
                    font_size: 'medium',
                    header: 'gradient',
                    border: 'solid',
                    cards_per_page: '12'
                },
                'classic': {
                    theme: 'default',
                    color: 'purple',
                    font_size: 'large',
                    header: 'solid',
                    border: 'double',
                    cards_per_page: '9'
                },
                'colorful': {
                    theme: 'shadow',
                    color: 'orange',
                    font_size: 'medium',
                    header: 'gradient',
                    border: 'solid',
                    cards_per_page: '12'
                },
                'minimal': {
                    theme: 'default',
                    color: 'teal',
                    font_size: 'small',
                    header: 'solid',
                    border: 'solid',
                    cards_per_page: '16'
                }
            };

            const preset = presets[presetName];
            if (preset) {
                // Apply preset values
                Object.keys(preset).forEach(key => {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) {
                        element.value = preset[key];
                    }
                });

                // Update color selection
                document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
                document.querySelector(`[data-color="${preset.color}"]`).classList.add('selected');
                document.getElementById('colorInput').value = preset.color;

                // Update preview
                updatePreview();

                // Show notification
                showNotification(`${presetName} প্রিসেট প্রয়োগ করা হয়েছে!`);
            }
        }

        // Apply optimal settings for A4 printing
        function applyOptimalSettings() {
            const optimalSettings = {
                theme: 'default',
                color: 'blue',
                font_size: 'small', // Changed to small for better fit
                header: 'solid', // Changed to solid for less space
                border: 'solid',
                cards_per_page: '9', // Changed to 9 for better fit
                paper_size: 'a4'
            };

            // Apply optimal values
            Object.keys(optimalSettings).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element) {
                    element.value = optimalSettings[key];
                }
            });

            // Update color selection
            document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
            document.querySelector(`[data-color="${optimalSettings.color}"]`).classList.add('selected');
            document.getElementById('colorInput').value = optimalSettings.color;

            // Update preview
            updatePreview();

            // Show notification
            showNotification('A4 প্রিন্টের জন্য সর্বোত্তম সেটিংস প্রয়োগ করা হয়েছে!');
        }

        // Apply compact settings for maximum cards
        function applyCompactSettings() {
            const compactSettings = {
                theme: 'default',
                color: 'blue',
                font_size: 'small',
                header: 'solid',
                border: 'solid',
                cards_per_page: '12',
                paper_size: 'a4'
            };

            // Apply compact values
            Object.keys(compactSettings).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element) {
                    element.value = compactSettings[key];
                }
            });

            // Update color selection
            document.querySelectorAll('.color-option').forEach(opt => opt.classList.remove('selected'));
            document.querySelector(`[data-color="${compactSettings.color}"]`).classList.add('selected');
            document.getElementById('colorInput').value = compactSettings.color;

            // Update preview
            updatePreview();

            // Show notification
            showNotification('কমপ্যাক্ট মোড প্রয়োগ করা হয়েছে! ১২টি কার্ড একটি পেজে ফিট হবে।');
        }

        // Show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `<i class="fas fa-check-circle me-2"></i>${message}`;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize preview
        updatePreview();
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
