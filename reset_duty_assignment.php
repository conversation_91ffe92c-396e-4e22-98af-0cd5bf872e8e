<?php
require_once 'includes/teacher_db.php';

// Initialize teacher manager
$teacherManager = new TeacherManager($pdo);

// Process form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_duty'])) {
    $dutyDate = $_POST['duty_date'] ?? '';
    
    if (empty($dutyDate)) {
        $message = 'দয়া করে একটি তারিখ নির্বাচন করুন!';
        $messageType = 'danger';
    } else {
        try {
            // Call the removeDutyAssignment method to reset duty for the selected date
            $result = $teacherManager->removeDutyAssignment($dutyDate);
            
            if ($result) {
                $message = $dutyDate . ' তারিখের সকল ডিউটি সফলভাবে রিসেট করা হয়েছে!';
                $messageType = 'success';
            } else {
                $message = 'ডিউটি রিসেট করতে সমস্যা হয়েছে। সম্ভবত এই তারিখে কোন ডিউটি নেই।';
                $messageType = 'warning';
            }
        } catch (Exception $e) {
            $message = 'ত্রুটি: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get all duty dates for dropdown
$dutyDates = $teacherManager->getAllDutyDates();

// Filter only dates that have duty assignments
$sql = "SELECT DISTINCT duty_date FROM duty_assignments ORDER BY duty_date ASC";
$stmt = $pdo->query($sql);
$assignedDates = $stmt->fetchAll(PDO::FETCH_COLUMN);

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>তারিখ ভিত্তিক ডিউটি রিসেট</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            padding: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .alert {
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card mt-4">
                    <div class="card-header text-center">
                        <h3>তারিখ ভিত্তিক ডিউটি রিসেট</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $messageType; ?>">
                                <?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="duty_date" class="form-label">ডিউটির তারিখ নির্বাচন করুন:</label>
                                <select class="form-select" name="duty_date" id="duty_date" required>
                                    <option value="">-- তারিখ নির্বাচন করুন --</option>
                                    <?php foreach ($assignedDates as $date): ?>
                                        <option value="<?php echo $date; ?>">
                                            <?php echo date('d F, Y (l)', strtotime($date)); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text text-muted">শুধুমাত্র যে তারিখগুলোতে ডিউটি বন্টন করা হয়েছে সেগুলো দেখানো হচ্ছে।</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="alert alert-warning">
                                    <strong>সতর্কতা:</strong> এই তারিখের সকল ডিউটি অ্যাসাইনমেন্ট মুছে ফেলা হবে। এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" name="reset_duty" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই তারিখের সকল ডিউটি রিসেট করতে চান?')">
                                    <i class="bi bi-trash"></i> ডিউটি রিসেট করুন
                                </button>
                                <a href="date_wise_duty_assignment.php" class="btn btn-secondary">
                                    ফিরে যান
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>