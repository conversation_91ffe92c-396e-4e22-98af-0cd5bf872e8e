<?php
// Seat plan generator for 3 rows, 2 columns, 2 per bench
function generateSeatPlan($rowCount, $colCount, $perBench) {
    $totalSeats = $rowCount * $colCount * $perBench;
    $seatNumbers = range(1, $totalSeats);
    $plan = [];
    for ($col = 0; $col < $colCount; $col++) {
        $plan[$col] = [];
        for ($row = 0; $row < $rowCount; $row++) {
            $bench = [];
            for ($p = 0; $p < $perBench; $p++) {
                $seatIndex = $row + $p * $rowCount + $col * $rowCount * $perBench;
                if ($seatIndex < $totalSeats) {
                    $bench[] = $seatNumbers[$seatIndex];
                }
            }
            $plan[$col][$row] = $bench;
        }
    }
    return $plan;
}

$rowCount = 3;
$colCount = 2;
$perBench = 2;
$plan = generateSeatPlan($rowCount, $colCount, $perBench);
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>সীট প্লান প্যাটার্ন উদাহরণ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container py-4">
    <h2 class="mb-4">সীট প্লান প্যাটার্ন (৩ সারি, ২ কলাম, প্রতি বেঞ্চে ২ জন)</h2>
    <div class="row">
        <?php foreach ($plan as $colIdx => $benches): ?>
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <strong>কলাম <?php echo $colIdx+1; ?></strong>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered text-center">
                            <thead>
                                <tr><th>বেঞ্চ</th><th>সীট নম্বর</th></tr>
                            </thead>
                            <tbody>
                            <?php foreach ($benches as $rowIdx => $bench): ?>
                                <tr>
                                    <td>বেঞ্চ <?php echo $rowIdx+1; ?></td>
                                    <td><?php echo implode(', ', $bench); ?></td>
                                </tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
</body>
</html> 