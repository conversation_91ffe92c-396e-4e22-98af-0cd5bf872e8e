@echo off
echo ========================================
echo EXMM System Auto Startup Fix
echo ========================================
echo.

REM Check if XAMPP is running
echo Checking XAMPP services...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Apache is running
) else (
    echo ❌ Apache is not running
    echo Starting Apache...
    cd /d "C:\xampp"
    start /min apache_start.bat
    timeout /t 3 >nul
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL is running
) else (
    echo ❌ MySQL is not running
    echo Starting MySQL...
    cd /d "C:\xampp"
    start /min mysql_start.bat
    timeout /t 5 >nul
)

echo.
echo Waiting for services to start completely...
timeout /t 10 >nul

REM Open system check page in browser
echo Opening system check page...
start http://localhost/exmm/system_startup_check.php

echo.
echo ========================================
echo Startup fix completed!
echo ========================================
echo.
echo If you see any errors in the browser,
echo please run this script as Administrator.
echo.
pause
