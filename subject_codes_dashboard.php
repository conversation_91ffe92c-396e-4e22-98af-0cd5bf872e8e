<?php
require_once __DIR__ . '/models/Student.php';

$student = new Student();
$allStudents = $student->getAll();

// First, collect all unique subject codes from database (sub_1 to sub_13)
$allSubjectCodes = [];
foreach ($allStudents as $s) {
    for ($i = 1; $i <= 13; $i++) {
        $subValue = trim($s["sub_$i"] ?? '');
        if (!empty($subValue)) {
            $allSubjectCodes[$subValue] = true;
        }
    }
}

// Define subject names (you can add more as needed)
$subjectNames = [
    '101' => 'Bangla',
    '102' => 'English',
    '103' => 'Information & Communication Technology',
    '107' => 'Physics',
    '108' => 'Chemistry',
    '109' => 'Biology',
    '110' => 'Higher Mathematics',
    '111' => 'Geography',
    '112' => 'History',
    '113' => 'Civics',
    '117' => 'Economics',
    '121' => 'Logic',
    '129' => 'Statistics',
    '174' => 'Accounting',
    '176' => 'Finance & Banking',
    '178' => 'Business Entrepreneurship',
    '249' => 'Study Of Islam',
    '253' => 'Hindu Religion',
    '265' => 'Christian Religion',
    '267' => 'Buddhist Religion',
    '269' => 'Moral Science & Ethics',
    '271' => 'Agriculture Studies',
    '273' => 'Home Science',
    '275' => 'Music',
    '277' => 'Fine Arts',
    '286' => 'Physical Education',
    '292' => 'Career Education',
    '304' => 'Child Development'
];

// Count students for each subject code found in database
$subjectStats = [];
$detailedStats = []; // For detailed breakdown by sub_1 to sub_13

foreach (array_keys($allSubjectCodes) as $code) {
    $count = 0;
    $fieldBreakdown = [];

    // Initialize field breakdown
    for ($i = 1; $i <= 13; $i++) {
        $fieldBreakdown["sub_$i"] = 0;
    }

    foreach ($allStudents as $s) {
        $studentHasCode = false;
        for ($i = 1; $i <= 13; $i++) {
            if (trim($s["sub_$i"]) === $code) {
                $fieldBreakdown["sub_$i"]++;
                if (!$studentHasCode) {
                    $count++;
                    $studentHasCode = true; // Count each student only once per subject
                }
            }
        }
    }

    // Get subject name or use code if name not defined
    $name = $subjectNames[$code] ?? "Subject Code $code";

    $subjectStats[$code] = [
        'name' => $name,
        'count' => $count
    ];

    $detailedStats[$code] = [
        'name' => $name,
        'count' => $count,
        'fields' => $fieldBreakdown
    ];
}

// Sort by count (descending)
uasort($subjectStats, function($a, $b) {
    return $b['count'] - $a['count'];
});

// Also sort detailed stats by count (descending)
uasort($detailedStats, function($a, $b) {
    return $b['count'] - $a['count'];
});

$totalStudents = count($allStudents);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subject Codes Dashboard - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .stats-card {
            transition: transform 0.3s ease;
            border-radius: 15px;
            overflow: hidden;
        }
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .subject-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        .subject-card:hover {
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .subject-count {
            font-size: 2rem;
            font-weight: bold;
        }
        .progress-custom {
            height: 8px;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card stats-card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-chart-bar"></i> Subject Codes Dashboard</h3>
                            <div>
                                <a href="view_students.php" class="btn btn-light">
                                    <i class="fas fa-arrow-left"></i> Back to Students
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="stats-card bg-info text-white p-3 rounded">
                                        <h4><?php echo $totalStudents; ?></h4>
                                        <p class="mb-0">মোট স্টুডেন্ট</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card bg-success text-white p-3 rounded">
                                        <h4><?php echo count(array_filter($subjectStats, function($s) { return $s['count'] > 0; })); ?></h4>
                                        <p class="mb-0">সক্রিয় বিষয়</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card bg-warning text-white p-3 rounded">
                                        <h4><?php echo count($allSubjectCodes); ?></h4>
                                        <p class="mb-0">ব্যবহৃত বিষয় কোড</p>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stats-card bg-danger text-white p-3 rounded">
                                        <h4><?php echo count($subjectNames) - count($allSubjectCodes); ?></h4>
                                        <p class="mb-0">অব্যবহৃত বিষয়</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subject Statistics -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5><i class="fas fa-list"></i> বিষয় অনুযায়ী স্টুডেন্ট সংখ্যা (শুধুমাত্র ব্যবহৃত কোড)</h5>
                        </div>
                        <div class="card-body">
                            <!-- Summary Information -->
                            <div class="alert alert-info mb-4">
                                <h6><i class="fas fa-info-circle"></i> সারসংক্ষেপ:</h6>
                                <p class="mb-1"><strong>ডেটাবেসে পাওয়া বিষয় কোডসমূহ:</strong>
                                    <?php echo implode(', ', array_keys($allSubjectCodes)); ?>
                                </p>
                                <p class="mb-0"><strong>মোট ব্যবহৃত বিষয় কোড:</strong> <?php echo count($allSubjectCodes); ?> টি</p>
                            </div>
                            <div class="row">
                                <?php foreach ($subjectStats as $code => $data): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="subject-card">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <strong><?php echo $code; ?></strong> - <?php echo htmlspecialchars($data['name']); ?>
                                                    </h6>
                                                    <div class="subject-count"><?php echo $data['count']; ?> জন</div>
                                                </div>
                                                <div>
                                                    <?php if ($data['count'] > 0): ?>
                                                        <a href="subject_filter.php?code=<?php echo $code; ?>"
                                                           class="btn btn-light btn-sm">
                                                            <i class="fas fa-eye"></i> দেখুন
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Empty</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <?php if ($totalStudents > 0): ?>
                                                <div class="mt-2">
                                                    <div class="progress progress-custom">
                                                        <div class="progress-bar bg-light" 
                                                             style="width: <?php echo ($data['count'] / $totalStudents) * 100; ?>%">
                                                        </div>
                                                    </div>
                                                    <small class="text-light">
                                                        <?php echo round(($data['count'] / $totalStudents) * 100, 1); ?>% of total students
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Breakdown Table -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-table"></i> বিস্তারিত বিশ্লেষণ (Sub_1 থেকে Sub_13 পর্যন্ত)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>বিষয় কোড</th>
                                            <th>বিষয়ের নাম</th>
                                            <th>মোট স্টুডেন্ট</th>
                                            <?php for ($i = 1; $i <= 13; $i++): ?>
                                                <th>Sub_<?php echo $i; ?></th>
                                            <?php endfor; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($detailedStats as $code => $data): ?>
                                            <tr>
                                                <td><strong><?php echo $code; ?></strong></td>
                                                <td><?php echo htmlspecialchars($data['name']); ?></td>
                                                <td><span class="badge bg-primary"><?php echo $data['count']; ?></span></td>
                                                <?php for ($i = 1; $i <= 13; $i++): ?>
                                                    <td>
                                                        <?php
                                                        $fieldCount = $data['fields']["sub_$i"];
                                                        if ($fieldCount > 0): ?>
                                                            <span class="badge bg-success"><?php echo $fieldCount; ?></span>
                                                        <?php else: ?>
                                                            <span class="text-muted">0</span>
                                                        <?php endif; ?>
                                                    </td>
                                                <?php endfor; ?>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>নোট:</strong> "মোট স্টুডেন্ট" কলামে প্রতিটি স্টুডেন্ট শুধুমাত্র একবার গণনা করা হয়েছে,
                                    কিন্তু Sub_1 থেকে Sub_13 কলামে দেখানো হয়েছে প্রতিটি field এ কতবার এই কোড আছে।
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-dark text-white">
                            <h5><i class="fas fa-tools"></i> Quick Actions</h5>
                        </div>
                        <div class="card-body text-center">
                            <a href="view_students.php" class="btn btn-primary me-2">
                                <i class="fas fa-users"></i> All Students
                            </a>
                            <a href="subject_filter.php" class="btn btn-info me-2">
                                <i class="fas fa-filter"></i> Subject Filter
                            </a>
                            <a href="add_student.php" class="btn btn-success me-2">
                                <i class="fas fa-plus"></i> Add Student
                            </a>
                            <a href="thirteen_subject_students.php" class="btn btn-warning">
                                <i class="fas fa-graduation-cap"></i> ১৩ বিষয়ের শিক্ষার্থী
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add some animation effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.subject-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = (index * 0.1) + 's';
                card.style.animation = 'fadeInUp 0.6s ease forwards';
            });
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
