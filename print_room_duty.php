<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$date = $_GET['date'] ?? '';
$shift = $_GET['shift'] ?? 'all';
$format = $_GET['format'] ?? 'table';
$model = $_GET['model'] ?? 'default';

// Custom heading fields
$custom_college = $_GET['college'] ?? 'মোহাম্মদপুর কেন্দ্রীয় কলেজ';
$custom_address = $_GET['address'] ?? 'ঢাকা';
$custom_exam = $_GET['exam'] ?? 'HSC পরীক্ষা- ' . date('Y');
$custom_title = $_GET['title'] ?? 'রুম ভিত্তিক ডিউটি শিক্ষকদের উপস্থিতি তালিকা';
$custom_date = $_GET['custom_date'] ?? formatDateBengali($date);

// Redirect if no date selected
if (empty($date)) {
    header("Location: duty_assignments_management.php?tab=room-duty");
    exit;
}

// Redirect to image format view if selected
if ($format === 'image') {
    header("Location: print_room_duty_image.php?date=" . urlencode($date) . "&shift=" . urlencode($shift));
    exit;
}

// Get assignments for selected date
$assignments = $teacherManager->getDutyAssignments($date);

// Filter by shift if needed
if ($shift !== 'all') {
    $assignments = array_filter($assignments, function($assignment) use ($shift) {
        return $assignment['duty_shift'] === $shift;
    });
}

// Group assignments by room
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomNumber = !empty($assignment['room_number']) ? $assignment['room_number'] : 'অনির্দিষ্ট';
    if (!isset($roomAssignments[$roomNumber])) {
        $roomAssignments[$roomNumber] = [];
    }
    $roomAssignments[$roomNumber][] = $assignment;
}

// Sort rooms numerically
ksort($roomAssignments, SORT_NATURAL);

// Get date details
$dateDetails = $teacherManager->getDutyDateDetails($date);

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Check if college logo exists
$logoPath = 'uploads/college_logo.jpg';
$hasLogo = file_exists($logoPath);

// Get signatures if available
$principalSignature = '';
$convenerSignature = '';
$signatureFiles = glob('uploads/signatures/*.jpg');
foreach ($signatureFiles as $signFile) {
    if (stripos($signFile, 'principal') !== false) {
        $principalSignature = $signFile;
    } elseif (stripos($signFile, 'convener') !== false) {
        $convenerSignature = $signFile;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($custom_title); ?> - <?php echo $custom_date; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
                font-size: 14px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .print-container {
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
            }
            
            .card {
                break-inside: avoid;
                border: none;
                box-shadow: none;
            }
            
            .table {
                width: 100%;
            }
            
            .page-break {
                page-break-after: always;
            }
            
            .teacher-serial {
                font-size: 11px !important;
                color: #495057 !important;
                background: #e9ecef !important;
                padding: 1px 4px !important;
                border-radius: 3px !important;
                border: 1px solid #dee2e6 !important;
            }
            
            /* Keep table headers and content together */
            .room-card {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                display: block !important;
            }
            
            thead {
                display: table-header-group !important;
            }
            
            tfoot {
                display: table-footer-group !important;
            }
            
            tr {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
            }
            
            /* Fix table layout */
            .teacher-table {
                table-layout: fixed !important;
            }
            
            /* Add empty footer to ensure content stays with header */
            .table-footer-spacer {
                height: 1px;
                visibility: hidden;
            }
        }
        
        .print-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .college-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .college-address {
            font-size: 16px;
            color: #555;
            margin-bottom: 5px;
        }
        
        .exam-title {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .duty-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .duty-date {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .room-card {
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .room-header {
            background: #eaf4ff;
            padding: 10px 15px;
            border-bottom: 2px solid #007bff;
        }
        
        .room-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 0;
        }
        
        .teacher-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        /* Larger photo for enhanced models */
        .large-photo {
            width: 65px;
            height: 65px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        /* Larger text */
        .large-text th {
            font-size: 18px;
            font-weight: 600;
        }
        
        .large-text td {
            font-size: 16px;
            font-weight: 500;
        }
        
        .teacher-serial {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
            margin-top: 2px;
        }
        
        .action-buttons {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 100;
        }
        
        .btn-print {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .btn-print:hover {
            transform: translateY(-5px);
            background: #0056b3;
            color: white;
        }
        
        /* Drag and Drop Styles */
        .sortable-row {
            cursor: move;
            transition: all 0.3s ease;
        }
        
        .sortable-row:hover {
            background-color: #f8f9fa !important;
        }
        
        .sortable-ghost {
            opacity: 0.4;
            background-color: #e3f2fd !important;
        }
        
        .sortable-chosen {
            background-color: #bbdefb !important;
        }
        
        .drag-handle {
            cursor: grab;
            color: #6c757d;
            padding: 5px;
        }
        
        .drag-handle:hover {
            color: #007bff;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .save-order-btn {
            position: fixed;
            bottom: 90px;
            right: 20px;
            z-index: 100;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: none;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: none;
        }
        
        .save-order-btn:hover {
            transform: translateY(-5px);
            background: #218838;
            color: white;
        }
        
        .save-order-btn.show {
            display: flex;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .alert-success {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: none;
        }
        
        tr.dragging {
            opacity: 0.5;
            background: #e3f2fd !important;
        }
        tr.drag-over {
            outline: 2px dashed #007bff;
            background: #f0f8ff !important;
        }

        /* Model 2: Compact Table */
        .compact-table th, .compact-table td {
            padding: 6px 8px;
            font-size: 14px;
        }
        .compact-table .teacher-photo {
            width: 30px;
            height: 30px;
        }

        /* Model 3: Bordered Table */
        .bordered-table th, .bordered-table td {
            border: 2px solid #000 !important;
        }
        .bordered-table th {
            background-color: #f0f0f0;
        }

        /* Model 4: Minimalist */
        .minimalist-table {
            border-collapse: collapse;
        }
        .minimalist-table th, .minimalist-table td {
            border: none;
            border-bottom: 1px solid #ddd;
        }
        .minimalist-table thead th {
            border-bottom: 2px solid #007bff;
        }

        /* Model 5: Colorful */
        .colorful-table thead th {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
        }
        .colorful-table tbody tr:nth-child(odd) {
            background-color: #f0f7ff;
        }
        .colorful-table tbody tr:nth-child(even) {
            background-color: #e6f0ff;
        }
        .colorful-table .room-header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            border-bottom: none;
        }
        .colorful-table .room-title {
            color: white;
        }
        
        /* Model 6: Card Based */
        .card-based-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            padding: 15px;
        }
        
        .teacher-card-item {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            background: white;
            box-shadow: 0 3px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s;
            page-break-inside: avoid;
            break-inside: avoid;
        }
        
        .teacher-card-item:hover {
            transform: translateY(-5px);
        }
        
        .teacher-card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .teacher-card-details {
            padding: 15px;
        }
        
        .teacher-card-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .teacher-card-info {
            font-size: 15px;
            color: #495057;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .teacher-card-signature {
            margin-top: 15px;
            border-top: 1px dashed #dee2e6;
            padding-top: 10px;
        }
        
        .signature-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .signature-line {
            height: 1px;
            background: #adb5bd;
            margin: 10px 0;
        }
        
        .attendance-time {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 14px;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="action-buttons no-print">
        <button onclick="window.print()" class="btn btn-print">
            <i class="fas fa-print fa-2x"></i>
        </button>
    </div>
    
    <button class="save-order-btn no-print" id="saveOrderBtn" onclick="saveTeacherOrder()">
        <i class="fas fa-save fa-lg loading-icon"></i>
        <div class="spinner-border spinner-border-sm loading-spinner" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </button>
    
    <div class="alert alert-success alert-dismissible fade" id="successAlert" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <span id="successMessage">শিক্ষকদের ক্রম সফলভাবে সংরক্ষিত হয়েছে!</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    
    <div class="container print-container">
        <div class="no-print mb-4">
            <form class="row g-2 align-items-end" id="customHeadingForm" onsubmit="return false;">
                <div class="col-md-2">
                    <label class="form-label">কলেজের নাম</label>
                    <input type="text" class="form-control" id="inputCollege" value="<?php echo htmlspecialchars($custom_college); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">ঠিকানা</label>
                    <input type="text" class="form-control" id="inputAddress" value="<?php echo htmlspecialchars($custom_address); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">পরীক্ষার নাম</label>
                    <input type="text" class="form-control" id="inputExam" value="<?php echo htmlspecialchars($custom_exam); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">হেডিং</label>
                    <input type="text" class="form-control" id="inputTitle" value="<?php echo htmlspecialchars($custom_title); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">তারিখ</label>
                    <input type="text" class="form-control" id="inputDate" value="<?php echo htmlspecialchars($custom_date); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">টেবিল মডেল</label>
                    <select class="form-select" id="tableModel" onchange="changeTableModel(this.value)">
                        <option value="default" <?php echo $model == 'default' ? 'selected' : ''; ?>>ডিফল্ট</option>
                        <option value="compact" <?php echo $model == 'compact' ? 'selected' : ''; ?>>কমপ্যাক্ট</option>
                        <option value="bordered" <?php echo $model == 'bordered' ? 'selected' : ''; ?>>বর্ডার সহ</option>
                        <option value="minimalist" <?php echo $model == 'minimalist' ? 'selected' : ''; ?>>মিনিমালিস্ট</option>
                        <option value="colorful" <?php echo $model == 'colorful' ? 'selected' : ''; ?>>রঙিন</option>
                        <option value="large" <?php echo $model == 'large' ? 'selected' : ''; ?>>বড় ফন্ট ও ছবি</option>
                        <option value="card" <?php echo $model == 'card' ? 'selected' : ''; ?>>কার্ড ভিউ</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="print-header">
            <?php if ($hasLogo): ?>
                <div class="text-center mb-2">
                    <img src="<?php echo $logoPath; ?>" alt="College Logo" style="height: 80px;">
                </div>
            <?php endif; ?>
            <div class="college-name" id="headingCollege"><?php echo htmlspecialchars($custom_college); ?></div>
            <div class="college-address" id="headingAddress"><?php echo htmlspecialchars($custom_address); ?></div>
            <div class="exam-title" id="headingExam"><?php echo htmlspecialchars($custom_exam); ?></div>
            <div class="duty-title" id="headingTitle"><?php echo htmlspecialchars($custom_title); ?></div>
            <div class="duty-date" id="headingDate">তারিখঃ <?php echo htmlspecialchars($custom_date); ?></div>
        </div>
        <script>
        // Live heading update
        document.addEventListener('DOMContentLoaded', function() {
            function updateHeading() {
                document.getElementById('headingCollege').textContent = document.getElementById('inputCollege').value;
                document.getElementById('headingAddress').textContent = document.getElementById('inputAddress').value;
                document.getElementById('headingExam').textContent = document.getElementById('inputExam').value;
                document.getElementById('headingTitle').textContent = document.getElementById('inputTitle').value;
                document.getElementById('headingDate').textContent = 'তারিখঃ ' + document.getElementById('inputDate').value;
            }
            ['inputCollege','inputAddress','inputExam','inputTitle','inputDate'].forEach(function(id) {
                document.getElementById(id).addEventListener('input', updateHeading);
            });
        });

        function changeTableModel(model) {
            // Update the URL with the new model parameter
            const url = new URL(window.location.href);
            url.searchParams.set('model', model);
            window.location.href = url.toString();
        }
        </script>
        
        <?php if (empty($roomAssignments)): ?>
            <div class="alert alert-warning text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                এই তারিখে কোন রুম ডিউটি বন্টন করা হয়নি!
            </div>
        <?php else: ?>
            <?php foreach ($roomAssignments as $roomNumber => $teachers): ?>
                <div class="room-card <?php echo $model == 'colorful' ? 'colorful-table' : ''; ?>">
                    <div class="room-header d-flex align-items-center justify-content-between">
                        <div class="room-title">
                            <i class="fas fa-door-open me-2"></i>
                            রুম নং: <?php echo $roomNumber; ?>
                        </div>
                        <span class="badge <?php echo $model == 'colorful' ? 'bg-light text-dark' : 'bg-primary'; ?> ms-2"><?php echo count($teachers); ?> জন শিক্ষক</span>
                    </div>
                    
                    <?php if ($model === 'card'): ?>
                    <div class="card-based-container">
                        <?php $i = 1; foreach ($teachers as $teacher): ?>
                            <div class="teacher-card-item">
                                <div class="teacher-card-header">
                                    <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                        <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="Teacher Photo" class="large-photo">
                                    <?php else: ?>
                                        <div class="large-photo d-flex align-items-center justify-content-center bg-light">
                                            <i class="fas fa-user fa-2x text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div>
                                        <div class="teacher-card-name"><?php echo htmlspecialchars($teacher['teacher_name']); ?></div>
                                        <?php if (!empty($teacher['sl_number'])): ?>
                                            <span class="badge bg-secondary">ক্রমিক: <?php echo htmlspecialchars($teacher['sl_number']); ?></span>
                                        <?php endif; ?>
                                        <div class="badge bg-primary">#<?php echo $i++; ?></div>
                                    </div>
                                </div>
                                <div class="teacher-card-details">
                                    <div class="teacher-card-info">
                                        <i class="fas fa-book text-primary"></i>
                                        <span><?php echo htmlspecialchars($teacher['subject']); ?></span>
                                    </div>
                                    <div class="teacher-card-info">
                                        <i class="fas fa-user-tie text-info"></i>
                                        <span><?php echo htmlspecialchars($teacher['designation']); ?></span>
                                    </div>
                                    <div class="teacher-card-signature">
                                        <div class="signature-label">উপস্থিতির সময়</div>
                                        <div class="attendance-time">
                                            <span>আগমন:</span>
                                            <span>_____________</span>
                                        </div>
                                        <div class="signature-label mt-3">স্বাক্ষর</div>
                                        <div class="signature-line"></div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="table-responsive">
                        <table class="table <?php 
                            if ($model == 'compact') echo 'compact-table';
                            elseif ($model == 'bordered') echo 'table-bordered bordered-table';
                            elseif ($model == 'minimalist') echo 'minimalist-table';
                            elseif ($model == 'colorful') echo 'colorful-table';
                            elseif ($model == 'large') echo 'table-bordered large-text';
                            else echo 'table-bordered table-striped';
                        ?> mb-0 teacher-table" data-room="<?php echo htmlspecialchars($roomNumber); ?>">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>ছবি</th>
                                    <th>নাম</th>
                                    <th>বিষয়</th>
                                    <th>পদবি</th>
                                    <th>উপস্থিতির সময়</th>
                                    <th>স্বাক্ষর</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 1; foreach ($teachers as $teacher): ?>
                                <tr draggable="true">
                                    <td class="serial-cell"><?php echo $i++; ?></td>
                                    <td>
                                        <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                            <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="Teacher Photo" class="<?php echo ($model == 'large') ? 'large-photo' : 'teacher-photo'; ?>">
                                        <?php else: ?>
                                            <div class="<?php echo ($model == 'large') ? 'large-photo' : 'teacher-photo'; ?> d-flex align-items-center justify-content-center bg-light">
                                                <i class="fas fa-user <?php echo ($model == 'large') ? 'fa-lg' : ''; ?> text-muted"></i>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo htmlspecialchars($teacher['teacher_name']); ?>
                                        <?php if (!empty($teacher['sl_number'])): ?>
                                            <br><span class="teacher-serial">ক্রমিক: <?php echo htmlspecialchars($teacher['sl_number']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                    <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-footer-spacer"><td colspan="7"></td></tr>
                            </tfoot>
                        </table>
                    </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="row mt-5">
            <div class="col-6 text-center">
                <div>
                    <?php if (!empty($convenerSignature) && file_exists($convenerSignature)): ?>
                        <img src="<?php echo $convenerSignature; ?>" alt="Convener Signature" style="height: 50px; margin-bottom: 5px;">
                    <?php endif; ?>
                    <div class="mt-4">____________________</div>
                </div>
                <div>পরীক্ষা কমিটির আহবায়ক</div>
            </div>
            <div class="col-6 text-center">
                <div>
                    <?php if (!empty($principalSignature) && file_exists($principalSignature)): ?>
                        <img src="<?php echo $principalSignature; ?>" alt="Principal Signature" style="height: 50px; margin-bottom: 5px;">
                    <?php endif; ?>
                    <div class="mt-4">____________________</div>
                </div>
                <div>অধ্যক্ষ</div>
            </div>
        </div>
    </div>
    
    <script>
    // Drag and drop for teacher rows
    document.addEventListener('DOMContentLoaded', function() {
        document.querySelectorAll('.teacher-table tbody').forEach(function(tbody) {
            let draggingRow = null;
            tbody.addEventListener('dragstart', function(e) {
                draggingRow = e.target;
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggingRow.outerHTML);
                draggingRow.classList.add('dragging');
            });
            tbody.addEventListener('dragover', function(e) {
                e.preventDefault();
                let target = e.target.closest('tr');
                if (target && target !== draggingRow) {
                    target.classList.add('drag-over');
                }
            });
            tbody.addEventListener('dragleave', function(e) {
                let target = e.target.closest('tr');
                if (target) {
                    target.classList.remove('drag-over');
                }
            });
            tbody.addEventListener('drop', function(e) {
                e.preventDefault();
                let target = e.target.closest('tr');
                if (draggingRow && target && draggingRow !== target) {
                    target.classList.remove('drag-over');
                    if (target.rowIndex > draggingRow.rowIndex) {
                        target.after(draggingRow);
                    } else {
                        target.before(draggingRow);
                    }
                    // Update serial numbers
                    tbody.querySelectorAll('tr').forEach(function(row, idx) {
                        row.querySelector('.serial-cell').textContent = idx + 1;
                    });
                }
                draggingRow.classList.remove('dragging');
                draggingRow = null;
            });
            tbody.addEventListener('dragend', function(e) {
                if (draggingRow) draggingRow.classList.remove('dragging');
                tbody.querySelectorAll('tr').forEach(function(row) {
                    row.classList.remove('drag-over');
                });
                draggingRow = null;
            });
        });
    });
    </script>
</body>
</html>
