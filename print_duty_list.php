<?php
require_once 'includes/teacher_db.php';
$teacherManager = new TeacherManager($pdo);
$dutyDates = $teacherManager->getAllDutyDates();
$date = $_GET['date'] ?? '';
$dateDetails = $teacherManager->getDutyDateDetails($date);
$dutyAssignments = $date ? $teacherManager->getDutyAssignments($date) : [];

// Bengali month names
$bengaliMonths = [
    1 => 'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
    'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
];

function formatDateBengali($dateString) {
    global $bengaliMonths;
    $date = new DateTime($dateString);
    $day = $date->format('d');
    $month = $bengaliMonths[(int)$date->format('m')];
    $year = $date->format('Y');
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি তালিকা - <?php echo formatDateBengali($date); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: white;
        }
        
        .header { 
            text-align: center; 
            margin-bottom: 30px;
            border-bottom: 3px solid #000;
            padding-bottom: 15px;
        }
        
        .header h1 {
            font-size: 28px;
            margin: 0;
            font-weight: bold;
            color: #000;
        }
        
        .header p {
            font-size: 18px;
            margin: 8px 0;
            color: #333;
        }
        
        .date-info {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 20px 0;
            background: #f0f0f0;
            padding: 10px;
            border-radius: 10px;
        }
        
        .duty-title {
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            margin: 20px 0;
            color: #333;
        }
        
        .total-teachers {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            background: #e3f2fd;
            padding: 10px;
            border-radius: 10px;
            border: 2px solid #2196f3;
        }
        
        /* Minimal, compact table for print */
        .duty-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            font-size: 13px;
        }
        .duty-table th, .duty-table td {
            border: 1px solid #222;
            padding: 4px 4px;
            text-align: center;
            font-size: 13px;
            background: #fff;
        }
        .duty-table th {
            font-weight: bold;
            background: #fff;
            color: #111;
        }
        .room-number {
            font-weight: bold;
            color: #111;
        }
        .duty-table tr {
            background: #fff;
        }
        .duty-table tr:nth-child(even) {
            background: #fff;
        }
        .duty-table tr:hover {
            background: #fff;
        }
        .duty-table th.name-col, .duty-table td.name-col { width: 180px; }
        .duty-table th, .duty-table td { font-size: 13px; }
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            .duty-table th, .duty-table td { font-size: 12px !important; }
            .header h1 {
                font-size: 32px !important;
            }
            .duty-title {
                font-size: 26px !important;
            }
            .total-teachers {
                font-size: 22px !important;
            }
            .date-info {
                font-size: 24px !important;
            }
            .no-print, .print-button, form, select, label {
                display: none !important;
            }
        }
        
        .footer {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            page-break-inside: avoid;
        }
        
        .signature {
            text-align: center;
            width: 250px;
        }
        
        .signature-line {
            border-top: 2px solid #000;
            margin-top: 60px;
            padding-top: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .print-button:hover {
            background: #0056b3;
        }
        
        .inline-teacher {
            display: inline-block;
            padding: 0 12px;
            border-right: 2px solid #bbb;
            margin-right: 0;
            min-width: 90px;
            text-align: center;
        }
        .inline-teacher:last-child {
            border-right: none;
        }
        
        .teacher-cell {
            border-bottom: 1px solid #bbb;
            padding: 4px 0 4px 0;
            min-width: 90px;
            text-align: center;
        }
        .teacher-cell:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4 mb-2 no-print">
        <form method="GET" class="row g-2 align-items-center">
            <div class="col-auto">
                <label for="date" class="form-label mb-0">তারিখ নির্বাচন করুন:</label>
            </div>
            <div class="col-auto">
                <select name="date" id="date" class="form-select" onchange="this.form.submit()" required>
                    <option value="">-- তারিখ নির্বাচন করুন --</option>
                    <?php foreach ($dutyDates as $d): ?>
                        <option value="<?php echo $d; ?>" <?php if($d==$date) echo 'selected'; ?>><?php echo formatDateBengali($d); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </form>
    </div>
    <?php if (!$date): ?>
        <div class="alert alert-info text-center">দয়া করে একটি তারিখ নির্বাচন করুন!</div>
    <?php else: ?>
    <button class="print-button no-print" onclick="window.print()">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="header">
        <h1>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h1>
        <p>দামুড়হুদা, চুয়াডাঙ্গা</p>
    </div>
    
    <div class="date-info">
        তারিখ: <?php echo formatDateBengali($date); ?>
    </div>
    
    <div class="duty-title">
        পরীক্ষা ডিউটি তালিকা
    </div>
    
    <div class="total-teachers">
        মোট ডিউটিরত শিক্ষক: <?php echo count($dutyAssignments); ?> জন
    </div>

    <!-- টেবিল ফরম্যাটে ডিউটি তালিকা -->
    <table class="duty-table">
        <thead>
            <tr>
                <th>ক্রমিক</th>
                <th>রুম নম্বর</th>
                <th class="name-col">নাম</th>
                <th>পদবী</th>
                <th>বিষয়</th>
                <th>মোবাইল</th>
                <th style="width:60px; min-width:60px;">উপস্থিতির সময়</th>
                <th style="width:140px; min-width:120px;">স্বাক্ষর</th>
            </tr>
        </thead>
        <tbody>
            <?php
            // Group assignments by room number and sort rooms
            $roomGroups = [];
            foreach ($dutyAssignments as $assignment) {
                $room = !empty($assignment['room_number']) ? $assignment['room_number'] : 'অনির্দিষ্ট';
                if (!isset($roomGroups[$room])) {
                    $roomGroups[$room] = [];
                }
                $roomGroups[$room][] = $assignment;
            }
            // Sort rooms naturally (numeric then string)
            uksort($roomGroups, function($a, $b) {
                if (is_numeric($a) && is_numeric($b)) {
                    return (int)$a - (int)$b;
                }
                return strnatcasecmp($a, $b);
            });
            $serial = 1;
            foreach ($roomGroups as $room => $teachers):
            ?>
            <tr>
                <td><?php echo $serial++; ?></td>
                <td class="room-number"><?php echo htmlspecialchars($room); ?></td>
                <td class="name-col"><?php foreach ($teachers as $t) echo htmlspecialchars($t['teacher_name']) . '<br>'; ?></td>
                <td><?php foreach ($teachers as $t) echo htmlspecialchars($t['designation']) . '<br>'; ?></td>
                <td><?php foreach ($teachers as $t) echo htmlspecialchars($t['subject']) . '<br>'; ?></td>
                <td><?php foreach ($teachers as $t) echo htmlspecialchars($t['mobile']) . '<br>'; ?></td>
                <td style="width:60px; min-width:60px;"><?php foreach ($teachers as $t) echo '&nbsp;<br>'; ?></td>
                <td style="width:140px; min-width:120px;"><?php foreach ($teachers as $t) echo '&nbsp;<br>'; ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <div class="footer">
        <div class="signature">
            <?php
            $principalSignature = $teacherManager->getActiveSignature('principal');
            if ($principalSignature && file_exists($principalSignature['signature_path'])):
            ?>
                <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>"
                     alt="Principal Signature" style="max-width: 150px; max-height: 60px; margin-bottom: 10px;">
            <?php endif; ?>
            <div class="signature-line">
                অধ্যক্ষ<br>
                আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                দামুড়হুদা, চুয়াডাঙ্গা
            </div>
        </div>
        <div class="signature">
            <?php
            $convenerSignature = $teacherManager->getActiveSignature('convener');
            if ($convenerSignature && file_exists($convenerSignature['signature_path'])):
            ?>
                <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>"
                     alt="Convener Signature" style="max-width: 150px; max-height: 60px; margin-bottom: 10px;">
            <?php endif; ?>
            <div class="signature-line">
                আহবায়ক<br>
                আবু জাফর মোঃ হাসিবুল আলম<br>
                HSC পরীক্ষা-২০২৫
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() {
        //     setTimeout(() => {
        //         window.print();
        //     }, 1000);
        // }
    </script>
</body>
</html>
