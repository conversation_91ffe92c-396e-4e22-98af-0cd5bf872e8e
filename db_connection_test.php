<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-good { color: #28a745; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .test-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .test-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Database Connection Test & Troubleshooting
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <h5><i class="fas fa-cog me-2"></i>Database Configuration</h5>
                        <div class="test-result test-success">
                            <strong>Host:</strong> localhost<br>
                            <strong>Username:</strong> root<br>
                            <strong>Password:</strong> (empty)<br>
                            <strong>Database:</strong> exmm
                        </div>
                        
                        <hr>
                        
                        <h5><i class="fas fa-check-circle me-2"></i>Connection Tests</h5>
                        
                        <?php
                        $host = 'localhost';
                        $username = 'root';
                        $password = '';
                        $dbname = 'exmm';
                        
                        // Test 1: Basic MySQL Connection
                        echo "<h6>1. MySQL Server Connection Test</h6>";
                        try {
                            $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                            echo "<div class='test-result test-success'>";
                            echo "<i class='fas fa-check-circle me-2'></i>";
                            echo "<strong>SUCCESS:</strong> MySQL server connection established successfully!";
                            echo "</div>";
                            
                            // Test 2: Database Existence
                            echo "<h6>2. Database Existence Test</h6>";
                            $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
                            $dbExists = $stmt->fetch();
                            
                            if ($dbExists) {
                                echo "<div class='test-result test-success'>";
                                echo "<i class='fas fa-check-circle me-2'></i>";
                                echo "<strong>SUCCESS:</strong> Database '$dbname' exists!";
                                echo "</div>";
                                
                                // Test 3: Database Connection
                                echo "<h6>3. Database Connection Test</h6>";
                                try {
                                    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                    echo "<div class='test-result test-success'>";
                                    echo "<i class='fas fa-check-circle me-2'></i>";
                                    echo "<strong>SUCCESS:</strong> Connected to database '$dbname' successfully!";
                                    echo "</div>";
                                    
                                    // Test 4: Tables Check
                                    echo "<h6>4. Tables Check</h6>";
                                    $stmt = $pdo->query('SHOW TABLES');
                                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                    
                                    if (empty($tables)) {
                                        echo "<div class='test-result test-warning'>";
                                        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                        echo "<strong>WARNING:</strong> No tables found in database. You may need to run setup.";
                                        echo "</div>";
                                    } else {
                                        echo "<div class='test-result test-success'>";
                                        echo "<i class='fas fa-check-circle me-2'></i>";
                                        echo "<strong>SUCCESS:</strong> Found " . count($tables) . " tables in database.";
                                        echo "<br><small>Tables: " . implode(', ', $tables) . "</small>";
                                        echo "</div>";
                                        
                                        // Test 5: Students Table Check
                                        if (in_array('students', $tables)) {
                                            echo "<h6>5. Students Table Test</h6>";
                                            try {
                                                $stmt = $pdo->query('SELECT COUNT(*) FROM students');
                                                $count = $stmt->fetchColumn();
                                                echo "<div class='test-result test-success'>";
                                                echo "<i class='fas fa-check-circle me-2'></i>";
                                                echo "<strong>SUCCESS:</strong> Students table accessible. Found $count records.";
                                                echo "</div>";
                                            } catch (Exception $e) {
                                                echo "<div class='test-result test-error'>";
                                                echo "<i class='fas fa-times-circle me-2'></i>";
                                                echo "<strong>ERROR:</strong> Cannot access students table: " . $e->getMessage();
                                                echo "</div>";
                                            }
                                        }
                                    }
                                    
                                } catch (PDOException $e) {
                                    echo "<div class='test-result test-error'>";
                                    echo "<i class='fas fa-times-circle me-2'></i>";
                                    echo "<strong>ERROR:</strong> Cannot connect to database '$dbname': " . $e->getMessage();
                                    echo "</div>";
                                }
                                
                            } else {
                                echo "<div class='test-result test-warning'>";
                                echo "<i class='fas fa-exclamation-triangle me-2'></i>";
                                echo "<strong>WARNING:</strong> Database '$dbname' does not exist!";
                                echo "</div>";
                            }
                            
                        } catch (PDOException $e) {
                            echo "<div class='test-result test-error'>";
                            echo "<i class='fas fa-times-circle me-2'></i>";
                            echo "<strong>ERROR:</strong> Cannot connect to MySQL server: " . $e->getMessage();
                            echo "</div>";
                            
                            echo "<h6><i class='fas fa-tools me-2'></i>Troubleshooting Steps:</h6>";
                            echo "<div class='test-result test-warning'>";
                            echo "<ol>";
                            echo "<li><strong>Check XAMPP:</strong> Make sure XAMPP is installed and running</li>";
                            echo "<li><strong>Start MySQL:</strong> Open XAMPP Control Panel and start MySQL service</li>";
                            echo "<li><strong>Check Port:</strong> Ensure MySQL is running on port 3306</li>";
                            echo "<li><strong>Check Firewall:</strong> Make sure Windows Firewall is not blocking MySQL</li>";
                            echo "<li><strong>Restart Services:</strong> Try restarting both Apache and MySQL services</li>";
                            echo "</ol>";
                            echo "</div>";
                        }
                        ?>
                        
                        <hr>
                        
                        <h5><i class='fas fa-tools me-2'></i>Quick Actions</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <a href="setup_database.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-database me-2"></i>Setup Database
                                </a>
                                <a href="database_troubleshooting.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-search me-2"></i>Advanced Diagnostics
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="index.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-home me-2"></i>Go to Home
                                </a>
                                <button onclick="location.reload()" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-refresh me-2"></i>Refresh Test
                                </button>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Common Solutions:</h6>
                            <ul class="mb-0">
                                <li><strong>XAMPP not running:</strong> Open XAMPP Control Panel and start Apache & MySQL</li>
                                <li><strong>Port conflict:</strong> Change MySQL port in XAMPP config if 3306 is occupied</li>
                                <li><strong>Permission issues:</strong> Run XAMPP as Administrator</li>
                                <li><strong>Database missing:</strong> Click "Setup Database" button above</li>
                            </ul>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
