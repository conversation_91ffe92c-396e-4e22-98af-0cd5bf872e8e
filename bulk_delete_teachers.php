<?php
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['teacher_ids']) && is_array($_POST['teacher_ids'])) {
        try {
            $teacherIds = $_POST['teacher_ids'];
            
            if ($teacherManager->bulkDeleteTeachers($teacherIds)) {
                $count = count($teacherIds);
                $message = "{$count} জন শিক্ষক সফলভাবে মুছে ফেলা হয়েছে!";
                $messageType = 'success';
            } else {
                $message = 'শিক্ষক মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'কোন শিক্ষক নির্বাচিত নেই।';
        $messageType = 'warning';
    }
}

// Get current teachers for display
$teachers = $teacherManager->getAllTeachers();
$totalTeachers = count($teachers);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক বাল্ক ডিলিট - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 1000px;
            margin: 0 auto;
        }
        .teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .teacher-card:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .teacher-card.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .teacher-photo {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .teacher-placeholder {
            width: 50px;
            height: 50px;
            background: #f8f9fa;
            border: 2px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 20px;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-normal { background: #e3f2fd; color: #1976d2; }
        .status-always { background: #e8f5e8; color: #2e7d32; }
        .status-never { background: #ffebee; color: #c62828; }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-users text-danger"></i> শিক্ষক বাল্ক ডিলিট</h1>
            <p class="text-muted">একাধিক শিক্ষক একসাথে মুছে ফেলুন</p>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Navigation -->
        <div class="mb-4">
            <a href="teacher_duty_management.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>শিক্ষক ব্যবস্থাপনায় ফিরুন
            </a>
            <a href="delete_all_teachers.php" class="btn btn-danger ms-2">
                <i class="fas fa-trash-alt me-2"></i>সকল শিক্ষক মুছুন
            </a>
        </div>

        <?php if ($totalTeachers > 0): ?>
            <form method="POST" id="bulkDeleteForm" onsubmit="return confirmBulkDelete()">
                <!-- Selection Controls -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-square me-2"></i>শিক্ষক নির্বাচন
                            <span class="badge bg-light text-dark ms-2">মোট: <?php echo $totalTeachers; ?> জন</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 mb-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="selectAll()">
                                <i class="fas fa-check-double me-1"></i>সব নির্বাচন
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearAll()">
                                <i class="fas fa-times me-1"></i>নির্বাচন মুছুন
                            </button>
                            <span class="ms-auto">
                                <span id="selectedCount">0</span> জন নির্বাচিত
                            </span>
                        </div>
                        
                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                            <i class="fas fa-trash me-2"></i>নির্বাচিত শিক্ষক মুছুন
                        </button>
                    </div>
                </div>

                <!-- Teachers List -->
                <div class="row">
                    <?php foreach ($teachers as $teacher): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="teacher-card" onclick="toggleSelection(<?php echo $teacher['id']; ?>)">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="teacher_ids[]" 
                                           value="<?php echo $teacher['id']; ?>" id="teacher_<?php echo $teacher['id']; ?>"
                                           onchange="updateSelectionCount()">
                                    <label class="form-check-label w-100" for="teacher_<?php echo $teacher['id']; ?>">
                                        <div class="d-flex align-items-start">
                                            <div class="me-3">
                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" 
                                                         alt="<?php echo htmlspecialchars($teacher['name']); ?>" 
                                                         class="teacher-photo">
                                                <?php else: ?>
                                                    <div class="teacher-placeholder">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                <p class="text-muted small mb-2">
                                                    <i class="fas fa-hashtag me-1"></i>SL: <?php echo htmlspecialchars($teacher['sl_number'] ?? 'N/A'); ?><br>
                                                    <i class="fas fa-book me-1"></i><?php echo htmlspecialchars($teacher['subject']); ?><br>
                                                    <i class="fas fa-id-badge me-1"></i><?php echo htmlspecialchars($teacher['designation']); ?><br>
                                                    <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($teacher['mobile']); ?>
                                                </p>
                                                <span class="status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal'); ?>">
                                                    <?php echo $teacher['duty_status']; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </form>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-5x text-muted mb-3"></i>
                <h3>কোন শিক্ষক পাওয়া যায়নি</h3>
                <p class="text-muted">প্রথমে শিক্ষক যুক্ত করুন।</p>
                <a href="teacher_duty_management.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>শিক্ষক যুক্ত করুন
                </a>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectAll() {
            const checkboxes = document.querySelectorAll('input[name="teacher_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.closest('.teacher-card').classList.add('selected');
            });
            updateSelectionCount();
        }

        function clearAll() {
            const checkboxes = document.querySelectorAll('input[name="teacher_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('.teacher-card').classList.remove('selected');
            });
            updateSelectionCount();
        }

        function toggleSelection(teacherId) {
            const checkbox = document.getElementById('teacher_' + teacherId);
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                checkbox.closest('.teacher-card').classList.add('selected');
            } else {
                checkbox.closest('.teacher-card').classList.remove('selected');
            }
            
            updateSelectionCount();
        }

        function updateSelectionCount() {
            const checkedBoxes = document.querySelectorAll('input[name="teacher_ids[]"]:checked');
            const count = checkedBoxes.length;
            
            document.getElementById('selectedCount').textContent = count;
            document.getElementById('deleteBtn').disabled = count === 0;
        }

        function confirmBulkDelete() {
            const checkedBoxes = document.querySelectorAll('input[name="teacher_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                alert('অনুগ্রহ করে কমপক্ষে একজন শিক্ষক নির্বাচন করুন।');
                return false;
            }
            
            const count = checkedBoxes.length;
            const message = `আপনি কি নিশ্চিত যে ${count} জন শিক্ষককে মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।\n\nমুছে যাবে:\n- শিক্ষকের সব তথ্য\n- শিক্ষকের ছবি\n- ডিউটি বন্টন তথ্য`;
            return confirm(message);
        }

        // Initialize selection count
        updateSelectionCount();
    </script>
</body>
</html>
