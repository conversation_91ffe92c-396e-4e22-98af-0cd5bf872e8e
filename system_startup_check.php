<?php
// System Startup Check and Auto-Fix Script
// This script checks and fixes common issues after PC restart

$results = [];
$autoFixed = [];
$errors = [];

// Function to check and create directory with proper permissions
function ensureDirectory($path, $permissions = 0777) {
    if (!is_dir($path)) {
        if (mkdir($path, $permissions, true)) {
            return "✅ Created directory: $path";
        } else {
            return "❌ Failed to create directory: $path";
        }
    } else {
        // Check if directory is writable
        if (is_writable($path)) {
            return "✅ Directory exists and writable: $path";
        } else {
            // Try to fix permissions
            if (chmod($path, $permissions)) {
                return "✅ Fixed permissions for: $path";
            } else {
                return "❌ Directory exists but not writable: $path";
            }
        }
    }
}

// Function to test database connection and auto-create database if needed
function checkAndFixDatabase() {
    $host = 'localhost';
    $username = 'root';
    $password = '';
    $dbname = 'exmm';
    
    try {
        // First, try to connect to MySQL server
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if database exists
        $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
        $dbExists = $stmt->fetch();
        
        if (!$dbExists) {
            // Create database
            $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $result = "✅ Auto-created database: $dbname";
        } else {
            $result = "✅ Database exists: $dbname";
        }
        
        // Now connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Check if students table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'students'");
        $tableExists = $stmt->fetch();
        
        if (!$tableExists) {
            // Create students table
            $createTableSQL = "
                CREATE TABLE `students` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL,
                    `father_name` varchar(255) DEFAULT NULL,
                    `gender` enum('Male','Female','Other') DEFAULT 'Male',
                    `roll` varchar(50) NOT NULL,
                    `registration` varchar(100) DEFAULT NULL,
                    `department` varchar(100) DEFAULT NULL,
                    `academic_year` varchar(20) DEFAULT NULL,
                    `student_type` enum('Regular','Irregular','Improvement') DEFAULT 'Regular',
                    `subjects` text DEFAULT NULL,
                    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `unique_roll` (`roll`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $pdo->exec($createTableSQL);
            $result .= "\n✅ Auto-created students table";
        } else {
            $result .= "\n✅ Students table exists";
        }
        
        return $result;
        
    } catch (PDOException $e) {
        return "❌ Database error: " . $e->getMessage();
    }
}

// Function to check PHP configuration
function checkPHPConfig() {
    $results = [];
    
    // Check file upload settings
    $uploadMaxFilesize = ini_get('upload_max_filesize');
    $postMaxSize = ini_get('post_max_size');
    $maxExecutionTime = ini_get('max_execution_time');
    $memoryLimit = ini_get('memory_limit');
    
    $results[] = "📋 PHP Upload Settings:";
    $results[] = "   upload_max_filesize: $uploadMaxFilesize";
    $results[] = "   post_max_size: $postMaxSize";
    $results[] = "   max_execution_time: $maxExecutionTime seconds";
    $results[] = "   memory_limit: $memoryLimit";
    
    // Check if file uploads are enabled
    if (ini_get('file_uploads')) {
        $results[] = "✅ File uploads: ENABLED";
    } else {
        $results[] = "❌ File uploads: DISABLED";
    }
    
    return implode("\n", $results);
}

// Function to check and fix file permissions
function checkFilePermissions() {
    $results = [];
    $filesToCheck = [
        'csv_upload.php',
        'config/database.php',
        'models/Student.php',
        'utils/SimpleCSVProcessor.php'
    ];
    
    foreach ($filesToCheck as $file) {
        if (file_exists($file)) {
            if (is_readable($file)) {
                $results[] = "✅ Readable: $file";
            } else {
                $results[] = "❌ Not readable: $file";
            }
        } else {
            $results[] = "❌ Missing file: $file";
        }
    }
    
    return implode("\n", $results);
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Startup Check - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .check-result {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-line;
        }
        
        .check-error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-line;
        }
        
        .auto-refresh {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="auto-refresh">
        <button onclick="location.reload()" class="btn btn-primary">
            <i class="fas fa-refresh"></i> Refresh Check
        </button>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            System Startup Check & Auto-Fix
                        </h3>
                        <small>PC restart এর পর CSV upload সমস্যার স্থায়ী সমাধান</small>
                    </div>
                    <div class="card-body">
                        
                        <h5><i class="fas fa-folder me-2"></i>1. Directory Check & Auto-Fix</h5>
                        <div class="check-result">
<?php
// Check and create necessary directories
$directories = [
    __DIR__ . '/uploads',
    __DIR__ . '/config',
    __DIR__ . '/models',
    __DIR__ . '/utils',
    __DIR__ . '/includes'
];

foreach ($directories as $dir) {
    echo ensureDirectory($dir) . "\n";
}
?>
                        </div>
                        
                        <h5><i class="fas fa-database me-2"></i>2. Database Check & Auto-Fix</h5>
                        <div class="check-result">
<?php echo checkAndFixDatabase(); ?>
                        </div>
                        
                        <h5><i class="fas fa-server me-2"></i>3. PHP Configuration Check</h5>
                        <div class="check-result">
<?php echo checkPHPConfig(); ?>
                        </div>
                        
                        <h5><i class="fas fa-lock me-2"></i>4. File Permissions Check</h5>
                        <div class="check-result">
<?php echo checkFilePermissions(); ?>
                        </div>
                        
                        <h5><i class="fas fa-test-tube me-2"></i>5. CSV Upload Test</h5>
                        <div class="check-result">
<?php
// Test CSV upload functionality
try {
    require_once 'config/database.php';
    require_once 'models/Student.php';
    
    $database = new Database();
    $connection = $database->getConnection();
    echo "✅ Database connection: SUCCESS\n";
    
    $student = new Student();
    $count = $student->getTotalCount();
    echo "✅ Student model: Working (Total students: $count)\n";
    
    // Check if uploads directory is writable
    $uploadsDir = __DIR__ . '/uploads';
    if (is_writable($uploadsDir)) {
        echo "✅ Uploads directory: Writable\n";
    } else {
        echo "❌ Uploads directory: Not writable\n";
    }
    
    echo "✅ CSV Upload System: READY";
    
} catch (Exception $e) {
    echo "❌ CSV Upload Test Failed: " . $e->getMessage();
}
?>
                        </div>
                        
                        <hr>
                        
                        <h5><i class="fas fa-tools me-2"></i>Quick Actions</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <a href="csv_upload.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-upload me-2"></i>Test CSV Upload
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="setup_database.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-database me-2"></i>Setup Database
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="db_connection_test.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-search me-2"></i>DB Connection Test
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="index.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-home me-2"></i>Go to Home
                                </a>
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
