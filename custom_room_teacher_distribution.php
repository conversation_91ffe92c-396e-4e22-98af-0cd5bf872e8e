<?php
// custom_room_teacher_distribution.php
// উন্নত কাস্টম রুম-শিক্ষক ডিস্ট্রিবিউশন পেজ
require_once 'includes/teacher_db.php';
$teacherManager = new TeacherManager($pdo);
$teachers = [];
$date_selected = isset($_POST['date']) ? $_POST['date'] : '';
if ($date_selected) {
    // If form submitted, get only teachers assigned on that date
    $stmt = $pdo->prepare('SELECT teacher_id FROM duty_assignments WHERE duty_date = ?');
    $stmt->execute([$date_selected]);
    $ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if ($ids) {
        $in = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "SELECT id, name, subject, designation FROM teachers WHERE id IN ($in) ORDER BY name ASC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($ids);
        $teachers = $stmt->fetchAll();
    }
}

// Handle form submission
$distribution = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['rooms'], $_POST['teachers'], $_POST['date'])) {
    $rooms = $_POST['rooms'];
    $teachers_selected = $_POST['teachers'];
    $date = $_POST['date'];
    shuffle($teachers_selected);
    $teacher_idx = 0;
    $total_teachers = count($teachers_selected);
    foreach ($rooms as $room) {
        $room_no = htmlspecialchars($room['room']);
        $seat_count = (int)$room['seats'];
        $teacher_count = isset($room['teacher_count']) ? (int)$room['teacher_count'] : 1;
        $assigned_teachers = [];
        for ($i = 0; $i < $teacher_count; $i++) {
            $teacher_id = isset($teachers_selected[$teacher_idx]) ? $teachers_selected[$teacher_idx] : '-';
            $teacher_info = '-';
            foreach ($teachers as $t) {
                if ($t['id'] == $teacher_id) {
                    $teacher_info = $t['name'] . ' (' . $t['subject'] . ', ' . $t['designation'] . ')';
                    break;
                }
            }
            $assigned_teachers[] = $teacher_info;
            $teacher_idx++;
            if ($teacher_idx >= $total_teachers) {
                $teacher_idx = 0;
            }
        }
        $distribution[] = [
            'room' => $room_no,
            'seats' => $seat_count,
            'teachers' => $assigned_teachers
        ];
    }
}

// Function to format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কাস্টম রুম-শিক্ষক ডিস্ট্রিবিউশন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .feature-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .room-row { margin-bottom: 10px; }
        .select-all-box { margin-bottom: 5px; }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table td, .table th {
            vertical-align: middle;
        }

        .form-control, .form-select {
            border-radius: 8px;
            padding: 0.6rem 1rem;
            border: 1px solid #dee2e6;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }

        /* Drag and Drop Styles */
        .teacher-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 8px 12px;
            margin-bottom: 8px;
            cursor: move;
            transition: all 0.2s;
            position: relative;
        }
        
        .teacher-item:hover {
            background: #e9ecef;
        }
        
        .teacher-item.dragging {
            opacity: 0.5;
            border: 2px dashed #6c757d;
        }
        
        .teacher-container {
            min-height: 50px;
            padding: 5px;
            border-radius: 5px;
        }
        
        .teacher-container.drag-over {
            background-color: #e8f4ff;
            border: 2px dashed #4a9fff;
        }
        
        .drag-controls {
            position: absolute;
            right: 10px;
            top: 8px;
            color: #6c757d;
            font-size: 14px;
        }
        
        .drag-controls i {
            margin-left: 5px;
            cursor: pointer;
        }
        
        .drag-controls i:hover {
            color: #495057;
        }
        
        .drag-help {
            background-color: #fff3cd;
            color: #664d03;
            padding: 10px 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .edit-note {
            color: #0d6efd;
            font-size: 0.85rem;
            margin-left: 8px;
        }
        
        .attendance-column {
            width: 140px;
            min-width: 140px;
        }
        
        .signature-column {
            width: 180px;
            min-width: 180px;
        }
        
        .signature-box {
            height: 60px;
            border: 1px dashed #ced4da;
            margin-top: 5px;
            margin-bottom: 5px;
            border-radius: 5px;
        }

        /* Updated table styles */
        .teacher-row {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            margin-bottom: 10px;
            width: 100%;
        }
        
        .teacher-name {
            flex: 1;
            font-weight: 500;
            position: relative;
            padding-right: 40px;
        }
        
        .attendance-box {
            width: 150px;
            margin: 0 10px;
        }
        
        .signature-box {
            height: 40px;
            width: 150px;
            border: 1px dashed #ced4da;
            border-radius: 5px;
            margin-left: 10px;
        }
        
        .time-label, .signature-label {
            display: block;
            font-size: 11px;
            color: #6c757d;
            margin-bottom: 3px;
        }
        
        /* Print styles update */
        @media print {
            .no-print {
                display: none;
            }
            body {
                background: white !important;
            }
            .main-content {
                margin-left: 0 !important;
                padding: 0 !important;
            }
            .distribution-card {
                border: none !important;
                box-shadow: none !important;
            }
            .teacher-item {
                background: none;
                border: none;
                padding: 8px 0;
                margin-bottom: 20px;
            }
            .drag-controls {
                display: none;
            }
            .teacher-row {
                background: none;
                border: none;
                padding: 12px 0;
                border-bottom: 1px dotted #ccc;
                break-inside: avoid;
                page-break-inside: avoid;
            }
            .signature-box {
                height: 40px;
                border: 1px dashed #999;
            }
            .attendance-box {
                border-bottom: 1px dotted #999;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-random me-3"></i>কাস্টম রুম-শিক্ষক ডিস্ট্রিবিউশন
                </h1>
                <p class="text-white-50">রুমে শিক্ষক কাস্টমাইজড বণ্টন সিস্টেম</p>
            </div>

            <!-- Main Form -->
            <div class="main-card">
                <form method="post" id="distributionForm">
                    <div class="feature-section">
                        <h4 class="mb-3">
                            <i class="fas fa-calendar-day me-2 text-primary"></i>তারিখ নির্বাচন করুন
                        </h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="date" class="form-label">Date (তারিখ)</label>
                                    <input type="date" class="form-control" id="date" name="date" value="<?php echo htmlspecialchars($date_selected); ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="feature-section">
                        <h4 class="mb-3">
                            <i class="fas fa-door-open me-2 text-primary"></i>রুম, আসন ও শিক্ষক সংখ্যা
                        </h4>
                        <div id="roomsContainer">
                            <div class="room-row row g-2 mb-2">
                                <div class="col-md-4 col-12 mb-2 mb-md-0">
                                    <input type="text" name="rooms[0][room]" class="form-control" placeholder="Room No (রুম নম্বর)" required>
                                </div>
                                <div class="col-md-3 col-6">
                                    <input type="number" name="rooms[0][seats]" class="form-control" placeholder="Seat Count (আসন সংখ্যা)" min="1" required>
                                </div>
                                <div class="col-md-3 col-6">
                                    <input type="number" name="rooms[0][teacher_count]" class="form-control" placeholder="Teacher Count (শিক্ষক সংখ্যা)" min="1" value="1" required>
                                </div>
                                <div class="col-md-2 col-12 d-flex align-items-center">
                                    <button type="button" class="btn btn-danger remove-room w-100" style="display:none;">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <button type="button" class="btn btn-secondary mt-2 mb-3" id="addRoomBtn">
                            <i class="fas fa-plus-circle me-2"></i>Add Room (রুম যোগ করুন)
                        </button>
                    </div>

                    <div class="feature-section">
                        <h4 class="mb-3">
                            <i class="fas fa-chalkboard-teacher me-2 text-primary"></i>শিক্ষক নির্বাচন করুন
                        </h4>
                        <div class="select-all-box">
                            <div class="form-check">
                                <input type="checkbox" id="selectAllTeachers" class="form-check-input"> 
                                <label for="selectAllTeachers" class="form-check-label">Select All (সব নির্বাচন করুন)</label>
                            </div>
                        </div>
                        <select name="teachers[]" class="form-select" id="teacherSelect" multiple required size="8" <?php if (!$teachers) echo 'disabled'; ?>>
                            <?php if ($teachers): ?>
                                <?php foreach ($teachers as $teacher): ?>
                                    <option value="<?php echo $teacher['id']; ?>">
                                        <?php echo htmlspecialchars($teacher['name']) . ' (' . htmlspecialchars($teacher['subject']) . ', ' . htmlspecialchars($teacher['designation']) . ')'; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="">Please select a date with assigned teachers (এই তারিখে কোনো শিক্ষক নেই)</option>
                            <?php endif; ?>
                        </select>
                        <small class="text-muted d-block mt-1">Hold Ctrl (Windows) or Command (Mac) to select multiple.</small>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-custom">
                            <i class="fas fa-random me-2"></i>Distribute (বণ্টন করুন)
                        </button>
                    </div>
                </form>
            </div>

            <?php if (!empty($distribution)): ?>
                <div class="main-card">
                    <h4 class="mb-3">
                        <i class="fas fa-clipboard-list me-2 text-primary"></i>ডিস্ট্রিবিউশন রেজাল্ট
                        <span class="edit-note no-print"><i class="fas fa-info-circle me-1"></i>শিক্ষকদের ড্র্যাগ করে যেকোন রুমে নিতে পারেন এবং উপর-নিচ করতে পারেন</span>
                    </h4>
                    
                    <div class="drag-help no-print">
                        <i class="fas fa-lightbulb me-2"></i> টিপস: 
                        <ul class="mb-0">
                            <li>শিক্ষকদের অন্য রুমে নিতে ড্র্যাগ করুন</li>
                            <li>একই রুমে উপরে/নিচে নিতে উপর/নিচের আইকন ব্যবহার করুন</li>
                            <li>প্রিন্টে উপস্থিতির সময় ও স্বাক্ষর কলাম দেখাবে</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3 no-print">
                        <button class="btn btn-outline-secondary me-2" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Print (প্রিন্ট করুন)
                        </button>
                        <button class="btn btn-outline-success" onclick="downloadCSV()">
                            <i class="fas fa-file-csv me-2"></i>Download CSV (ডাউনলোড করুন)
                        </button>
                    </div>
                    
                    <?php if ($date_selected): ?>
                        <div class="alert alert-info mb-3">
                            <strong><i class="fas fa-calendar-alt me-2"></i>নির্বাচিত তারিখ:</strong> 
                            <?php echo formatDateBengali($date_selected); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-hover" id="resultTable">
                            <thead>
                                <tr>
                                    <th>Room (রুম)</th>
                                    <th>Seat Count (আসন সংখ্যা)</th>
                                    <th>Teachers & Attendance (শিক্ষকগণ, উপস্থিতি ও স্বাক্ষর)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($distribution as $index => $row): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['room']); ?></td>
                                        <td><?php echo htmlspecialchars($row['seats']); ?></td>
                                        <td>
                                            <div class="teacher-container" id="room-<?php echo $index; ?>" data-room="<?php echo htmlspecialchars($row['room']); ?>">
                                                <?php foreach ($row['teachers'] as $t_index => $t): ?>
                                                    <div class="teacher-item" draggable="true" data-teacher="<?php echo htmlspecialchars($t); ?>">
                                                        <div class="teacher-row">
                                                            <div class="teacher-name">
                                                                <?php echo htmlspecialchars($t); ?>
                                                                <div class="drag-controls no-print">
                                                                    <i class="fas fa-arrow-up move-up" title="Move Up"></i>
                                                                    <i class="fas fa-arrow-down move-down" title="Move Down"></i>
                                                                    <i class="fas fa-arrows-alt move" title="Drag to Another Room"></i>
                                                                </div>
                                                            </div>
                                                            <div class="attendance-field">
                                                                <small class="time-label">Attendance Time (উপস্থিতির সময়)</small>
                                                                <div class="attendance-box">_____________</div>
                                                            </div>
                                                            <div class="signature-field">
                                                                <small class="signature-label">Signature (স্বাক্ষর)</small>
                                                                <div class="signature-box"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

        </div>
    </div>

    <script>
    // Add/Remove room rows dynamically
    let roomIdx = 1;
    document.getElementById('addRoomBtn').onclick = function() {
        const container = document.getElementById('roomsContainer');
        const row = document.createElement('div');
        row.className = 'room-row row g-2 mb-2';
        row.innerHTML = `
            <div class="col-md-4 col-12 mb-2 mb-md-0">
                <input type="text" name="rooms[${roomIdx}][room]" class="form-control" placeholder="Room No (রুম নম্বর)" required>
            </div>
            <div class="col-md-3 col-6">
                <input type="number" name="rooms[${roomIdx}][seats]" class="form-control" placeholder="Seat Count (আসন সংখ্যা)" min="1" required>
            </div>
            <div class="col-md-3 col-6">
                <input type="number" name="rooms[${roomIdx}][teacher_count]" class="form-control" placeholder="Teacher Count (শিক্ষক সংখ্যা)" min="1" value="1" required>
            </div>
            <div class="col-md-2 col-12 d-flex align-items-center">
                <button type="button" class="btn btn-danger remove-room w-100">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        `;
        container.appendChild(row);
        roomIdx++;
    };
    document.getElementById('roomsContainer').addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-room') || e.target.closest('.remove-room')) {
            e.target.closest('.room-row').remove();
        }
    });
    // Select All Teachers functionality
    document.getElementById('selectAllTeachers').onclick = function() {
        const select = document.getElementById('teacherSelect');
        for (let i = 0; i < select.options.length; i++) {
            select.options[i].selected = this.checked;
        }
    };
    // AJAX to load teachers by date
    document.getElementById('date').addEventListener('change', function() {
        const date = this.value;
        const select = document.getElementById('teacherSelect');
        select.innerHTML = '<option>Loading...</option>';
        select.disabled = true;
        fetch('ajax/get_teachers_by_date.php?date=' + encodeURIComponent(date))
            .then(res => res.json())
            .then(data => {
                select.innerHTML = '';
                if (data.length === 0) {
                    select.innerHTML = '<option value="">No teachers assigned for this date (এই তারিখে কোনো শিক্ষক নেই)</option>';
                } else {
                    data.forEach(function(teacher) {
                        const opt = document.createElement('option');
                        opt.value = teacher.id;
                        opt.textContent = teacher.name + ' (' + teacher.subject + ', ' + teacher.designation + ')';
                        select.appendChild(opt);
                    });
                }
                select.disabled = data.length === 0;
            });
    });
    // Drag and Drop & Reorder Functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Variables to store drag data
        let draggedItem = null;
        let sourceContainer = null;
        
        // Get all draggable items
        const teacherItems = document.querySelectorAll('.teacher-item');
        // Get all teacher containers
        const teacherContainers = document.querySelectorAll('.teacher-container');
        
        // Add event listeners to each teacher item
        teacherItems.forEach(item => {
            // When drag starts
            item.addEventListener('dragstart', function(e) {
                draggedItem = this;
                sourceContainer = this.parentElement;
                setTimeout(() => {
                    this.classList.add('dragging');
                }, 0);
                e.dataTransfer.setData('text/plain', this.dataset.teacher);
            });
            
            // When drag ends
            item.addEventListener('dragend', function() {
                this.classList.remove('dragging');
                teacherContainers.forEach(container => {
                    container.classList.remove('drag-over');
                });
                updateAttendanceColumns();
            });
        });
        
        // Add event listeners to each teacher container
        teacherContainers.forEach(container => {
            // When item enters container
            container.addEventListener('dragenter', function(e) {
                e.preventDefault();
                if (this !== sourceContainer) {
                    this.classList.add('drag-over');
                }
            });
            
            // When item is over container
            container.addEventListener('dragover', function(e) {
                e.preventDefault();
            });
            
            // When item leaves container
            container.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });
            
            // When item is dropped in container
            container.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                
                // Only allow drop if this is not the source container
                if (sourceContainer !== this) {
                    this.appendChild(draggedItem);
                }
            });
        });
        
        // Moving teachers up and down within the same container
        document.addEventListener('click', function(e) {
            // Move up button
            if (e.target.classList.contains('move-up')) {
                const item = e.target.closest('.teacher-item');
                const prev = item.previousElementSibling;
                if (prev) {
                    item.parentNode.insertBefore(item, prev);
                    updateAttendanceColumns();
                }
            }
            
            // Move down button
            if (e.target.classList.contains('move-down')) {
                const item = e.target.closest('.teacher-item');
                const next = item.nextElementSibling;
                if (next) {
                    item.parentNode.insertBefore(next, item);
                    updateAttendanceColumns();
                }
            }
        });

        // Update attendance and signature columns when teachers are reordered
        function updateAttendanceColumns() {
            const rows = document.querySelectorAll('#resultTable tbody tr');
            rows.forEach(row => {
                // Create new layout automatically for each teacher item
                const teacherItems = row.querySelector('.teacher-container').querySelectorAll('.teacher-item');
                
                teacherItems.forEach(item => {
                    // Get the teacher name from data attribute
                    const teacherName = item.dataset.teacher;
                    
                    // Create the new layout with attendance and signature fields
                    item.innerHTML = `
                        <div class="teacher-row">
                            <div class="teacher-name">
                                ${teacherName}
                                <div class="drag-controls no-print">
                                    <i class="fas fa-arrow-up move-up" title="Move Up"></i>
                                    <i class="fas fa-arrow-down move-down" title="Move Down"></i>
                                    <i class="fas fa-arrows-alt move" title="Drag to Another Room"></i>
                                </div>
                            </div>
                            <div class="attendance-field">
                                <small class="time-label">Attendance Time (উপস্থিতির সময়)</small>
                                <div class="attendance-box">_____________</div>
                            </div>
                            <div class="signature-field">
                                <small class="signature-label">Signature (স্বাক্ষর)</small>
                                <div class="signature-box"></div>
                            </div>
                        </div>
                    `;
                });
            });
        }
    });
    
    // CSV Download with updated teacher distribution
    function downloadCSV() {
        let csv = 'Room,Seat Count,Teachers\n';
        const rows = document.querySelectorAll('#resultTable tbody tr');
        rows.forEach(row => {
            const cols = row.querySelectorAll('td');
            let rowData = [];
            rowData.push('"' + cols[0].innerText.replace(/"/g, '""') + '"');
            rowData.push('"' + cols[1].innerText.replace(/"/g, '""') + '"');
            
            // Get updated teachers from the teacher container
            let teachers = [];
            const teacherItems = cols[2].querySelectorAll('.teacher-item');
            teacherItems.forEach(item => {
                teachers.push(item.dataset.teacher);
            });
            
            rowData.push('"' + teachers.join('; ') + '"');
            csv += rowData.join(',') + '\n';
        });
        
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'distribution_result.csv';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
    window.downloadCSV = downloadCSV;
    </script>

</body>
</html> 