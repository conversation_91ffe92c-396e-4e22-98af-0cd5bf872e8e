<?php
// Simple database structure checker
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

echo '<h2>ডাটাবেস স্ট্রাকচার চেকার</h2>';

// Function to display table structure
function showTableStructure($conn, $tableName) {
    echo "<h3>টেবিল: $tableName</h3>";
    
    // Check if table exists
    $result = $conn->query("SHOW TABLES LIKE '$tableName'");
    if ($result->num_rows == 0) {
        echo "<div style='color:red'>❌ টেবিল এক্সিস্ট করে না!</div>";
        return;
    }
    
    // Get columns
    $result = $conn->query("DESCRIBE $tableName");
    if ($result === false) {
        echo "<div style='color:red'>❌ এরর: " . $conn->error . "</div>";
        return;
    }
    
    echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
    echo "<tr style='background:#f0f0f0;'><th>ফিল্ড</th><th>টাইপ</th><th>NULL</th><th>কি</th><th>ডিফল্ট</th><th>এক্সট্রা</th></tr>";
    
    $found = false;
    while ($row = $result->fetch_assoc()) {
        $found = true;
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>" . ($row['Default'] === NULL ? 'NULL' : $row['Default']) . "</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (!$found) {
        echo "<div style='color:orange'>⚠️ কোন কলাম পাওয়া যায়নি</div>";
    }
    
    // Sample data
    $dataResult = $conn->query("SELECT * FROM $tableName LIMIT 1");
    if ($dataResult && $dataResult->num_rows > 0) {
        echo "<h4>নমুনা ডাটা:</h4>";
        $row = $dataResult->fetch_assoc();
        echo "<ul>";
        foreach ($row as $key => $value) {
            echo "<li><strong>$key:</strong> " . htmlspecialchars($value ?? 'NULL') . "</li>";
        }
        echo "</ul>";
    }
}

// List all tables in database
echo "<h3>সকল টেবিল:</h3>";
$result = $conn->query("SHOW TABLES");
if ($result === false) {
    echo "<div style='color:red'>❌ এরর: " . $conn->error . "</div>";
} else {
    echo "<ul>";
    while ($row = $result->fetch_array()) {
        echo "<li><a href='#" . $row[0] . "'>" . $row[0] . "</a></li>";
    }
    echo "</ul>";
    
    // Reset result pointer
    $result->data_seek(0);
    
    // Show each table structure
    while ($row = $result->fetch_array()) {
        echo "<hr id='" . $row[0] . "'>";
        showTableStructure($conn, $row[0]);
    }
}

// Show table containing 'teacher' in the name
echo "<h3>শিক্ষক সম্পর্কিত টেবিল:</h3>";
$result = $conn->query("SHOW TABLES LIKE '%teacher%'");
if ($result === false) {
    echo "<div style='color:red'>❌ এরর: " . $conn->error . "</div>";
} else {
    if ($result->num_rows == 0) {
        echo "<div style='color:orange'>⚠️ 'teacher' শব্দযুক্ত কোন টেবিল পাওয়া যায়নি</div>";
    } else {
        while ($row = $result->fetch_array()) {
            echo "<div style='color:green'>✓ পাওয়া গেছে: " . $row[0] . "</div>";
        }
    }
}

// Show table containing 'duty' in the name
echo "<h3>ডিউটি সম্পর্কিত টেবিল:</h3>";
$result = $conn->query("SHOW TABLES LIKE '%duty%'");
if ($result === false) {
    echo "<div style='color:red'>❌ এরর: " . $conn->error . "</div>";
} else {
    if ($result->num_rows == 0) {
        echo "<div style='color:orange'>⚠️ 'duty' শব্দযুক্ত কোন টেবিল পাওয়া যায়নি</div>";
    } else {
        while ($row = $result->fetch_array()) {
            echo "<div style='color:green'>✓ পাওয়া গেছে: " . $row[0] . "</div>";
        }
    }
}

// Back links
echo "<div style='margin-top:20px'>";
echo "<p><a href='direct_honorarium_debug.php' style='text-decoration:none;color:#007bff;'>← ডিবাগিং টুল</a></p>";
echo "<p><a href='teacher_honorarium_calculator.php' style='text-decoration:none;color:#007bff;'>← সন্মানী হিসাব পেজ</a></p>";
echo "</div>";
?> 