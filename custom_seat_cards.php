<?php
// Custom Seat Card Generator with Advanced Customization
require_once __DIR__ . '/config/database.php';

// Check if this is a preview request
$isPreview = isset($_GET['preview']) && $_GET['preview'] == '1';

// If not preview, show the customization interface
if (!$isPreview) {
    include 'custom_seat_cards_interface.php';
    exit;
}

// Get customization parameters
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$designTheme = $_GET['theme'] ?? 'default';
$colorScheme = $_GET['color'] ?? 'blue';
$fontSize = $_GET['font_size'] ?? 'medium';
$borderStyle = $_GET['border'] ?? 'solid';
$headerStyle = $_GET['header'] ?? 'gradient';
$logoPosition = $_GET['logo'] ?? 'left';
$customTitle = $_GET['custom_title'] ?? 'HSC Exam-2025';
$customSubtitle = $_GET['custom_subtitle'] ?? 'দামুড়হুদা, কোড. 295';
$showPhoto = isset($_GET['show_photo']);
$showQR = isset($_GET['show_qr']);
$paperSize = $_GET['paper_size'] ?? 'a4';

// Filter parameters
$subjectCode = $_GET['subject'] ?? '';
$searchName = $_GET['search'] ?? '';
$groupName = $_GET['group'] ?? '';
$studentType = $_GET['type'] ?? '';
$rollNumbers = $_GET['rolls'] ?? '';
$gender = $_GET['gender'] ?? '';

// Database connection and student fetching
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Build query
    $sql = "SELECT * FROM students WHERE 1=1";
    $params = [];
    
    // Add filters
    if (!empty($subjectCode)) {
        $sql .= " AND subjects LIKE :subject_pattern";
        $params['subject_pattern'] = '%' . $subjectCode . '%';
    }
    
    if (!empty($searchName)) {
        $sql .= " AND name LIKE :name";
        $params['name'] = '%' . $searchName . '%';
    }
    
    if (!empty($groupName)) {
        $sql .= " AND department LIKE :group";
        $params['group'] = '%' . $groupName . '%';
    }

    if (!empty($studentType)) {
        $sql .= " AND student_type = :type";
        $params['type'] = $studentType;
    }

    if (!empty($gender)) {
        $sql .= " AND gender = :gender";
        $params['gender'] = $gender;
    }

    if (!empty($rollNumbers)) {
        $rollArray = explode(',', $rollNumbers);
        $rollArray = array_map('trim', $rollArray);
        $rollArray = array_filter($rollArray);

        if (!empty($rollArray)) {
            $placeholders = [];
            foreach ($rollArray as $index => $roll) {
                $placeholder = 'roll' . $index;
                $placeholders[] = ':' . $placeholder;
                $params[$placeholder] = $roll;
            }
            $sql .= " AND roll IN (" . implode(',', $placeholders) . ")";
        }
    }

    // Filter incomplete students
    if (isset($_GET['incomplete_only'])) {
        $sql .= " AND (subjects IS NULL OR subjects = '' OR LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1 < 13)";
    }

    // Filter complete students
    if (isset($_GET['complete_only'])) {
        $sql .= " AND subjects IS NOT NULL AND subjects != '' AND LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1 = 13";
    }

    $sql .= " ORDER BY CAST(roll AS UNSIGNED) ASC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $students = [];
    $error = $e->getMessage();
}

$totalStudents = count($students);
$totalPages = ceil($totalStudents / $cardsPerPage);

// Color schemes
$colorSchemes = [
    'blue' => ['primary' => '#4a90e2', 'secondary' => '#357abd', 'bg' => '#f8fbff'],
    'green' => ['primary' => '#27ae60', 'secondary' => '#219a52', 'bg' => '#f8fff8'],
    'purple' => ['primary' => '#8e44ad', 'secondary' => '#7d3c98', 'bg' => '#faf8ff'],
    'red' => ['primary' => '#e74c3c', 'secondary' => '#c0392b', 'bg' => '#fff8f8'],
    'orange' => ['primary' => '#f39c12', 'secondary' => '#e67e22', 'bg' => '#fffaf8'],
    'teal' => ['primary' => '#1abc9c', 'secondary' => '#16a085', 'bg' => '#f8ffff']
];

$colors = $colorSchemes[$colorScheme] ?? $colorSchemes['blue'];

// Font sizes - responsive based on cards per page
$baseFontSizes = [
    'small' => ['title' => 12, 'name' => 14, 'roll' => 24, 'details' => 10],
    'medium' => ['title' => 14, 'name' => 16, 'roll' => 28, 'details' => 11],
    'large' => ['title' => 16, 'name' => 18, 'roll' => 32, 'details' => 12]
];

// Adjust font sizes based on cards per page
$fontMultiplier = 1;
if ($cardsPerPage <= 4) $fontMultiplier = 1.3;
elseif ($cardsPerPage <= 6) $fontMultiplier = 1.1;
elseif ($cardsPerPage <= 9) $fontMultiplier = 1.0;
elseif ($cardsPerPage <= 12) $fontMultiplier = 0.9;
elseif ($cardsPerPage <= 16) $fontMultiplier = 0.8;
else $fontMultiplier = 0.7;

$baseFonts = $baseFontSizes[$fontSize] ?? $baseFontSizes['medium'];
$fonts = [
    'title' => round($baseFonts['title'] * $fontMultiplier) . 'px',
    'name' => round($baseFonts['name'] * $fontMultiplier) . 'px',
    'roll' => round($baseFonts['roll'] * $fontMultiplier) . 'px',
    'details' => round($baseFonts['details'] * $fontMultiplier) . 'px'
];

// Grid layouts based on cards per page
$gridLayouts = [
    4 => ['cols' => 2, 'rows' => 2],
    6 => ['cols' => 3, 'rows' => 2],
    8 => ['cols' => 4, 'rows' => 2],
    9 => ['cols' => 3, 'rows' => 3],
    12 => ['cols' => 3, 'rows' => 4],
    15 => ['cols' => 5, 'rows' => 3],
    16 => ['cols' => 4, 'rows' => 4],
    20 => ['cols' => 5, 'rows' => 4]
];

$layout = $gridLayouts[$cardsPerPage] ?? $gridLayouts[12];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কাস্টম সীট কার্ড - <?php echo htmlspecialchars($customTitle); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
        }
        
        .page {
            width: <?php echo $paperSize == 'a4' ? '210mm' : '216mm'; ?>;
            height: <?php echo $paperSize == 'a4' ? '297mm' : '279mm'; ?>;
            margin: 0 auto;
            padding: 5mm;
            background: white;
            page-break-after: always;
            box-sizing: border-box;
            overflow: hidden;
        }

        .page:last-child {
            page-break-after: avoid;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(<?php echo $layout['cols']; ?>, 1fr);
            grid-template-rows: repeat(<?php echo $layout['rows']; ?>, 1fr);
            gap: <?php
                $gap = $cardsPerPage <= 6 ? 6 : ($cardsPerPage <= 9 ? 5 : ($cardsPerPage <= 12 ? 4 : 3));
                echo $gap . 'mm';
            ?>;
            height: <?php
                // More conservative height calculation
                $pageHeight = $paperSize == 'a4' ? 297 : 279; // mm
                $padding = 10; // 5mm top + 5mm bottom
                $availableHeight = $pageHeight - $padding;
                echo $availableHeight . 'mm';
            ?>;
            width: 100%;
        }
        
        .seat-card {
            border: 2px <?php echo $borderStyle; ?> <?php echo $colors['primary']; ?>;
            border-radius: <?php echo $designTheme == 'modern' ? '12px' : '8px'; ?>;
            padding: <?php echo $cardsPerPage <= 6 ? '8px' : ($cardsPerPage <= 9 ? '6px' : ($cardsPerPage <= 12 ? '5px' : '4px')); ?>;
            background: <?php echo $headerStyle == 'gradient' ? 'linear-gradient(135deg, ' . $colors['bg'] . ' 0%, #ffffff 100%)' : $colors['bg']; ?>;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
            height: auto;
            height: <?php
                // More precise card height calculation
                $pageHeight = $paperSize == 'a4' ? 297 : 279; // mm
                $padding = 10; // 5mm top + 5mm bottom
                $gap = $cardsPerPage <= 6 ? 6 : ($cardsPerPage <= 9 ? 5 : ($cardsPerPage <= 12 ? 4 : 3));
                $totalGapHeight = ($layout['rows'] - 1) * $gap;
                $availableHeight = $pageHeight - $padding - $totalGapHeight;
                $cardHeight = $availableHeight / $layout['rows'];

                // Ensure minimum readable size
                $minHeight = $cardsPerPage <= 6 ? 60 : ($cardsPerPage <= 9 ? 50 : ($cardsPerPage <= 12 ? 40 : 30));
                $finalHeight = max($minHeight, $cardHeight - 2); // 2mm safety margin

                echo round($finalHeight, 1) . 'mm';
            ?>;
            <?php if ($designTheme == 'shadow'): ?>
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            <?php endif; ?>
            overflow: hidden;
        }
        
        .card-header {
            background: <?php echo $headerStyle == 'gradient' ? 'linear-gradient(135deg, ' . $colors['primary'] . ' 0%, ' . $colors['secondary'] . ' 100%)' : $colors['primary']; ?>;
            color: white;
            padding: <?php echo $cardsPerPage <= 9 ? '6px' : ($cardsPerPage <= 12 ? '4px' : '3px'); ?>;
            margin: <?php
                if ($cardsPerPage <= 9) echo '-8px -8px 6px -8px';
                elseif ($cardsPerPage <= 12) echo '-6px -6px 4px -6px';
                else echo '-4px -4px 3px -4px';
            ?>;
            border-radius: <?php echo $designTheme == 'modern' ? '8px 8px 0 0' : '4px 4px 0 0'; ?>;
            <?php if ($designTheme == 'shadow'): ?>
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
            <?php endif; ?>
        }
        
        .exam-title {
            font-size: <?php echo $fonts['title']; ?>;
            font-weight: bold;
            margin-bottom: 2px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .college-info {
            font-size: <?php echo $fonts['details']; ?>;
            opacity: 0.9;
        }
        
        .student-name {
            font-size: <?php echo $fonts['name']; ?>;
            font-weight: 600;
            color: #333;
            margin: <?php echo $cardsPerPage <= 9 ? '6px 0' : ($cardsPerPage <= 12 ? '4px 0' : '2px 0'); ?>;
            line-height: 1.0;
            min-height: <?php echo $cardsPerPage <= 9 ? '30px' : ($cardsPerPage <= 12 ? '25px' : '20px'); ?>;
            display: flex;
            align-items: center;
            justify-content: center;
            <?php if ($designTheme == 'bold'): ?>
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            <?php endif; ?>
        }

        .roll-number {
            font-size: <?php echo $fonts['roll']; ?>;
            font-weight: bold;
            color: <?php echo $colors['primary']; ?>;
            background: rgba(<?php echo hexdec(substr($colors['primary'], 1, 2)) . ',' . hexdec(substr($colors['primary'], 3, 2)) . ',' . hexdec(substr($colors['primary'], 5, 2)); ?>, 0.1);
            padding: <?php echo $cardsPerPage <= 9 ? '6px' : ($cardsPerPage <= 12 ? '4px' : '3px'); ?>;
            border-radius: 4px;
            margin: <?php echo $cardsPerPage <= 9 ? '6px 0' : ($cardsPerPage <= 12 ? '4px 0' : '2px 0'); ?>;
            border: 1px solid <?php echo $colors['primary']; ?>;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        .student-details {
            font-size: <?php echo $fonts['details']; ?>;
            color: #666;
            line-height: 1.1;
        }

        .student-details div {
            margin: <?php echo $cardsPerPage <= 9 ? '2px 0' : ($cardsPerPage <= 12 ? '1px 0' : '0.5px 0'); ?>;
        }

        /* Force fit to page */
        .force-fit {
            max-height: 100vh;
            overflow: hidden;
        }

        /* Ensure no overflow */
        * {
            box-sizing: border-box;
        }

        .seat-card * {
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        @media print {
            * {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            @page {
                size: A4;
                margin: 3mm;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
            }

            .page {
                margin: 0 !important;
                padding: 3mm !important;
                box-shadow: none !important;
                page-break-inside: avoid;
                height: 291mm !important; /* 297mm - 6mm margin */
                width: 204mm !important; /* 210mm - 6mm margin */
                overflow: hidden;
                display: block;
            }

            .cards-container {
                height: 285mm !important; /* 291mm - 6mm padding */
                page-break-inside: avoid;
                overflow: hidden;
                display: grid !important;
            }

            .seat-card {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                overflow: hidden !important;
                height: <?php
                    $printPageHeight = 285; // Available height in print
                    $gap = $cardsPerPage <= 6 ? 6 : ($cardsPerPage <= 9 ? 5 : ($cardsPerPage <= 12 ? 4 : 3));
                    $totalGapHeight = ($layout['rows'] - 1) * $gap;
                    $availableHeight = $printPageHeight - $totalGapHeight;
                    $cardHeight = $availableHeight / $layout['rows'];
                    echo round($cardHeight - 1, 1) . 'mm'; // 1mm safety margin
                ?> !important;
            }
        }
        
        .logo-container {
            position: absolute;
            top: 5px;
            left: <?php echo $logoPosition == 'left' ? '5px' : 'auto'; ?>;
            right: <?php echo $logoPosition == 'right' ? '5px' : 'auto'; ?>;
            width: 30px;
            height: 30px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: <?php echo $colors['primary']; ?>;
        }

        .qr-code {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 25px;
            height: 25px;
            background: white;
            border: 1px solid #ddd;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body>
    <?php if ($totalStudents > 0): ?>
        <?php for ($page = 0; $page < $totalPages; $page++): ?>
            <div class="page force-fit">
                <div class="cards-container">
                    <?php 
                    $startIndex = $page * $cardsPerPage;
                    $endIndex = min($startIndex + $cardsPerPage, $totalStudents);
                    
                    for ($i = $startIndex; $i < $endIndex; $i++): 
                        $s = $students[$i];
                    ?>
                        <div class="seat-card">
                            <?php if ($showPhoto): ?>
                                <div class="logo-container">
                                    📷
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-header">
                                <div class="exam-title"><?php echo htmlspecialchars($customTitle); ?></div>
                                <div class="college-info"><?php echo htmlspecialchars($customSubtitle); ?></div>
                            </div>
                            
                            <div class="student-name">
                                <?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? ''); ?>
                            </div>

                            <div class="roll-number">
                                <?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? ''); ?>
                            </div>

                            <div class="student-details">
                                <div><strong>রেজিঃ</strong> <?php echo htmlspecialchars($s['registration'] ?? $s['reg_no'] ?? ''); ?></div>
                                <div><strong>বিভাগ:</strong> <?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? ''); ?></div>
                                <div><strong>শিক্ষাবর্ষ:</strong> <?php echo htmlspecialchars($s['academic_year'] ?? $s['session'] ?? ''); ?></div>
                            </div>
                            
                            <?php if ($showQR): ?>
                                <div class="qr-code">
                                    QR
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                    
                    <?php 
                    // Fill remaining slots with empty cards if needed
                    $remainingSlots = $cardsPerPage - ($endIndex - $startIndex);
                    for ($j = 0; $j < $remainingSlots; $j++): 
                    ?>
                        <div class="seat-card" style="border: 1px dashed #ccc; background: #f9f9f9; opacity: 0.5;">
                            <div style="color: #ccc; font-size: 14px; margin: auto;">খালি</div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        <?php endfor; ?>
    <?php else: ?>
        <div class="page">
            <div style="text-align: center; margin-top: 100px; font-size: 18px; color: #666;">
                কোন শিক্ষার্থী পাওয়া যায়নি
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Adjust layout to fit page
        function adjustLayout() {
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => {
                const container = page.querySelector('.cards-container');
                const cards = container.querySelectorAll('.seat-card');

                // Check if content overflows
                if (page.scrollHeight > page.clientHeight) {
                    // Reduce font sizes slightly
                    cards.forEach(card => {
                        const elements = card.querySelectorAll('*');
                        elements.forEach(el => {
                            const currentSize = parseFloat(window.getComputedStyle(el).fontSize);
                            if (currentSize > 8) {
                                el.style.fontSize = (currentSize * 0.95) + 'px';
                            }
                        });

                        // Reduce padding
                        const currentPadding = parseFloat(window.getComputedStyle(card).padding);
                        if (currentPadding > 2) {
                            card.style.padding = Math.max(2, currentPadding - 1) + 'px';
                        }
                    });
                }
            });
        }

        // Auto print when page loads
        window.onload = function() {
            adjustLayout();
            setTimeout(() => {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
