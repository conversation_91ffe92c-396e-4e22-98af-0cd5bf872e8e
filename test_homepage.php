<?php
/**
 * Test script for the new homepage
 * This script tests the functionality of the homepage and exam data helper
 */

echo "=== EXMM Homepage Test Script ===\n\n";

// Test 1: Database Connection
echo "1. Testing Database Connection...\n";
try {
    require_once 'config/database.php';
    $db = new Database();
    $pdo = $db->getConnection();
    echo "✓ Database connection successful\n\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Teacher Manager
echo "2. Testing Teacher Manager...\n";
try {
    require_once 'includes/teacher_db.php';
    $teacherManager = new TeacherManager($pdo);
    echo "✓ Teacher Manager initialized successfully\n\n";
} catch (Exception $e) {
    echo "✗ Teacher Manager initialization failed: " . $e->getMessage() . "\n\n";
}

// Test 3: Exam Data Helper
echo "3. Testing Exam Data Helper...\n";
try {
    require_once 'includes/exam_data_helper.php';
    $examHelper = new ExamDataHelper($pdo);
    echo "✓ Exam Data Helper initialized successfully\n\n";
} catch (Exception $e) {
    echo "✗ Exam Data Helper initialization failed: " . $e->getMessage() . "\n\n";
}

// Test 4: Get Exam Overview
echo "4. Testing Exam Overview Data...\n";
try {
    $examData = $examHelper->getExamOverview();
    echo "✓ Exam overview data retrieved successfully\n";
    echo "   - Total Students: " . $examData['total_students'] . "\n";
    echo "   - Total Teachers: " . $examData['total_teachers'] . "\n";
    echo "   - Upcoming Exams: " . count($examData['upcoming_exams']) . "\n";
    echo "   - Today's Exams: " . count($examData['today_exams']) . "\n";
    echo "   - Room Assignments: " . count($examData['room_assignments']) . "\n\n";
} catch (Exception $e) {
    echo "✗ Failed to get exam overview: " . $e->getMessage() . "\n\n";
}

// Test 5: Check Tables
echo "5. Checking Database Tables...\n";
$tables = ['students', 'teachers', 'duty_assignments', 'duty_dates'];
foreach ($tables as $table) {
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch()['count'];
        echo "✓ Table '$table': $count records\n";
    } catch (Exception $e) {
        echo "✗ Table '$table': Error - " . $e->getMessage() . "\n";
    }
}
echo "\n";

// Test 6: Test Homepage Loading
echo "6. Testing Homepage Loading...\n";
try {
    ob_start();
    include 'index.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'EXMM পরীক্ষা ব্যবস্থাপনা') !== false) {
        echo "✓ Homepage loads successfully with Bengali content\n";
    } else {
        echo "✗ Homepage content issue - Bengali title not found\n";
    }
    
    if (strpos($output, 'exam-info-card') !== false) {
        echo "✓ Exam info cards are present\n";
    } else {
        echo "✗ Exam info cards not found\n";
    }
    
    if (strpos($output, 'feature-card') !== false) {
        echo "✓ Feature cards are present\n";
    } else {
        echo "✗ Feature cards not found\n";
    }
    
} catch (Exception $e) {
    echo "✗ Homepage loading failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 7: Test Public Exam Info Page
echo "7. Testing Public Exam Info Page...\n";
try {
    ob_start();
    include 'public_exam_info.php';
    $output = ob_get_clean();
    
    if (strpos($output, 'পরীক্ষার বিস্তারিত তথ্য') !== false) {
        echo "✓ Public exam info page loads successfully\n";
    } else {
        echo "✗ Public exam info page content issue\n";
    }
    
} catch (Exception $e) {
    echo "✗ Public exam info page loading failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 8: Test Responsive CSS
echo "8. Testing CSS and Responsive Design...\n";
$cssFiles = ['index.php', 'public_exam_info.php'];
foreach ($cssFiles as $file) {
    $content = file_get_contents($file);
    if (strpos($content, '@media (max-width: 768px)') !== false) {
        echo "✓ File '$file' has mobile responsive CSS\n";
    } else {
        echo "✗ File '$file' missing mobile responsive CSS\n";
    }
    
    if (strpos($content, 'Noto Sans Bengali') !== false) {
        echo "✓ File '$file' has Bengali font support\n";
    } else {
        echo "✗ File '$file' missing Bengali font support\n";
    }
}
echo "\n";

// Test 9: Check File Permissions and Structure
echo "9. Checking File Structure...\n";
$requiredFiles = [
    'index.php',
    'public_exam_info.php',
    'includes/exam_data_helper.php',
    'config/database.php',
    'includes/teacher_db.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "✓ File '$file' exists\n";
    } else {
        echo "✗ File '$file' missing\n";
    }
}
echo "\n";

echo "=== Test Summary ===\n";
echo "Homepage testing completed. Please check the browser to verify visual appearance.\n";
echo "If all tests passed, the homepage should be working correctly.\n\n";

echo "Next Steps:\n";
echo "1. Open http://localhost:8000 in your browser\n";
echo "2. Test the 'বিস্তারিত পরীক্ষার তথ্য' link\n";
echo "3. Try searching for students in the public exam info page\n";
echo "4. Test responsive design by resizing browser window\n";
echo "5. Check all navigation links work properly\n\n";

echo "=== End of Test ===\n";
?>
