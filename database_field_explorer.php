<?php
// Database Field Explorer
// This tool explores all tables and field relationships to help build proper queries
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

function getTableInfo($conn) {
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    
    if ($result) {
        while ($row = $result->fetch_array()) {
            $tableName = $row[0];
            $tables[$tableName] = [
                'fields' => [],
                'sample' => []
            ];
            
            // Get columns
            $fields_result = $conn->query("DESCRIBE $tableName");
            if ($fields_result) {
                while ($field = $fields_result->fetch_assoc()) {
                    $tables[$tableName]['fields'][$field['Field']] = [
                        'type' => $field['Type'],
                        'key' => $field['Key'],
                        'default' => $field['Default'],
                        'extra' => $field['Extra']
                    ];
                }
            }
            
            // Get sample data
            $sample_result = $conn->query("SELECT * FROM $tableName LIMIT 1");
            if ($sample_result && $sample_result->num_rows > 0) {
                $tables[$tableName]['sample'] = $sample_result->fetch_assoc();
            }
        }
    }
    
    return $tables;
}

// Get all tables and columns
$tables_info = getTableInfo($conn);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাটাবেস ফিল্ড এক্সপ্লোরার</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card-header {
            background: #eaf4ff;
            border-radius: 10px 10px 0 0;
            border-bottom: 2px solid #007bff;
        }
        .field-name {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 2px 5px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .relationship-finder {
            background-color: #fff8dc;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .badge-key {
            font-size: 11px;
            vertical-align: middle;
        }
        .sample-value {
            max-width: 200px;
            overflow-x: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">
            <i class="fas fa-database me-2"></i>
            ডাটাবেস ফিল্ড এক্সপ্লোরার
        </h1>
        
        <div class="alert alert-info">
            <p><strong>নির্দেশনাঃ</strong> এই পেজটি আপনাকে ডাটাবেসের সম্পূর্ণ স্ট্রাকচার দেখাবে। <code>teacher_id</code> এবং সম্পর্কিত ফিল্ডের নাম দেখুন।</p>
        </div>
        
        <!-- Table relationship finder -->
        <div class="relationship-finder">
            <h3><i class="fas fa-project-diagram me-2"></i>টেবিল সম্পর্ক বিশ্লেষণ</h3>
            <div class="row">
                <?php
                // Find teacher-related tables
                $teacher_tables = [];
                $duty_tables = [];
                foreach ($tables_info as $table_name => $table_data) {
                    if (stripos($table_name, 'teacher') !== false) {
                        $teacher_tables[] = $table_name;
                    }
                    if (stripos($table_name, 'duty') !== false || stripos($table_name, 'assign') !== false) {
                        $duty_tables[] = $table_name;
                    }
                }
                
                if (!empty($teacher_tables)) {
                    echo '<div class="col-md-6">';
                    echo '<h4>শিক্ষক তথ্য টেবিল:</h4>';
                    echo '<ul>';
                    foreach ($teacher_tables as $table) {
                        echo "<li><strong>$table</strong>";
                        
                        // Find ID fields
                        $id_fields = [];
                        foreach ($tables_info[$table]['fields'] as $field => $attr) {
                            if ($attr['key'] == 'PRI' || stripos($field, 'id') !== false) {
                                $id_fields[] = $field;
                            }
                        }
                        
                        if (!empty($id_fields)) {
                            echo " - আইডি ফিল্ড: ";
                            foreach ($id_fields as $id) {
                                echo "<code>$id</code> ";
                            }
                        }
                        
                        echo "</li>";
                    }
                    echo '</ul>';
                    echo '</div>';
                }
                
                if (!empty($duty_tables)) {
                    echo '<div class="col-md-6">';
                    echo '<h4>ডিউটি তথ্য টেবিল:</h4>';
                    echo '<ul>';
                    foreach ($duty_tables as $table) {
                        echo "<li><strong>$table</strong>";
                        
                        // Find foreign key fields
                        $fk_fields = [];
                        foreach ($tables_info[$table]['fields'] as $field => $attr) {
                            if (stripos($field, 'teacher') !== false || stripos($field, 'id') !== false) {
                                $fk_fields[] = $field;
                            }
                        }
                        
                        if (!empty($fk_fields)) {
                            echo " - সম্ভাব্য ফরেন কি: ";
                            foreach ($fk_fields as $fk) {
                                echo "<code>$fk</code> ";
                            }
                        }
                        
                        echo "</li>";
                    }
                    echo '</ul>';
                    echo '</div>';
                }
                ?>
            </div>
            
            <h4 class="mt-3">সম্ভাব্য জয়েন স্টেটমেন্ট:</h4>
            <?php
            if (!empty($teacher_tables) && !empty($duty_tables)) {
                foreach ($teacher_tables as $teacher_table) {
                    foreach ($duty_tables as $duty_table) {
                        // Find possible join fields
                        $teacher_id_fields = [];
                        foreach ($tables_info[$teacher_table]['fields'] as $field => $attr) {
                            if ($attr['key'] == 'PRI' || stripos($field, 'id') !== false) {
                                $teacher_id_fields[] = $field;
                            }
                        }
                        
                        $duty_fk_fields = [];
                        foreach ($tables_info[$duty_table]['fields'] as $field => $attr) {
                            if (stripos($field, 'teacher') !== false || stripos($field, 'id') !== false) {
                                $duty_fk_fields[] = $field;
                            }
                        }
                        
                        if (!empty($teacher_id_fields) && !empty($duty_fk_fields)) {
                            echo "<div class='bg-light p-2 mb-2'>";
                            echo "<code>SELECT * FROM $duty_table d JOIN $teacher_table t ON ";
                            
                            $joins = [];
                            foreach ($teacher_id_fields as $t_id) {
                                foreach ($duty_fk_fields as $d_fk) {
                                    $joins[] = "d.$d_fk = t.$t_id";
                                }
                            }
                            
                            echo implode(" OR ", $joins);
                            echo "</code>";
                            echo "</div>";
                        }
                    }
                }
            }
            ?>
        </div>
        
        <!-- Generate adaptive SQL -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>এডাপ্টিভ SQL জেনারেটর</h3>
            </div>
            <div class="card-body">
                <form method="post" action="test_adaptive_query.php">
                    <?php
                    // Pre-select teacher table
                    echo '<div class="row mb-3">';
                    echo '<div class="col-md-6">';
                    echo '<label for="teacher_table">শিক্ষক টেবিল:</label>';
                    echo '<select name="teacher_table" id="teacher_table" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        $selected = (stripos($table_name, 'teacher') !== false) ? 'selected' : '';
                        echo "<option value='$table_name' $selected>$table_name</option>";
                    }
                    echo '</select>';
                    echo '</div>';
                    
                    // Pre-select duty table
                    echo '<div class="col-md-6">';
                    echo '<label for="duty_table">ডিউটি টেবিল:</label>';
                    echo '<select name="duty_table" id="duty_table" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        $selected = (stripos($table_name, 'duty') !== false) ? 'selected' : '';
                        echo "<option value='$table_name' $selected>$table_name</option>";
                    }
                    echo '</select>';
                    echo '</div>';
                    echo '</div>';
                    
                    echo '<div class="row">';
                    // Select teacher ID field
                    echo '<div class="col-md-3">';
                    echo '<label for="teacher_id_field">শিক্ষক আইডি ফিল্ড:</label>';
                    echo '<select name="teacher_id_field" id="teacher_id_field" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        if (stripos($table_name, 'teacher') !== false) {
                            foreach ($table_data['fields'] as $field_name => $field_data) {
                                $selected = ($field_data['key'] == 'PRI') ? 'selected' : '';
                                echo "<option value='$field_name' $selected>$field_name</option>";
                            }
                            break;
                        }
                    }
                    echo '</select>';
                    echo '</div>';
                    
                    // Select duty foreign key field
                    echo '<div class="col-md-3">';
                    echo '<label for="duty_fk_field">ডিউটি টেবিল আইডি:</label>';
                    echo '<select name="duty_fk_field" id="duty_fk_field" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        if (stripos($table_name, 'duty') !== false || stripos($table_name, 'assign') !== false) {
                            foreach ($table_data['fields'] as $field_name => $field_data) {
                                $selected = (stripos($field_name, 'teacher') !== false && stripos($field_name, 'id') !== false) ? 'selected' : '';
                                echo "<option value='$field_name' $selected>$field_name</option>";
                            }
                            break;
                        }
                    }
                    echo '</select>';
                    echo '</div>';
                    
                    // Select teacher name field
                    echo '<div class="col-md-3">';
                    echo '<label for="teacher_name_field">শিক্ষকের নাম ফিল্ড:</label>';
                    echo '<select name="teacher_name_field" id="teacher_name_field" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        if (stripos($table_name, 'teacher') !== false) {
                            foreach ($table_data['fields'] as $field_name => $field_data) {
                                $selected = (stripos($field_name, 'name') !== false) ? 'selected' : '';
                                echo "<option value='$field_name' $selected>$field_name</option>";
                            }
                            break;
                        }
                    }
                    echo '</select>';
                    echo '</div>';
                    
                    // Select duty date field
                    echo '<div class="col-md-3">';
                    echo '<label for="duty_date_field">ডিউটি তারিখ ফিল্ড:</label>';
                    echo '<select name="duty_date_field" id="duty_date_field" class="form-select">';
                    foreach ($tables_info as $table_name => $table_data) {
                        if (stripos($table_name, 'duty') !== false || stripos($table_name, 'assign') !== false) {
                            foreach ($table_data['fields'] as $field_name => $field_data) {
                                $selected = (stripos($field_name, 'date') !== false) ? 'selected' : '';
                                echo "<option value='$field_name' $selected>$field_name</option>";
                            }
                            break;
                        }
                    }
                    echo '</select>';
                    echo '</div>';
                    echo '</div>';
                    ?>
                    <div class="mt-3">
                        <input type="submit" class="btn btn-primary" value="এডাপ্টিভ কোয়ারী টেস্ট করুন">
                    </div>
                </form>
            </div>
        </div>
        
        <!-- All tables and their fields -->
        <h2 class="mt-4 mb-3">সমস্ত টেবিল এবং ফিল্ড</h2>
        <div class="row">
            <?php foreach ($tables_info as $table_name => $table_data): ?>
            <div class="col-lg-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h3 class="mb-0"><?php echo $table_name; ?></h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ফিল্ড নাম</th>
                                    <th>টাইপ</th>
                                    <th>কি</th>
                                    <th>নমুনা তথ্য</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($table_data['fields'] as $field_name => $field_info): ?>
                                <tr>
                                    <td>
                                        <span class="field-name"><?php echo $field_name; ?></span>
                                        <?php if ($field_info['key'] == 'PRI'): ?>
                                            <span class="badge bg-primary badge-key">PK</span>
                                        <?php elseif ($field_info['key'] == 'MUL'): ?>
                                            <span class="badge bg-info badge-key">FK</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $field_info['type']; ?></td>
                                    <td><?php echo $field_info['key'] ?: '-'; ?></td>
                                    <td>
                                        <?php 
                                        if (isset($table_data['sample'][$field_name])) {
                                            echo '<span class="sample-value">' . 
                                                htmlspecialchars($table_data['sample'][$field_name]) . 
                                                '</span>';
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-5 mb-3">
            <a href="teacher_honorarium_fix.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-calculator me-2"></i>
                সন্মানী হিসাব পেজে ফিরে যান
            </a>
            <a href="db_structure_check.php" class="btn btn-outline-secondary">
                <i class="fas fa-database me-2"></i>
                ডাটাবেস স্ট্রাকচার চেকারে ফিরুন
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>