<?php
// Demo data for testing print layout
$date = '2024-12-15';

// Demo teachers data
$demoTeachers = [
    0 => [
        'name' => 'মোঃ নজরুল ইসলাম',
        'mobile' => '01711111111',
        'subject' => 'গণিত',
        'designation' => 'সহকারী অধ্যাপক',
        'college' => 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'
    ],
    1 => [
        'name' => 'মোছাঃ রোকেয়া খাতুন',
        'mobile' => '01722222222',
        'subject' => 'বাংলা',
        'designation' => 'প্রভাষক',
        'college' => 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'
    ],
    2 => [
        'name' => 'মোঃ আব্দুর রহমান',
        'mobile' => '01733333333',
        'subject' => 'ইংরেজি',
        'designation' => 'সহকারী অধ্যাপক',
        'college' => 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'
    ],
    3 => [
        'name' => 'মোছাঃ ফাতেমা বেগম',
        'mobile' => '01744444444',
        'subject' => 'পদার্থবিজ্ঞান',
        'designation' => 'প্রভাষক',
        'college' => 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'
    ],
    4 => [
        'name' => 'মোঃ করিম উদ্দিন',
        'mobile' => '01755555555',
        'subject' => 'রসায়ন',
        'designation' => 'সহকারী অধ্যাপক',
        'college' => 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'
    ]
];

// Demo rooms
$demoRooms = [
    'পরীক্ষা হল - ১',
    'পরীক্ষা হল - ২',
    'পরীক্ষা হল - ৩',
    'কম্পিউটার ল্যাব',
    'লাইব্রেরি হল'
];

// Demo room assignments
$demoAssignments = [
    0 => [0, 1], // Room 0 has teachers 0 and 1
    1 => [2],    // Room 1 has teacher 2
    2 => [3, 4], // Room 2 has teachers 3 and 4
    3 => [],     // Room 3 is empty
    4 => []      // Room 4 is empty
];

// Bengali month names
$bengaliMonths = [
    1 => 'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
    'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
];

function formatDateBengali($dateString) {
    global $bengaliMonths;
    $date = new DateTime($dateString);
    $day = $date->format('d');
    $month = $bengaliMonths[(int)$date->format('m')];
    $year = $date->format('Y');
    return "$day $month $year";
}

// Calculate total assigned teachers
$totalAssigned = 0;
foreach ($demoAssignments as $roomAssignment) {
    $totalAssigned += count($roomAssignment);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম ওয়াইজ ডিউটি তালিকা - <?php echo formatDateBengali($date); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            background: white;
        }
        
        .header { 
            text-align: center; 
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .college-info {
            border: 3px solid #000;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .govt-text {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #000;
        }
        
        .header h1 {
            font-size: 24px;
            margin: 5px 0;
            font-weight: bold;
            color: #000;
            text-decoration: underline;
        }
        
        .header p {
            font-size: 16px;
            margin: 5px 0;
            color: #000;
            font-weight: 600;
        }
        
        .exam-info {
            margin-top: 15px;
            border-top: 2px solid #000;
            padding-top: 15px;
        }
        
        .exam-title {
            font-size: 18px;
            font-weight: bold;
            color: #000;
            margin-bottom: 10px;
        }
        
        .exam-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .exam-date, .exam-time, .print-date {
            color: #000;
        }
        
        .duty-header {
            font-size: 16px;
            font-weight: bold;
            color: #000;
            text-decoration: underline;
        }
        
        .duty-table-container {
            margin: 30px 0;
        }
        
        .duty-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .duty-table th,
        .duty-table td {
            border: 1px solid #000;
            padding: 10px 8px;
            vertical-align: top;
        }
        
        .duty-table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            font-size: 13px;
            height: 50px;
        }
        
        .serial-col { width: 8%; }
        .teacher-name-col { width: 30%; }
        .subject-col { width: 12%; }
        .designation-col { width: 15%; }
        .time-col { width: 15%; }
        .signature-col { width: 10%; }
        .remarks-col { width: 10%; }
        
        .serial-number {
            text-align: center;
            font-weight: bold;
            font-size: 14px;
            vertical-align: middle;
        }
        
        .teacher-info {
            padding: 8px;
        }
        
        .teacher-name {
            font-weight: bold;
            font-size: 13px;
            color: #000;
            margin-bottom: 3px;
        }
        
        .room-name {
            font-size: 12px;
            color: #000;
            margin-bottom: 3px;
        }
        
        .mobile-info {
            font-size: 10px;
            color: #666;
            line-height: 1.2;
        }
        
        .subject-info,
        .designation-info {
            text-align: center;
            font-size: 12px;
            font-weight: bold;
            vertical-align: middle;
        }
        
        .time-cell {
            text-align: center;
            font-size: 11px;
            vertical-align: middle;
            height: 60px;
            background: #f9f9f9;
            border: 1px solid #000;
        }
        
        .signature-cell {
            height: 60px;
            background: #f9f9f9;
        }
        
        .remarks-cell {
            font-size: 10px;
            color: #666;
            text-align: center;
            vertical-align: middle;
        }
        
        .footer {
            margin-top: 60px;
            display: flex;
            justify-content: space-between;
            page-break-inside: avoid;
        }
        
        .signature {
            text-align: center;
            width: 250px;
        }
        
        .signature-line {
            border-top: 2px solid #000;
            margin-top: 60px;
            padding-top: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        
        @media print { 
            @page {
                size: A4;
                margin: 15mm;
            }
            
            body { 
                margin: 0; 
                padding: 0;
                font-size: 12px;
            }
            
            .no-print { 
                display: none !important; 
            }
            
            .header {
                margin-bottom: 20px;
            }
            
            .college-info {
                border: 2px solid #000 !important;
                padding: 15px;
            }
            
            .duty-table {
                page-break-inside: avoid;
                font-size: 11px;
            }
            
            .duty-table th,
            .duty-table td {
                border: 1px solid #000 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                padding: 6px 4px;
            }
            
            .duty-table th {
                background-color: #f0f0f0 !important;
                font-size: 11px;
                height: 40px;
            }
            
            .teacher-name {
                font-size: 11px;
            }
            
            .room-name {
                font-size: 10px;
            }
            
            .mobile-info {
                font-size: 9px;
            }
            
            .subject-info,
            .designation-info {
                font-size: 10px;
            }
            
            .time-cell {
                height: 50px;
                background: #f9f9f9 !important;
            }
            
            .signature-cell {
                height: 50px;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="header">
        <div class="college-info">
            <div class="govt-text">গণপ্রজাতন্ত্রী বাংলাদেশ সরকার</div>
            <h1>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h1>
            <p>দামুড়হুদা, চুয়াডাঙ্গা</p>
            <div class="exam-info">
                <div class="exam-title">উচ্চ মাধ্যমিক পরীক্ষা-২০২৫</div>
                <div class="exam-details">
                    <span class="exam-date">বিষয়ঃ বাংলা ১ম পত্র</span>
                    <span class="exam-time">সময়ঃ সকাল ১০টা</span>
                    <span class="print-date">তারিখঃ <?php echo formatDateBengali($date); ?></span>
                </div>
                <div class="duty-header">রুম প্রত্যেকক্ষেত্রের ব্যাহিরা পত্র</div>
            </div>
        </div>
    </div>

    <div class="duty-table-container">
        <table class="duty-table">
            <thead>
                <tr>
                    <th class="serial-col">ক্রম নং</th>
                    <th class="teacher-name-col">রুম প্রত্যেকক্ষেত্রের নাম</th>
                    <th class="subject-col">বিষয়</th>
                    <th class="designation-col">পদবী</th>
                    <th class="time-col">উপস্থিতির সময়</th>
                    <th class="signature-col">স্বাক্ষর</th>
                    <th class="remarks-col">মন্তব্য</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $serialNumber = 100; // Starting from 100 like the sample
                foreach ($demoRooms as $roomIndex => $roomName):
                    $roomTeachers = $demoAssignments[$roomIndex] ?? [];
                    if (!empty($roomTeachers)):
                        foreach ($roomTeachers as $teacherIndex):
                            if (isset($demoTeachers[$teacherIndex])):
                                $teacher = $demoTeachers[$teacherIndex];
                ?>
                    <tr>
                        <td class="serial-number"><?php echo $serialNumber; ?></td>
                        <td class="teacher-info">
                            <div class="teacher-name"><?php echo htmlspecialchars($teacher['name']); ?></div>
                            <div class="room-name"><?php echo htmlspecialchars($roomName); ?></div>
                            <div class="mobile-info">মোবাইলঃ <?php echo htmlspecialchars($teacher['mobile']); ?> কলেজঃ <?php echo htmlspecialchars($teacher['college']); ?></div>
                        </td>
                        <td class="subject-info"><?php echo htmlspecialchars($teacher['subject']); ?></td>
                        <td class="designation-info"><?php echo htmlspecialchars($teacher['designation']); ?></td>
                        <td class="time-cell"></td>
                        <td class="signature-cell"></td>
                        <td class="remarks-cell"></td>
                    </tr>
                <?php
                                $serialNumber++;
                            endif;
                        endforeach;
                    else:
                        // Show empty room entry
                ?>
                    <tr>
                        <td class="serial-number"><?php echo $serialNumber; ?></td>
                        <td class="teacher-info">
                            <div class="teacher-name">-</div>
                            <div class="room-name"><?php echo htmlspecialchars($roomName); ?></div>
                            <div class="mobile-info">মোবাইলঃ - কলেজঃ -</div>
                        </td>
                        <td class="subject-info">-</td>
                        <td class="designation-info">-</td>
                        <td class="time-cell"></td>
                        <td class="signature-cell"></td>
                        <td class="remarks-cell">কোনো শিক্ষক নেই</td>
                    </tr>
                <?php
                        $serialNumber++;
                    endif;
                endforeach;
                ?>
            </tbody>
        </table>
    </div>

    <div class="footer">
        <div class="signature">
            <div class="signature-line">অধ্যক্ষের স্বাক্ষর</div>
        </div>
        <div class="signature">
            <div class="signature-line">পরীক্ষা নিয়ন্ত্রকের স্বাক্ষর</div>
        </div>
    </div>
</body>
</html>
