<?php
require_once 'includes/teacher_db.php';

// Function to get college logo
function getCollegeLogo() {
    $logoExtensions = ['png', 'jpg', 'jpeg', 'gif'];
    foreach ($logoExtensions as $ext) {
        $logoPath = "uploads/college_logo.$ext";
        if (file_exists($logoPath)) {
            return $logoPath;
        }
    }
    return null;
}

// Get parameters
$date = $_GET['date'] ?? '';
$teacherId = $_GET['teacher_id'] ?? '';
$dates = $_GET['dates'] ?? ''; // Support for multiple dates

// Validate parameters
if (empty($teacherId)) {
    die('শিক্ষক ID প্রয়োজন!');
}

// Handle multiple dates
$dutyDates = [];
if (!empty($dates)) {
    $dutyDates = explode(',', $dates);
} elseif (!empty($date)) {
    $dutyDates = [$date];
} else {
    die('তারিখ প্রয়োজন!');
}

// Get teacher information
$teacher = $teacherManager->getTeacherById($teacherId);
if (!$teacher) {
    die('শিক্ষক পাওয়া যায়নি!');
}

// Get duty assignments for this teacher
$teacherAssignments = [];
$roomNumbers = [];
foreach ($dutyDates as $dutyDate) {
    $assignments = $teacherManager->getDutyAssignments(trim($dutyDate));
    foreach ($assignments as $assignment) {
        if ($assignment['teacher_id'] == $teacherId) {
            $teacherAssignments[] = $assignment;
            if (!empty($assignment['room_number'])) {
                $roomNumbers[] = $assignment['room_number'];
            }
            break;
        }
    }
}

if (empty($teacherAssignments)) {
    die('এই তারিখগুলোতে এই শিক্ষকের কোন ডিউটি পাওয়া যায়নি!');
}

// Get unique room numbers
$roomNumbers = array_unique($roomNumbers);

// Get signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Generate memo number
$memoNumber = sprintf("০১/%s-%02d", date('Y'), date('m', strtotime($date)));
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি পত্র - <?php echo htmlspecialchars($teacher['name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        body {
            font-family: 'Hind Siliguri', sans-serif;
            line-height: 1.4;
            color: #000;
            background: white;
            margin: 0;
            padding: 10px;
            font-size: 14px;
        }

        .letter-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            padding: 15px;
            border: 2px solid #000;
            min-height: 95vh;
            max-height: 95vh;
            overflow: hidden;
        }
        
        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            position: relative;
        }

        .header-logo {
            position: absolute;
            left: 20px;
            top: 10px;
            width: 80px;
            height: 80px;
            object-fit: contain;
        }

        .logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 8px;
        }

        .college-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #000;
        }

        .college-address {
            font-size: 14px;
            margin-bottom: 3px;
            color: #333;
        }

        .college-info {
            font-size: 12px;
            color: #666;
        }
        
        .memo-section {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }

        .memo-left {
            text-align: left;
        }

        .memo-right {
            text-align: right;
        }

        .memo-number {
            font-weight: bold;
            font-size: 14px;
        }

        .subject-line {
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .letter-body {
            margin: 15px 0;
            text-align: justify;
            font-size: 14px;
            line-height: 1.5;
        }

        .teacher-info-box {
            background: #f8f9fa;
            border: 2px solid #000;
            border-radius: 3px;
            padding: 8px;
            margin: 10px 0;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
        }

        .info-table td {
            padding: 6px 10px;
            border: 1px solid #000;
            font-size: 13px;
            text-align: center;
        }

        .info-table .label {
            background: #e9ecef;
            font-weight: bold;
            width: 16.66%;
        }

        .duty-dates-box {
            background: #fff3cd;
            border: 2px solid #000;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
        }

        .duty-dates-title {
            font-weight: bold;
            font-size: 15px;
            color: #856404;
            margin-bottom: 8px;
            text-align: center;
        }

        .duty-dates-text {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            line-height: 1.6;
            padding: 10px;
            background: #fff;
            border: 1px solid #856404;
            border-radius: 3px;
        }
        
        .duty-details {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .duty-title {
            font-weight: bold;
            font-size: 18px;
            color: #856404;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .duty-list {
            list-style: none;
            padding: 0;
        }
        
        .duty-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ffeaa7;
            font-size: 16px;
        }
        
        .duty-list li:last-child {
            border-bottom: none;
        }
        
        .duty-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .signatures {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
            page-break-inside: avoid;
        }

        .signature-box {
            text-align: center;
            width: 200px;
            position: relative;
        }

        .signature-image {
            max-width: 120px;
            max-height: 50px;
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            background: white;
            padding: 0 10px;
        }

        .signature-line {
            border-top: 2px solid #000;
            margin-top: 50px;
            padding-top: 8px;
            font-weight: bold;
            font-size: 14px;
            position: relative;
            z-index: 1;
        }

        .signature-name {
            font-size: 13px;
            font-weight: bold;
            margin-top: 5px;
            color: #333;
        }

        .signature-details {
            font-size: 12px;
            margin-top: 3px;
            line-height: 1.3;
        }




        
        @media print {
            body {
                margin: 0;
                padding: 0;
                font-size: 13px;
            }

            .no-print {
                display: none !important;
            }

            .letter-container {
                border: 2px solid #000;
                box-shadow: none;
                padding: 10px;
                margin: 0;
                min-height: auto;
                max-height: none;
                page-break-inside: avoid;
            }

            .signatures {
                page-break-inside: avoid;
                margin-top: 20px;
            }

            .header {
                margin-bottom: 10px;
                padding-bottom: 8px;
            }

            .memo-section {
                margin: 10px 0;
                padding: 5px 0;
            }

            .subject-line {
                margin: 10px 0;
                padding: 8px;
            }

            .teacher-info-box,
            .duty-dates-box {
                margin: 8px 0;
                padding: 6px;
            }

            .letter-body {
                margin: 10px 0;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="print-button no-print" onclick="window.print()">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="letter-container">
        <!-- Header -->
        <div class="header">
            <?php
            // Check for uploaded logo
            $logoPath = getCollegeLogo();
            if ($logoPath): ?>
                <img src="<?php echo $logoPath; ?>" alt="College Logo" class="header-logo">
            <?php endif; ?>

            <div class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
            <div class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</div>
            <div class="college-info">
                উচ্চ মাধ্যমিক পরীক্ষা-২০২৫<br>
                কেন্দ্র নম্বর: ২৯৫
            </div>
        </div>

        <!-- Memo Section -->
        <div class="memo-section">
            <div class="memo-left">
                <div class="memo-number">স্মারক নং: <?php echo $memoNumber; ?></div>
            </div>
            <div class="memo-right">
                <div><strong>তারিখ:</strong> <?php echo formatDateBengali(date('Y-m-d')); ?></div>
            </div>
        </div>

        <!-- Subject -->
        <div class="subject-line">
            <strong>বিষয়:</strong> HSC পরীক্ষা-২০২৫ এর ডিউটি সংক্রান্ত।
        </div>

        <!-- Teacher Information Table (Top) -->
        <div class="teacher-info-box">
            <table class="info-table">
                <tr>
                    <td class="label">নাম</td>
                    <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                    <td class="label">পদবী</td>
                    <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                    <td class="label">কলেজ</td>
                    <td><?php echo htmlspecialchars($teacher['college']); ?></td>
                </tr>
            </table>
        </div>

        <!-- Letter Body -->
        <div class="letter-body">
            <p>
                উপর্যুক্ত বিষয়ের প্রেক্ষিতে জানানো যাচ্ছে যে, আপনাকে উচ্চ মাধ্যমিক পরীক্ষা-২০২৫ এর
                কক্ষ পরিদর্শকের দায়িত্ব দেওয়া হয়েছে। নিম্ন বর্ণিত তারিখ সমূহে পরীক্ষা শুরু হওয়ার
                ৪৫ মিনিট পূর্বে পরীক্ষা নিয়ন্ত্রণ কক্ষে উপস্থিত হওয়ার জন্য আপনাকে অনুরোধ করা হলো।
            </p>

            <!-- Duty Dates -->
            <div class="duty-dates-box">
                <div class="duty-dates-title">ডিউটির তারিখ সমূহ</div>
                <div class="duty-dates-text">
                    <?php
                    $formattedDates = [];
                    foreach ($dutyDates as $dutyDate) {
                        $formattedDates[] = formatDateBengali(trim($dutyDate));
                    }
                    echo implode(', ', $formattedDates);
                    ?>
                    <?php if (!empty($roomNumbers)): ?>
                        <br><strong>রুম নং:</strong> <?php echo implode(', ', $roomNumbers); ?>
                    <?php endif; ?>
                </div>
            </div>

            <p>
                আশা করি আপনি যথাসময়ে উপস্থিত হয়ে দায়িত্ব পালন করবেন।
                আপনার সহযোগিতার জন্য ধন্যবাদ।
            </p>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <?php if ($principalSignature && file_exists($principalSignature['signature_path'])): ?>
                    <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>"
                         alt="Principal Signature" class="signature-image">
                <?php endif; ?>
                <div class="signature-line">
                    অধ্যক্ষ
                </div>
                <div class="signature-name">
                    মোঃ কামাল উদ্দীন
                </div>
                <div class="signature-details">
                    আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                    দামুড়হুদা, চুয়াডাঙ্গা
                </div>
            </div>

            <div class="signature-box">
                <?php if ($convenerSignature && file_exists($convenerSignature['signature_path'])): ?>
                    <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>"
                         alt="Convener Signature" class="signature-image">
                <?php endif; ?>
                <div class="signature-line">
                    আহবায়ক
                </div>
                <div class="signature-name">
                    মোঃ আবু জাফর মোঃ হাসিবুল আলম
                </div>
                <div class="signature-details">
                    HSC পরীক্ষা-২০২৫<br>
                    আব্দুল ওদুদ শাহ ডিগ্রি কলেজ<br>
                    দামুড়হুদা, চুয়াডাঙ্গা
                </div>

            </div>
        </div>


    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() {
        //     setTimeout(() => {
        //         window.print();
        //     }, 1000);
        // }
    </script>
</body>
</html>
