<?php
// Database Setup Script
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

try {
    // First, connect without specifying database to create it if needed
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$dbname' created/verified successfully.<br>";

    // Now connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

    // Disable foreign key checks to allow safe table dropping
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "✅ Foreign key checks disabled.<br>";

    // Drop existing tables if they exist (for fresh start)
    $tablesToDrop = ['student_subjects', 'seating_arrangements', 'teacher_duties', 'room_assignments', 'duty_assignments', 'students', 'teachers', 'subjects', 'exam_rooms', 'system_settings'];
    foreach ($tablesToDrop as $table) {
        $pdo->exec("DROP TABLE IF EXISTS `$table`");
        echo "✅ Table '$table' dropped if existed.<br>";
    }

    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "✅ Foreign key checks re-enabled.<br>";
    
    // Create teachers table
    $createTableSQL = "
    CREATE TABLE teachers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sl_number VARCHAR(10) NOT NULL,
        name VARCHAR(255) NOT NULL,
        mobile VARCHAR(20) NOT NULL,
        subject VARCHAR(100) NOT NULL,
        designation VARCHAR(100) NOT NULL,
        college VARCHAR(255) NOT NULL,
        duty_status ENUM('সাধারন', 'সম সময় অন্তর্ভুক্ত', 'কখনো অন্তর্ভুক্ত নয়') DEFAULT 'সাধারন',
        photo VARCHAR(500) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_sl (sl_number),
        INDEX idx_name (name),
        INDEX idx_subject (subject),
        INDEX idx_duty_status (duty_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $pdo->exec($createTableSQL);
    echo "✅ Teachers table created successfully.<br>";

    // Create students table
    $createStudentsTableSQL = "
    CREATE TABLE students (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        father_name VARCHAR(255) DEFAULT NULL,
        gender ENUM('Male', 'Female', 'Other') DEFAULT 'Male',
        roll VARCHAR(50) NOT NULL UNIQUE,
        registration VARCHAR(50) NOT NULL,
        department VARCHAR(100) NOT NULL,
        academic_year VARCHAR(20) NOT NULL,
        student_type ENUM('Regular', 'Improvement', 'Irregular') DEFAULT 'Regular',
        subjects TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_roll (roll),
        INDEX idx_department (department),
        INDEX idx_student_type (student_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createStudentsTableSQL);
    echo "✅ Students table created successfully.<br>";

    // Create subjects table
    $createSubjectsTableSQL = "
    CREATE TABLE subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_code VARCHAR(10) NOT NULL UNIQUE,
        subject_name VARCHAR(255) NOT NULL,
        subject_name_bengali VARCHAR(255),
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createSubjectsTableSQL);
    echo "✅ Subjects table created successfully.<br>";

    // Insert default subjects
    $defaultSubjects = [
        ['101', 'Bangla 1st Paper', 'বাংলা ১ম পত্র'],
        ['102', 'Bangla 2nd Paper', 'বাংলা ২য় পত্র'],
        ['103', 'English 1st Paper', 'ইংরেজি ১ম পত্র'],
        ['104', 'English 2nd Paper', 'ইংরেজি ২য় পত্র'],
        ['105', 'ICT', 'তথ্য ও যোগাযোগ প্রযুক্তি'],
        ['106', 'Physics 1st Paper', 'পদার্থবিজ্ঞান ১ম পত্র'],
        ['107', 'Physics 2nd Paper', 'পদার্থবিজ্ঞান ২য় পত্র'],
        ['108', 'Chemistry 1st Paper', 'রসায়ন ১ম পত্র'],
        ['109', 'Chemistry 2nd Paper', 'রসায়ন ২য় পত্র'],
        ['110', 'Biology 1st Paper', 'জীববিজ্ঞান ১ম পত্র'],
        ['111', 'Biology 2nd Paper', 'জীববিজ্ঞান ২য় পত্র'],
        ['112', 'Mathematics 1st Paper', 'গণিত ১ম পত্র'],
        ['113', 'Mathematics 2nd Paper', 'গণিত ২য় পত্র']
    ];

    $insertSubjectSQL = "INSERT INTO subjects (subject_code, subject_name, subject_name_bengali) VALUES (?, ?, ?)";
    $subjectStmt = $pdo->prepare($insertSubjectSQL);

    foreach ($defaultSubjects as $subject) {
        $subjectStmt->execute($subject);
    }

    echo "✅ Default subjects inserted successfully.<br>";

    // Create duty_assignments table
    $createDutyTableSQL = "
    CREATE TABLE duty_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        duty_date DATE NOT NULL,
        teacher_id INT NOT NULL,
        room_number VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
        UNIQUE KEY unique_duty_teacher (duty_date, teacher_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($createDutyTableSQL);
    echo "✅ Duty assignments table created successfully.<br>";

    // Insert some sample data
    $sampleTeachers = [
        ['1', 'মোঃ আব্দুল করিম', '01711111111', 'গণিত', 'সহকারী অধ্যাপক', 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'],
        ['2', 'মোছাঃ রোকেয়া খাতুন', '01722222222', 'বাংলা', 'প্রভাষক', 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'],
        ['3', 'মোঃ আব্দুর রহমান', '01733333333', 'ইংরেজি', 'সহকারী অধ্যাপক', 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'],
        ['4', 'মোছাঃ ফাতেমা বেগম', '01744444444', 'পদার্থবিজ্ঞান', 'প্রভাষক', 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ'],
        ['5', 'মোঃ নজরুল ইসলাম', '01755555555', 'রসায়ন', 'সহকারী অধ্যাপক', 'আব্দুল ওদুদ শাহ ডিগ্রি কলেজ']
    ];
    
    $insertSQL = "INSERT INTO teachers (sl_number, name, mobile, subject, designation, college) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($insertSQL);
    
    foreach ($sampleTeachers as $teacher) {
        $stmt->execute($teacher);
    }
    
    echo "✅ Sample teachers data inserted successfully.<br>";

    // Insert sample students data
    $sampleStudents = [
        ['আব্দুল করিম', 'মোঃ আব্দুল হামিদ', 'Male', '100001', 'REG2023001', 'Science', '2023', 'Regular', '101,102,103,104,105,106,107,108,109,110,111,112,113'],
        ['ফাতেমা খাতুন', 'মোঃ আব্দুল কাদের', 'Female', '100002', 'REG2023002', 'Science', '2023', 'Regular', '101,102,103,104,105,106,107,108,109,110,111,112,113'],
        ['মোহাম্মদ রহিম', 'মোঃ আব্দুর রহমান', 'Male', '100003', 'REG2023003', 'Science', '2023', 'Regular', '101,102,103,104,105,106,107,108,109,112,113'],
        ['রোকেয়া বেগম', 'মোঃ আব্দুল মজিদ', 'Female', '100004', 'REG2023004', 'Science', '2023', 'Regular', '101,102,103,104,105,106,107,108,109,110,111'],
        ['আব্দুর রহমান', 'মোঃ আব্দুল আলিম', 'Male', '100005', 'REG2023005', 'Science', '2023', 'Improvement', '106,107,108,109']
    ];

    $insertStudentSQL = "INSERT INTO students (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $studentStmt = $pdo->prepare($insertStudentSQL);

    foreach ($sampleStudents as $student) {
        $studentStmt->execute($student);
    }

    echo "✅ Sample students data inserted successfully.<br>";

    // Verify data
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM teachers");
    $teacherCount = $stmt->fetch()['count'];
    echo "✅ Total teachers in database: $teacherCount<br>";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM students");
    $studentCount = $stmt->fetch()['count'];
    echo "✅ Total students in database: $studentCount<br>";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM subjects");
    $subjectCount = $stmt->fetch()['count'];
    echo "✅ Total subjects in database: $subjectCount<br>";
    
    // Show table structure
    echo "<br><h3>📋 Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE teachers");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<br><h3>👥 Sample Teachers Data:</h3>";
    $stmt = $pdo->query("SELECT id, sl_number, name, subject, designation, duty_status FROM teachers LIMIT 5");
    $teachers = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>SL</th><th>Name</th><th>Subject</th><th>Designation</th><th>Status</th></tr>";
    foreach ($teachers as $teacher) {
        echo "<tr>";
        echo "<td>{$teacher['id']}</td>";
        echo "<td>{$teacher['sl_number']}</td>";
        echo "<td>{$teacher['name']}</td>";
        echo "<td>{$teacher['subject']}</td>";
        echo "<td>{$teacher['designation']}</td>";
        echo "<td>{$teacher['duty_status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample students data
    echo "<br><h3>👨‍🎓 Sample Students Data:</h3>";
    $stmt = $pdo->query("SELECT id, name, roll, department, student_type, academic_year FROM students LIMIT 5");
    $students = $stmt->fetchAll();

    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Roll</th><th>Department</th><th>Type</th><th>Year</th></tr>";
    foreach ($students as $student) {
        echo "<tr>";
        echo "<td>{$student['id']}</td>";
        echo "<td>{$student['name']}</td>";
        echo "<td>{$student['roll']}</td>";
        echo "<td>{$student['department']}</td>";
        echo "<td>{$student['student_type']}</td>";
        echo "<td>{$student['academic_year']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<br><h2>🎉 Database setup completed successfully!</h2>";
    echo "<p>";
    echo "<a href='teacher_duty_management.php'>Teacher Management</a> | ";
    echo "<a href='teacher_list_view.php'>Teacher List</a> | ";
    echo "<a href='view_students.php'>Student Management</a> | ";
    echo "<a href='date_wise_duty_assignment.php'>Duty Assignment</a> | ";
    echo "<a href='room_wise_duty_assignment.php'>Room Assignment</a>";
    echo "</p>";
    
} catch(PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "<br>";
    echo "Please check your database connection settings.";
}
?>
