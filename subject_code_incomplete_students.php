<?php
require_once __DIR__ . '/models/Student.php';
require_once __DIR__ . '/utils/DatabaseHelper.php';
require_once __DIR__ . '/config/database.php';

// Get parameters
$subjectCode = $_GET['code'] ?? '';
$searchName = $_GET['name'] ?? '';
$searchRoll = $_GET['roll'] ?? '';
$searchGroup = $_GET['group'] ?? '';
$searchType = $_GET['type'] ?? '';
$searchGender = $_GET['gender'] ?? '';
$action = $_GET['action'] ?? ''; // 'print' or 'seatcard'

// Subject names mapping for display
$subjectNames = [
    '101' => 'Bangla (101)',
    '107' => 'English (107)',
    '275' => 'Information & Technology (275)',
    '174' => 'Physics (174)',
    '176' => 'Chemistry (176)',
    '178' => 'Biology (178)',
    '265' => 'Higher Math (265)',
    '253' => 'Accounting (253)',
    '292' => 'Finance & Banking (292)',
    '277' => 'Business Organization & Management (277)',
    '286' => 'Production Management & Marketing (286)',
    '109' => 'Economics (109)',
    '121' => 'Logic (121)',
    '117' => 'Sociology (117)',
    '249' => 'Study Of Islam (249)',
    '271' => 'Social Work (271)',
    '273' => 'Home Science (273)',
    '267' => 'Islamic History (267)',
    '269' => 'Civics & Good Governance (269)',
    '304' => 'History (304)',
    '129' => 'Statistics (129)'
];

// Get the subject name for the selected code
$currentSubjectName = $subjectNames[$subjectCode] ?? "Subject Code $subjectCode";

// Initialize variables
$students = [];
$filteredStudents = [];
$incompleteStudents = [];
$completeStudents = [];
$error = '';
$totalStudents = 0;
$totalIncomplete = 0;
$totalIncompleteWithSubject = 0;
$totalCompletelWithSubject = 0;

try {
    // Connect to database
    $database = new Database();
    $db = $database->getConnection();
    $dbHelper = new DatabaseHelper($db);

    // Get all students
    $student = new Student();
    $allStudents = $student->getAll();
    $totalStudents = count($allStudents);
    
    // Process all students to identify those with incomplete subjects
    foreach ($allStudents as $s) {
        $subjectCount = 0;
        $subjectList = [];
        $hasTargetSubject = false;
        
        // Check if student has the specific subject code
        if (!empty($subjectCode)) {
            // Check new subjects field format first
            if (!empty($s['subjects'])) {
                $subjects = explode(',', $s['subjects']);
                $subjectCount = count(array_filter($subjects, function($subject) {
                    return !empty(trim($subject));
                }));
                $subjectList = array_filter(array_map('trim', $subjects));
                
                // Check if target subject is in the list
                $hasTargetSubject = in_array($subjectCode, $subjectList);
            } else {
                // Check old subject fields
                for ($i = 1; $i <= 13; $i++) {
                    $subField = 'sub_' . $i;
                    if (!empty($s[$subField])) {
                        $subjectCount++;
                        $subjectList[] = $s[$subField];
                        
                        if ($s[$subField] == $subjectCode) {
                            $hasTargetSubject = true;
                        }
                    }
                }
            }
        }
        
        // Store subject count and list
        $s['subject_count'] = $subjectCount;
        $s['subject_list'] = $subjectList;
        $s['has_target_subject'] = $hasTargetSubject;
        
        // Check if student is incomplete (less than 13 subjects)
        $isIncomplete = ($subjectCount < 13);
        
        // Filter by search criteria
        $matchesSearch = true;
        
        // Name search
        if (!empty($searchName)) {
            $studentName = $s['name'] ?? $s['student_name'] ?? '';
            if (stripos($studentName, $searchName) === false) {
                $matchesSearch = false;
            }
        }
        
        // Roll search
        if (!empty($searchRoll)) {
            $studentRoll = $s['roll'] ?? $s['roll_no'] ?? '';
            if (stripos($studentRoll, $searchRoll) === false) {
                $matchesSearch = false;
            }
        }
        
        // Group search
        if (!empty($searchGroup)) {
            $studentGroup = $s['department'] ?? $s['group_name'] ?? '';
            if (stripos($studentGroup, $searchGroup) === false) {
                $matchesSearch = false;
            }
        }
        
        // Type search
        if (!empty($searchType)) {
            $studentType = $s['student_type'] ?? $s['type'] ?? '';
            if (stripos($studentType, $searchType) === false) {
                $matchesSearch = false;
            }
        }
        
        // Gender search
        if (!empty($searchGender)) {
            $studentGender = $s['gender'] ?? '';
            if (stripos($studentGender, $searchGender) === false) {
                $matchesSearch = false;
            }
        }
        
        // Add to appropriate lists if matches search criteria
        if ($matchesSearch) {
            if ($hasTargetSubject) {
                $students[] = $s;
                
                if ($isIncomplete) {
                    $incompleteStudents[] = $s;
                    $totalIncompleteWithSubject++;
                } else {
                    $completeStudents[] = $s;
                    $totalCompletelWithSubject++;
                }
            }
            
            if ($isIncomplete) {
                $totalIncomplete++;
            }
        }
    }
    
    // Sort all lists by roll number
    usort($students, function($a, $b) {
        $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
        $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
        return $rollA - $rollB;
    });
    
    usort($incompleteStudents, function($a, $b) {
        $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
        $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
        return $rollA - $rollB;
    });
    
    usort($completeStudents, function($a, $b) {
        $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
        $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
        return $rollA - $rollB;
    });
    
    // Handle action requests
    if ($action === 'print' && !empty($subjectCode)) {
        // Redirect to print page with the selected students
        $rollNumbers = array_map(function($s) {
            return $s['roll'] ?? $s['roll_no'] ?? '';
        }, $incompleteStudents);
        $rollsString = implode(',', $rollNumbers);
        
        header("Location: working_seat_cards.php?rolls=$rollsString&print_list=1");
        exit;
    } else if ($action === 'seatcard' && !empty($subjectCode)) {
        // Redirect to seat cards page with the selected students
        $rollNumbers = array_map(function($s) {
            return $s['roll'] ?? $s['roll_no'] ?? '';
        }, $incompleteStudents);
        $rollsString = implode(',', $rollNumbers);
        
        header("Location: working_seat_cards.php?rolls=$rollsString");
        exit;
    }
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কোড ভিত্তিক অসম্পূর্ণ শিক্ষার্থী - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-content {
            padding-top: 20px;
            padding-bottom: 50px;
        }
        
        .card {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            font-weight: 600;
        }
        
        .subject-btn {
            margin: 5px;
            border-radius: 30px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .subject-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 10px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            color: white;
            margin-bottom: 15px;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        
        .table th {
            background-color: #f8f9fa;
        }
        
        .badge-subject {
            font-size: 0.8rem;
            margin: 2px;
            padding: 4px 8px;
        }
        
        /* Export button styles */
        .export-btn-group .btn {
            border-radius: 4px;
            margin-right: 5px;
            font-size: 0.9rem;
            transition: all 0.2s;
        }
        
        .export-btn-group .dropdown-menu {
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
            padding: 8px 0;
        }
        
        .export-btn-group .dropdown-item {
            padding: 8px 15px;
            font-size: 0.9rem;
        }
        
        .export-btn-group .dropdown-item:hover {
            background-color: #f0f7ff;
        }
        
        /* Hide DataTables default buttons */
        .dt-buttons {
            display: none;
        }
        
        /* Loading animation */
        @keyframes pulse {
            0% { transform: scale(0.95); opacity: 0.7; }
            50% { transform: scale(1); opacity: 1; }
            100% { transform: scale(0.95); opacity: 0.7; }
        }
        
        .spinner-border {
            animation: spinner-border 1s linear infinite, pulse 2s infinite;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <h2 class="text-white">
                        <i class="fas fa-book-reader me-2"></i>
                        কোড ভিত্তিক অসম্পূর্ণ শিক্ষার্থী
                    </h2>
                    <p class="text-white-50">পরীক্ষার বিষয় কোড অনুযায়ী অসম্পূর্ণ শিক্ষার্থীদের তালিকা</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="incomplete_subject_students.php" class="btn btn-info">
                        <i class="fas fa-list-alt me-1"></i> অসম্পূর্ণ শিক্ষার্থী
                    </a>
                </div>
            </div>

            <!-- Subject Selection -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>বিষয় কোড নির্বাচন করুন</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($subjectNames as $code => $name): ?>
                                <div class="col-md-2 col-sm-4 col-6">
                                    <a href="?code=<?php echo $code; ?>" class="btn <?php echo ($code === $subjectCode) ? 'btn-primary' : 'btn-outline-primary'; ?> subject-btn w-100 mb-2">
                                        <?php echo $name; ?>
                                    </a>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($subjectCode)): ?>
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card bg-primary">
                        <div class="stats-number"><?php echo count($students); ?></div>
                        <h6>সর্বমোট শিক্ষার্থী (<?php echo $currentSubjectName; ?>)</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-success">
                        <div class="stats-number"><?php echo $totalCompletelWithSubject; ?></div>
                        <h6>সম্পূর্ণ বিষয়ের শিক্ষার্থী</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-warning">
                        <div class="stats-number"><?php echo $totalIncompleteWithSubject; ?></div>
                        <h6>অসম্পূর্ণ বিষয়ের শিক্ষার্থী</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card bg-info">
                        <div class="stats-number"><?php echo $totalStudents; ?></div>
                        <h6>ডাটাবেসে মোট শিক্ষার্থী</h6>
                    </div>
                </div>
            </div>

            <!-- Search Form -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-search me-2"></i>খুঁজুন</h5>
                        </div>
                        <div class="card-body">
                            <form action="" method="GET" class="row g-3">
                                <input type="hidden" name="code" value="<?php echo htmlspecialchars($subjectCode); ?>">
                                
                                <div class="col-md-3">
                                    <label class="form-label">শিক্ষার্থীর নাম</label>
                                    <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($searchName); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">রোল নম্বর</label>
                                    <input type="text" class="form-control" name="roll" value="<?php echo htmlspecialchars($searchRoll); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">গ্রুপ</label>
                                    <input type="text" class="form-control" name="group" value="<?php echo htmlspecialchars($searchGroup); ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">ধরণ</label>
                                    <select class="form-select" name="type">
                                        <option value="">সকল</option>
                                        <option value="Regular" <?php echo $searchType === 'Regular' ? 'selected' : ''; ?>>নিয়মিত</option>
                                        <option value="Irregular" <?php echo $searchType === 'Irregular' ? 'selected' : ''; ?>>অনিয়মিত</option>
                                        <option value="Improvement" <?php echo $searchType === 'Improvement' ? 'selected' : ''; ?>>উন্নতি</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">লিঙ্গ</label>
                                    <select class="form-select" name="gender">
                                        <option value="">সকল</option>
                                        <option value="Male" <?php echo $searchGender === 'Male' ? 'selected' : ''; ?>>ছেলে</option>
                                        <option value="Female" <?php echo $searchGender === 'Female' ? 'selected' : ''; ?>>মেয়ে</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-1 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="mb-3">অসম্পূর্ণ শিক্ষার্থী (<?php echo $totalIncompleteWithSubject; ?>)</h5>
                                    <div class="d-flex gap-2">
                                        <a href="?code=<?php echo $subjectCode; ?>&action=print<?php echo !empty($searchName) ? '&name=' . urlencode($searchName) : ''; ?><?php echo !empty($searchRoll) ? '&roll=' . urlencode($searchRoll) : ''; ?><?php echo !empty($searchGroup) ? '&group=' . urlencode($searchGroup) : ''; ?><?php echo !empty($searchType) ? '&type=' . urlencode($searchType) : ''; ?><?php echo !empty($searchGender) ? '&gender=' . urlencode($searchGender) : ''; ?>" class="btn btn-success">
                                            <i class="fas fa-print me-1"></i> তালিকা প্রিন্ট
                                        </a>
                                        <a href="?code=<?php echo $subjectCode; ?>&action=seatcard<?php echo !empty($searchName) ? '&name=' . urlencode($searchName) : ''; ?><?php echo !empty($searchRoll) ? '&roll=' . urlencode($searchRoll) : ''; ?><?php echo !empty($searchGroup) ? '&group=' . urlencode($searchGroup) : ''; ?><?php echo !empty($searchType) ? '&type=' . urlencode($searchType) : ''; ?><?php echo !empty($searchGender) ? '&gender=' . urlencode($searchGender) : ''; ?>" class="btn btn-primary">
                                            <i class="fas fa-id-card me-1"></i> সীট কার্ড
                                        </a>
                                        <?php
                                        // Create roll numbers string for all incomplete students
                                        $rollNumbers = array_map(function($s) {
                                            return $s['roll'] ?? $s['roll_no'] ?? '';
                                        }, $incompleteStudents);
                                        $rollsString = implode(',', $rollNumbers);
                                        ?>
                                        <a href="export_incomplete_students.php?rolls=<?php echo urlencode($rollsString); ?>" class="btn btn-info" target="_blank">
                                            <i class="fas fa-file-csv me-1"></i> CSV এক্সপোর্ট
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <?php
                                    // Create roll numbers string for all incomplete students
                                    $rollNumbers = array_map(function($s) {
                                        return $s['roll'] ?? $s['roll_no'] ?? '';
                                    }, $incompleteStudents);
                                    $rollsString = implode(',', $rollNumbers);
                                    ?>
                                    <small class="text-muted d-block mb-2">রোল নম্বরগুলি:</small>
                                    <div class="form-group">
                                        <textarea class="form-control" rows="2" readonly><?php echo $rollsString; ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Student Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="fas fa-table me-2"></i>
                                <?php echo $currentSubjectName; ?> বিষয়ে অসম্পূর্ণ শিক্ষার্থী তালিকা
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($incompleteStudents)): ?>
                                <div class="alert alert-info m-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <?php echo $currentSubjectName; ?> বিষয়ে কোন অসম্পূর্ণ শিক্ষার্থী পাওয়া যায়নি।
                                </div>
                            <?php else: ?>
                                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-bottom">
                                    <div>
                                        <strong>মোট শিক্ষার্থী:</strong> <?php echo count($incompleteStudents); ?>
                                    </div>
                                    <div class="d-flex align-items-center gap-3">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-download me-1"></i> এক্সপোর্ট
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php
                                                // Create roll numbers string for export
                                                $rollNumbers = array_map(function($s) {
                                                    return $s['roll'] ?? $s['roll_no'] ?? '';
                                                }, $incompleteStudents);
                                                $rollsString = implode(',', $rollNumbers);
                                                ?>
                                                <li><a class="dropdown-item" href="export_incomplete_students.php?rolls=<?php echo urlencode($rollsString); ?>" target="_blank">
                                                    <i class="fas fa-file-csv me-1"></i> CSV এক্সপোর্ট
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" id="exportExcel">
                                                    <i class="fas fa-file-excel me-1"></i> এক্সেল এক্সপোর্ট
                                                </a></li>
                                                <li><a class="dropdown-item" href="#" id="exportPDF">
                                                    <i class="fas fa-file-pdf me-1"></i> PDF এক্সপোর্ট
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" id="printTable">
                                                    <i class="fas fa-print me-1"></i> প্রিন্ট
                                                </a></li>
                                            </ul>
                                        </div>
                                        <label class="me-2">প্রতি পৃষ্ঠায় দেখান:</label>
                                        <select id="pageSizeSelector" class="form-select form-select-sm" style="width: 80px;">
                                            <option value="10">10</option>
                                            <option value="25">25</option>
                                            <option value="50">50</option>
                                            <option value="100">100</option>
                                            <option value="200" selected>200</option>
                                            <option value="-1">সকল</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Loading Spinner -->
                                <div id="tableLoadingSpinner" class="text-center p-5">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">লোড হচ্ছে...</span>
                                    </div>
                                    <p class="mt-2">শিক্ষার্থী তালিকা লোড হচ্ছে, অনুগ্রহ করে অপেক্ষা করুন...</p>
                                </div>
                                
                                <div class="table-responsive" id="tableContainer" style="display: none;">
                                    <table class="table table-striped table-hover" id="studentTable">
                                        <thead>
                                            <tr>
                                                <th width="5%">#</th>
                                                <th width="10%">রোল</th>
                                                <th width="20%">নাম</th>
                                                <th width="10%">গ্রুপ</th>
                                                <th width="10%">ধরণ</th>
                                                <th width="8%">বিষয় সংখ্যা</th>
                                                <th>বিষয়সমূহ</th>
                                                <th width="10%">অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($incompleteStudents as $index => $s): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? 'N/A'); ?></td>
                                                <td><?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <?php 
                                                    $type = $s['student_type'] ?? $s['type'] ?? 'Regular';
                                                    $badgeClass = '';
                                                    switch($type) {
                                                        case 'Regular': $badgeClass = 'bg-success'; break;
                                                        case 'Irregular': $badgeClass = 'bg-danger'; break;
                                                        case 'Improvement': $badgeClass = 'bg-warning'; break;
                                                        default: $badgeClass = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $badgeClass; ?>"><?php echo $type; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary"><?php echo $s['subject_count']; ?>/13</span>
                                                </td>
                                                <td>
                                                    <?php foreach($s['subject_list'] as $subj): ?>
                                                        <span class="badge <?php echo ($subj == $subjectCode) ? 'bg-warning' : 'bg-secondary'; ?> badge-subject">
                                                            <?php echo $subj; ?>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </td>
                                                <td>
                                                    <a href="working_seat_cards.php?rolls=<?php echo $s['roll'] ?? $s['roll_no'] ?? ''; ?>" class="btn btn-sm btn-primary" target="_blank">
                                                        <i class="fas fa-id-card"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- No Subject Selected -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-book fa-4x text-muted mb-4"></i>
                            <h4 class="mb-3">অনুগ্রহ করে একটি বিষয় কোড নির্বাচন করুন</h4>
                            <p class="text-muted">
                                নির্দিষ্ট বিষয় কোড সহ অসম্পূর্ণ শিক্ষার্থীদের খুঁজে পেতে উপরের বিষয় কোড বাটনগুলি থেকে একটি নির্বাচন করুন।
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Export Libraries -->
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var studentTable = $('#studentTable').DataTable({
                "language": {
                    "lengthMenu": "প্রতি পৃষ্ঠায় _MENU_ টি রেকর্ড দেখান",
                    "zeroRecords": "কোন রেকর্ড পাওয়া যায়নি",
                    "info": "_START_ থেকে _END_ পর্যন্ত _TOTAL_ টি রেকর্ডের মধ্যে",
                    "infoEmpty": "০ টি রেকর্ড",
                    "infoFiltered": "(মোট _MAX_ টি রেকর্ড থেকে ফিল্টার করা হয়েছে)",
                    "search": "খুঁজুন:",
                    "paginate": {
                        "first": "প্রথম",
                        "last": "শেষ",
                        "next": "পরবর্তী",
                        "previous": "পূর্ববর্তী"
                    }
                },
                "pageLength": 200,
                "lengthMenu": [[10, 25, 50, 100, 200, -1], [10, 25, 50, 100, 200, "সকল"]],
                "deferRender": true,
                "processing": true,
                "initComplete": function(settings, json) {
                    // Hide spinner and show table when loading is complete
                    $('#tableLoadingSpinner').hide();
                    $('#tableContainer').show();
                }
            });

            // Add event listener for page size selector
            $('#pageSizeSelector').change(function() {
                var selectedPageSize = $(this).val();
                studentTable.page.len(selectedPageSize).draw();
            });
            
            // Excel Export
            $('#exportExcel').click(function(e) {
                e.preventDefault();
                
                // Create a hidden button with DataTables Excel export functionality
                $('<button>')
                    .attr('id', 'hiddenExcelButton')
                    .css('display', 'none')
                    .appendTo('body');
                
                new $.fn.dataTable.Buttons(studentTable, {
                    buttons: [
                        {
                            extend: 'excel',
                            text: 'Excel',
                            title: '<?php echo $currentSubjectName; ?> বিষয়ে অসম্পূর্ণ শিক্ষার্থী তালিকা',
                            exportOptions: {
                                columns: [1, 2, 3, 4, 5, 6]
                            }
                        }
                    ]
                }).container().appendTo('#hiddenExcelButton');
                
                $('#hiddenExcelButton .buttons-excel').click();
                $('#hiddenExcelButton').remove();
            });
            
            // PDF Export
            $('#exportPDF').click(function(e) {
                e.preventDefault();
                
                // Create a hidden button with DataTables PDF export functionality
                $('<button>')
                    .attr('id', 'hiddenPDFButton')
                    .css('display', 'none')
                    .appendTo('body');
                
                new $.fn.dataTable.Buttons(studentTable, {
                    buttons: [
                        {
                            extend: 'pdf',
                            text: 'PDF',
                            title: '<?php echo $currentSubjectName; ?> বিষয়ে অসম্পূর্ণ শিক্ষার্থী তালিকা',
                            exportOptions: {
                                columns: [1, 2, 3, 4, 5, 6]
                            },
                            orientation: 'landscape',
                            pageSize: 'A4',
                            customize: function(doc) {
                                doc.defaultStyle.font = 'SolaimanLipi';
                                doc.styles.tableHeader.fontSize = 10;
                                doc.styles.tableHeader.alignment = 'left';
                                doc.styles.tableBodyEven.alignment = 'left';
                                doc.styles.tableBodyOdd.alignment = 'left';
                            }
                        }
                    ]
                }).container().appendTo('#hiddenPDFButton');
                
                $('#hiddenPDFButton .buttons-pdf').click();
                $('#hiddenPDFButton').remove();
            });
            
            // Print Table
            $('#printTable').click(function(e) {
                e.preventDefault();
                
                // Create a hidden button with DataTables print functionality
                $('<button>')
                    .attr('id', 'hiddenPrintButton')
                    .css('display', 'none')
                    .appendTo('body');
                
                new $.fn.dataTable.Buttons(studentTable, {
                    buttons: [
                        {
                            extend: 'print',
                            text: 'Print',
                            title: '<?php echo $currentSubjectName; ?> বিষয়ে অসম্পূর্ণ শিক্ষার্থী তালিকা',
                            exportOptions: {
                                columns: [1, 2, 3, 4, 5, 6]
                            },
                            customize: function(win) {
                                $(win.document.body)
                                    .css('font-size', '10pt')
                                    .css('font-family', 'SolaimanLipi, Arial, sans-serif');
                                
                                $(win.document.body).find('table')
                                    .addClass('compact')
                                    .css('font-size', '9pt')
                                    .css('border-collapse', 'collapse');
                                    
                                $(win.document.body).find('h1')
                                    .css('text-align', 'center')
                                    .css('font-size', '14pt');
                            }
                        }
                    ]
                }).container().appendTo('#hiddenPrintButton');
                
                $('#hiddenPrintButton .buttons-print').click();
                $('#hiddenPrintButton').remove();
            });
        });
    </script>
</body>
</html>
