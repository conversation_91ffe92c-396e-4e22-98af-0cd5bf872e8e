<?php
// Update Honorarium Config
// This tool saves the detected database structure for the honorarium system
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Process form data
$teachers_table = $_POST['teachers_table'] ?? '';
$duties_table = $_POST['duties_table'] ?? '';
$teacher_id_field = $_POST['teacher_id_field'] ?? '';
$duty_fk_field = $_POST['duty_fk_field'] ?? '';
$teacher_name_field = $_POST['teacher_name_field'] ?? '';
$duty_date_field = $_POST['duty_date_field'] ?? '';

$status = 'error';
$message = 'সমস্যা হয়েছে। আবার চেষ্টা করুন।';

// Validate inputs
if (empty($teachers_table) || empty($duties_table) || empty($teacher_id_field) || 
    empty($duty_fk_field) || empty($teacher_name_field) || empty($duty_date_field)) {
    $status = 'error';
    $message = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করা হয়নি।';
} else {
    // Create config array
    $config = [
        'teachers_table' => $teachers_table,
        'duties_table' => $duties_table,
        'teacher_id_field' => $teacher_id_field,
        'duty_fk_field' => $duty_fk_field,
        'teacher_name_field' => $teacher_name_field,
        'duty_date_field' => $duty_date_field,
        'room_field' => 'room_number', // Default
        'shift_field' => 'duty_shift',  // Default
        'updated_at' => date('Y-m-d H:i:s')
    ];
    
    // Create config directory if it doesn't exist
    if (!file_exists('config')) {
        mkdir('config', 0755);
    }
    
    // Save config to file
    $config_json = json_encode($config, JSON_PRETTY_PRINT);
    $save_result = file_put_contents('config/honorarium_structure.json', $config_json);
    
    if ($save_result !== false) {
        // Update the teacher_honorarium_fix.php file 
        updateHonorariumFile($config);
        
        $status = 'success';
        $message = 'কনফিগারেশন সফলভাবে আপডেট করা হয়েছে।';
    }
}

// Function to update the honorarium file
function updateHonorariumFile($config) {
    $file_path = 'teacher_honorarium_fix.php';
    
    if (!file_exists($file_path)) {
        // Create a new file from template if it doesn't exist
        return false;
    }
    
    // Generate the configuration code
    $config_code = "
    // Auto-generated database structure configuration - " . date('Y-m-d H:i:s') . "
    \$db_structure = [
        'teachers_table' => '{$config['teachers_table']}',
        'duties_table' => '{$config['duties_table']}',
        'teacher_id_field' => '{$config['teacher_id_field']}',
        'teacher_name_field' => '{$config['teacher_name_field']}',
        'duty_date_field' => '{$config['duty_date_field']}',
        'duty_fk_field' => '{$config['duty_fk_field']}',
        'room_field' => '{$config['room_field']}', 
        'shift_field' => '{$config['shift_field']}'
    ];";
    
    // Read the file content
    $file_content = file_get_contents($file_path);
    
    // Check if the file already has a configuration section
    if (strpos($file_content, '// Auto-generated database structure configuration') !== false) {
        // Replace the existing configuration
        $pattern = '/\/\/ Auto-generated database structure configuration.*?\$db_structure = \[.*?\];/s';
        $file_content = preg_replace($pattern, $config_code, $file_content);
    } else {
        // Find a good place to insert the configuration (after the initial comments and includes)
        $pos = strpos($file_content, '<?php');
        if ($pos !== false) {
            $pos = strpos($file_content, "\n", $pos + 5);
            if ($pos !== false) {
                $file_content = substr_replace($file_content, "\n" . $config_code . "\n", $pos, 0);
            }
        }
    }
    
    // Write back to the file
    return file_put_contents($file_path, $file_content);
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>কনফিগারেশন আপডেট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            max-width: 500px;
        }
        .card-header {
            background: <?php echo $status == 'success' ? '#d4edda' : '#f8d7da'; ?>;
            border-radius: 15px 15px 0 0;
            border-bottom: 2px solid <?php echo $status == 'success' ? '#28a745' : '#dc3545'; ?>;
            color: <?php echo $status == 'success' ? '#155724' : '#721c24'; ?>;
            text-align: center;
            padding: 20px;
        }
        .icon-container {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .success-icon {
            color: #28a745;
        }
        .error-icon {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <div class="icon-container">
                            <?php if ($status == 'success'): ?>
                                <i class="fas fa-check-circle success-icon"></i>
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle error-icon"></i>
                            <?php endif; ?>
                        </div>
                        <h2><?php echo $status == 'success' ? 'সফল!' : 'সমস্যা!'; ?></h2>
                    </div>
                    <div class="card-body text-center">
                        <p class="lead mb-4"><?php echo $message; ?></p>
                        
                        <?php if ($status == 'success'): ?>
                            <div class="alert alert-success">
                                <strong>ডাটাবেস স্ট্রাকচার কনফিগারেশন সংরক্ষিত হয়েছে।</strong><br>
                                এখন আপনি সন্মানী হিসাব পেজে গিয়ে কাজ চালিয়ে যেতে পারেন।
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <strong>কনফিগারেশন আপডেট করা যায়নি।</strong><br>
                                পুনরায় চেষ্টা করুন অথবা ম্যানুয়ালি ফাইলটি আপডেট করুন।
                            </div>
                        <?php endif; ?>
                        
                        <div class="d-grid gap-2">
                            <a href="teacher_honorarium_fix.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-calculator me-2"></i>
                                সন্মানী হিসাব পেজে যান
                            </a>
                            <a href="database_field_explorer.php" class="btn btn-outline-secondary mt-2">
                                <i class="fas fa-database me-2"></i>
                                ডাটাবেস ফিল্ড এক্সপ্লোরারে ফিরুন
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html> 