<?php
// Simple seat card generator without complex filtering
require_once __DIR__ . '/models/Student.php';

// Get parameters from URL
$subjectCode = $_GET['subject'] ?? '';
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);

echo "<!DOCTYPE html>";
echo "<html lang='bn'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Seat Cards - HSC Exam 2025</title>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>";
echo "body { font-family: 'Hind Siliguri', Arial, sans-serif; margin: 0; padding: 20px; }";
echo ".debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; }";
echo ".seat-card { border: 2px solid #333; padding: 10px; margin: 10px; width: 200px; height: 250px; display: inline-block; text-align: center; }";
echo ".exam-title { font-weight: bold; font-size: 14px; border-bottom: 1px solid #333; padding-bottom: 5px; margin-bottom: 5px; }";
echo ".college-info { font-size: 10px; color: #666; margin-bottom: 10px; }";
echo ".student-name { font-size: 16px; font-weight: 600; margin: 10px 0; }";
echo ".roll-number { background: #f0f0f0; border: 2px solid #333; padding: 8px; margin: 10px 0; font-size: 20px; font-weight: bold; }";
echo ".student-details { font-size: 11px; line-height: 1.3; }";
echo "</style>";
echo "</head>";
echo "<body>";

// Debug information
echo "<div class='debug'>";
echo "<h3>Debug Information:</h3>";
echo "Subject Code from URL: " . htmlspecialchars($subjectCode) . "<br>";
echo "Cards Per Page: " . $cardsPerPage . "<br>";

try {
    $student = new Student();
    
    // Get all students first
    $allStudents = $student->getAll();
    echo "Total students in database: " . count($allStudents) . "<br>";
    
    // Filter by subject if provided
    if (!empty($subjectCode)) {
        $filteredStudents = [];
        foreach ($allStudents as $s) {
            // Check all subject fields
            for ($i = 1; $i <= 13; $i++) {
                $subField = 'sub_' . $i;
                if (isset($s[$subField]) && $s[$subField] == $subjectCode) {
                    $filteredStudents[] = $s;
                    break; // Found the subject, add student and move to next
                }
            }
        }
        $students = $filteredStudents;
        echo "Students with subject " . $subjectCode . ": " . count($students) . "<br>";
    } else {
        $students = $allStudents;
        echo "Showing all students<br>";
    }
    
    echo "</div>";
    
    // Show seat cards
    if (count($students) > 0) {
        echo "<h2>Seat Cards (" . count($students) . " students)</h2>";
        
        $count = 0;
        foreach ($students as $s) {
            if ($count >= 20) break; // Limit to first 20 for testing
            
            echo "<div class='seat-card'>";
            echo "<div class='exam-title'>HSC Exam-2025</div>";
            echo "<div class='college-info'>দামুড়হুদা, কোড. 295</div>";
            echo "<div class='student-name'>" . htmlspecialchars($s['student_name']) . "</div>";
            echo "<div class='roll-number'>" . htmlspecialchars($s['roll_no']) . "</div>";
            echo "<div class='student-details'>";
            echo "<div><strong>রেজিঃ</strong> " . htmlspecialchars($s['reg_no']) . "</div>";
            echo "<div><strong>বিভাগ:</strong> " . htmlspecialchars($s['group_name']) . "</div>";
            echo "<div><strong>শিক্ষাবর্ষ:</strong> " . htmlspecialchars($s['session']) . "</div>";
            echo "</div>";
            echo "</div>";
            
            $count++;
        }
        
        if (count($students) > 20) {
            echo "<p><strong>Note:</strong> Showing first 20 cards only. Total: " . count($students) . " students.</p>";
        }
        
    } else {
        echo "<h2>No students found</h2>";
        if (!empty($subjectCode)) {
            echo "<p>No students found with subject code: " . htmlspecialchars($subjectCode) . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "</body>";
echo "</html>";
?>
