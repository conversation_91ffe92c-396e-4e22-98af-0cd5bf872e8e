<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
// Get all teachers sorted by serial number in ascending order
$teachers = $teacherManager->getAllTeachers('sl_number ASC');
$dutyDates = $teacherManager->getAllDutyDates();
$selectedTeacherId = $_GET['teacher_id'] ?? '';
$selectedTeacher = null;
$teacherDuties = [];

// Get teacher details if selected
if ($selectedTeacherId) {
    try {
        $selectedTeacher = $teacherManager->getTeacherById($selectedTeacherId);
        if ($selectedTeacher) {
            // Get all duties assigned to this teacher
            $teacherDuties = $teacherManager->getTeacherDutyAssignments($selectedTeacherId);
        }
    } catch (Exception $e) {
        $message = 'শিক্ষকের তথ্য লোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle add new duty assignment
if (isset($_POST['add_duty'])) {
    $teacherId = $_POST['teacher_id'];
    $dutyDate = $_POST['duty_date'];
    $roomNumber = $_POST['room_number'] ?? '';
    
    if (!empty($teacherId) && !empty($dutyDate)) {
        try {
            // Check if teacher already has duty on this date
            $existingDuty = $teacherManager->checkTeacherDutyExists($teacherId, $dutyDate);
            
            if ($existingDuty) {
                $message = 'শিক্ষক ইতিমধ্যে এই তারিখে ডিউটিতে আছেন!';
                $messageType = 'warning';
            } else {
                // Add new duty assignment
                $teacherManager->assignTeacherToDuty($teacherId, $dutyDate, $roomNumber);
                
                // Refresh teacher duties
                $teacherDuties = $teacherManager->getTeacherDutyAssignments($teacherId);
                
                $message = 'শিক্ষকের ডিউটি সফলভাবে যোগ করা হয়েছে!';
                $messageType = 'success';
            }
        } catch (Exception $e) {
            $message = 'ডিউটি যোগ করতে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'শিক্ষক এবং তারিখ নির্বাচন করুন!';
        $messageType = 'warning';
    }
}

// Handle edit duty assignment
if (isset($_POST['update_duty'])) {
    $dutyId = $_POST['duty_id'];
    $teacherId = $_POST['teacher_id'];
    $dutyDate = $_POST['duty_date'];
    $roomNumber = $_POST['room_number'] ?? '';
    
    if (!empty($dutyId) && !empty($teacherId) && !empty($dutyDate)) {
        try {
            // Update duty assignment
            $teacherManager->updateTeacherDuty($dutyId, $teacherId, $dutyDate, $roomNumber);
            
            // Refresh teacher duties
            $teacherDuties = $teacherManager->getTeacherDutyAssignments($teacherId);
            
            $message = 'শিক্ষকের ডিউটি সফলভাবে আপডেট করা হয়েছে!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'ডিউটি আপডেট করতে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'সমস্ত প্রয়োজনীয় তথ্য প্রদান করুন!';
        $messageType = 'warning';
    }
}

// Handle delete duty assignment
if (isset($_POST['delete_duty'])) {
    $dutyId = $_POST['duty_id'];
    $teacherId = $_POST['teacher_id'];
    
    if (!empty($dutyId) && !empty($teacherId)) {
        try {
            // Delete duty assignment
            $teacherManager->deleteTeacherDuty($dutyId);
            
            // Refresh teacher duties
            $teacherDuties = $teacherManager->getTeacherDutyAssignments($teacherId);
            
            $message = 'শিক্ষকের ডিউটি সফলভাবে মুছে ফেলা হয়েছে!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'ডিউটি মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'ডিউটি আইডি এবং শিক্ষক আইডি প্রদান করুন!';
        $messageType = 'warning';
    }
}

// Add these functions to TeacherManager class in teacher_db.php
// Make sure these functions are available in the TeacherManager class
/*
// Get all duty assignments for a specific teacher
public function getTeacherDutyAssignments($teacherId) {
    try {
        $sql = "SELECT da.id, da.duty_date, da.room_number, t.name, t.subject 
                FROM duty_assignments da 
                JOIN teachers t ON da.teacher_id = t.id 
                WHERE da.teacher_id = ? 
                ORDER BY da.duty_date ASC";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$teacherId]);
        return $stmt->fetchAll();
    } catch(PDOException $e) {
        throw new Exception("Error fetching teacher duties: " . $e->getMessage());
    }
}

// Check if teacher already has duty on a specific date
public function checkTeacherDutyExists($teacherId, $dutyDate) {
    try {
        $sql = "SELECT COUNT(*) FROM duty_assignments WHERE teacher_id = ? AND duty_date = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$teacherId, $dutyDate]);
        return $stmt->fetchColumn() > 0;
    } catch(PDOException $e) {
        throw new Exception("Error checking teacher duty: " . $e->getMessage());
    }
}

// Assign teacher to duty
public function assignTeacherToDuty($teacherId, $dutyDate, $roomNumber = null) {
    try {
        $sql = "INSERT INTO duty_assignments (teacher_id, duty_date, room_number) VALUES (?, ?, ?)";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$teacherId, $dutyDate, $roomNumber]);
    } catch(PDOException $e) {
        throw new Exception("Error assigning teacher to duty: " . $e->getMessage());
    }
}

// Update teacher duty
public function updateTeacherDuty($dutyId, $teacherId, $dutyDate, $roomNumber = null) {
    try {
        $sql = "UPDATE duty_assignments SET teacher_id = ?, duty_date = ?, room_number = ? WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$teacherId, $dutyDate, $roomNumber, $dutyId]);
    } catch(PDOException $e) {
        throw new Exception("Error updating teacher duty: " . $e->getMessage());
    }
}

// Delete teacher duty
public function deleteTeacherDuty($dutyId) {
    try {
        $sql = "DELETE FROM duty_assignments WHERE id = ?";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$dutyId]);
    } catch(PDOException $e) {
        throw new Exception("Error deleting teacher duty: " . $e->getMessage());
    }
}
*/
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক ভিত্তিক ডিউটি ম্যানেজমেন্ট - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .teacher-card.selected {
            border-color: #667eea;
            background-color: #f0f4ff;
        }
        .teacher-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .teacher-details {
            font-size: 14px;
            color: #6c757d;
        }
        .duty-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        .duty-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        /* Fix for delete button appearing outside table */
        .table-responsive {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            table-layout: fixed;
        }
        
        .action-col {
            width: 90px;
            min-width: 90px;
            text-align: center;
        }
        
        .btn-delete {
            display: inline-block;
            width: 32px;
            height: 32px;
            padding: 0;
            line-height: 32px;
            text-align: center;
        }
        
        /* Ensure form stays within cell bounds */
        td form {
            display: inline-block;
        }
        .search-box {
            margin-bottom: 20px;
        }
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .card { box-shadow: none !important; border: 1px solid #ddd !important; }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="text-center">
                            <h1><i class="fas fa-user-clock text-primary"></i> শিক্ষক ভিত্তিক ডিউটি ম্যানেজমেন্ট</h1>
                            <p class="text-muted mb-0">শিক্ষক অনুযায়ী ডিউটি যোগ, সম্পাদনা এবং আপডেট করুন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Teacher Selection -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="section-header">
                            <h4><i class="fas fa-users me-2"></i> শিক্ষক নির্বাচন</h4>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" class="form-control" id="teacherSearch" placeholder="শিক্ষকের নাম বা বিষয় দিয়ে খুঁজুন..." onkeyup="searchTeachers()">
                        </div>
                        
                        <div class="teacher-list" style="max-height: 600px; overflow-y: auto;">
                            <?php foreach ($teachers as $teacher): ?>
                                <a href="?teacher_id=<?php echo $teacher['id']; ?>" class="text-decoration-none">
                                    <div class="teacher-card <?php echo ($selectedTeacherId == $teacher['id']) ? 'selected' : ''; ?>">
                                        <div class="teacher-name">
                                            <?php echo htmlspecialchars($teacher['name']); ?>
                                            <span class="badge bg-secondary float-end"><?php echo htmlspecialchars($teacher['sl_number']); ?></span>
                                        </div>
                                        <div class="teacher-details">
                                            <div><i class="fas fa-book me-1"></i> <?php echo htmlspecialchars($teacher['subject']); ?></div>
                                            <div><i class="fas fa-user-tie me-1"></i> <?php echo htmlspecialchars($teacher['designation']); ?></div>
                                            <div><i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($teacher['mobile']); ?></div>
                                        </div>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Duty Management -->
                <div class="col-md-8">
                    <?php if ($selectedTeacher): ?>
                        <div class="card">
                            <div class="section-header">
                                <h4><i class="fas fa-user me-2"></i> <?php echo htmlspecialchars($selectedTeacher['name']); ?> এর ডিউটি ম্যানেজমেন্ট</h4>
                            </div>
                            
                            <!-- Teacher Info -->
                            <div class="row mb-4">
                                <div class="col-md-3 text-center">
                                    <?php if (!empty($selectedTeacher['photo']) && file_exists($selectedTeacher['photo'])): ?>
                                        <img src="<?php echo $selectedTeacher['photo']; ?>" class="img-fluid rounded" style="max-height: 150px;">
                                    <?php else: ?>
                                        <div class="bg-light rounded p-4">
                                            <i class="fas fa-user fa-5x text-secondary"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-9">
                                    <h5><?php echo htmlspecialchars($selectedTeacher['name']); ?></h5>
                                    <p class="mb-1"><strong>ক্রমিক:</strong> <?php echo htmlspecialchars($selectedTeacher['sl_number']); ?></p>
                                    <p class="mb-1"><strong>বিষয়:</strong> <?php echo htmlspecialchars($selectedTeacher['subject']); ?></p>
                                    <p class="mb-1"><strong>পদবি:</strong> <?php echo htmlspecialchars($selectedTeacher['designation']); ?></p>
                                    <p class="mb-1"><strong>মোবাইল:</strong> <?php echo htmlspecialchars($selectedTeacher['mobile']); ?></p>
                                    <p class="mb-1"><strong>কলেজ:</strong> <?php echo htmlspecialchars($selectedTeacher['college']); ?></p>
                                    <p class="mb-0"><strong>ডিউটি স্ট্যাটাস:</strong> <?php echo htmlspecialchars($selectedTeacher['duty_status']); ?></p>
                                </div>
                            </div>
                            
                            <!-- Add New Duty -->
                            <div class="mb-4">
                                <h5><i class="fas fa-plus-circle me-2"></i> নতুন ডিউটি যোগ করুন</h5>
                                <form method="POST" class="row g-3">
                                    <input type="hidden" name="teacher_id" value="<?php echo $selectedTeacher['id']; ?>">
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">তারিখ নির্বাচন করুন</label>
                                        <select class="form-select" name="duty_date" required>
                                            <option value="">তারিখ নির্বাচন করুন</option>
                                            <?php foreach ($dutyDates as $date): ?>
                                                <option value="<?php echo $date; ?>"><?php echo date('d F Y', strtotime($date)); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">রুম নম্বর (ঐচ্ছিক)</label>
                                        <input type="text" class="form-control" name="room_number" placeholder="রুম নম্বর লিখুন...">
                                    </div>
                                    
                                    <div class="col-12">
                                        <button type="submit" name="add_duty" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i> ডিউটি যোগ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Duty List -->
                            <div class="duty-list">
                                <h5><i class="fas fa-list me-2"></i> ডিউটি তালিকা</h5>
                                
                                <?php if (empty($teacherDuties)): ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> এই শিক্ষকের কোন ডিউটি নেই!
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>তারিখ</th>
                                                    <th>রুম</th>
                                                    <th>কর্ম</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($teacherDuties as $duty): ?>
                                                    <tr>
                                                        <td><?php echo date('d F Y', strtotime($duty['duty_date'])); ?></td>
                                                        <td><?php echo !empty($duty['room_number']) ? htmlspecialchars($duty['room_number']) : 'রুম বরাদ্দ করা হয়নি'; ?></td>
                                                        <td>
                                                            <div class="action-buttons">
                                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDutyModal<?php echo $duty['id']; ?>">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDutyModal<?php echo $duty['id']; ?>">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    
                                                    <!-- Edit Duty Modal -->
                                                    <div class="modal fade" id="editDutyModal<?php echo $duty['id']; ?>" tabindex="-1" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">ডিউটি সম্পাদনা করুন</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <form method="POST">
                                                                    <div class="modal-body">
                                                                        <input type="hidden" name="duty_id" value="<?php echo $duty['id']; ?>">
                                                                        <input type="hidden" name="teacher_id" value="<?php echo $selectedTeacher['id']; ?>">
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">তারিখ</label>
                                                                            <select class="form-select" name="duty_date" required>
                                                                                <?php foreach ($dutyDates as $date): ?>
                                                                                    <option value="<?php echo $date; ?>" <?php echo ($date == $duty['duty_date']) ? 'selected' : ''; ?>>
                                                                                        <?php echo date('d F Y', strtotime($date)); ?>
                                                                                    </option>
                                                                                <?php endforeach; ?>
                                                                            </select>
                                                                        </div>
                                                                        
                                                                        <div class="mb-3">
                                                                            <label class="form-label">রুম নম্বর</label>
                                                                            <input type="text" class="form-control" name="room_number" value="<?php echo htmlspecialchars($duty['room_number'] ?? ''); ?>" placeholder="রুম নম্বর লিখুন...">
                                                                        </div>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                                        <button type="submit" name="update_duty" class="btn btn-primary">আপডেট করুন</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Delete Duty Modal -->
                                                    <div class="modal fade" id="deleteDutyModal<?php echo $duty['id']; ?>" tabindex="-1" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title">ডিউটি মুছে ফেলুন</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <form method="POST">
                                                                    <div class="modal-body">
                                                                        <input type="hidden" name="duty_id" value="<?php echo $duty['id']; ?>">
                                                                        <input type="hidden" name="teacher_id" value="<?php echo $selectedTeacher['id']; ?>">
                                                                        
                                                                        <p>আপনি কি নিশ্চিত যে আপনি এই ডিউটি মুছে ফেলতে চান?</p>
                                                                        <p><strong>তারিখ:</strong> <?php echo date('d F Y', strtotime($duty['duty_date'])); ?></p>
                                                                        <p><strong>রুম:</strong> <?php echo !empty($duty['room_number']) ? htmlspecialchars($duty['room_number']) : 'রুম বরাদ্দ করা হয়নি'; ?></p>
                                                                    </div>
                                                                    <div class="modal-footer">
                                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                                        <button type="submit" name="delete_duty" class="btn btn-danger">মুছে ফেলুন</button>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card">
                            <div class="text-center p-5">
                                <i class="fas fa-user-clock fa-5x text-secondary mb-3"></i>
                                <h4>শিক্ষক নির্বাচন করুন</h4>
                                <p class="text-muted">ডিউটি ম্যানেজমেন্ট করতে বাম পাশ থেকে একজন শিক্ষক নির্বাচন করুন</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function searchTeachers() {
            const searchText = document.getElementById('teacherSearch').value.toLowerCase();
            const teacherCards = document.querySelectorAll('.teacher-card');
            
            teacherCards.forEach(card => {
                const teacherName = card.querySelector('.teacher-name').textContent.toLowerCase();
                const teacherDetails = card.querySelector('.teacher-details').textContent.toLowerCase();
                
                if (teacherName.includes(searchText) || teacherDetails.includes(searchText)) {
                    card.parentElement.style.display = '';
                } else {
                    card.parentElement.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>