<?php
$message = '';
$messageType = '';
$uploadResult = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['excel_file'])) {
    try {
        // Check file upload errors first
        if ($_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
            $message = 'File upload error occurred. Error code: ' . $_FILES['excel_file']['error'];
            $messageType = 'danger';
        } else {
            // Check if composer dependencies are available
            $composerExists = file_exists(__DIR__ . '/vendor/autoload.php');

            if (!$composerExists) {
                // Use simple CSV processor as fallback
                require_once __DIR__ . '/utils/SimpleCSVProcessor.php';
                $processor = new SimpleCSVProcessor();

                // Check if uploaded file is CSV
                $fileExtension = strtolower(pathinfo($_FILES['excel_file']['name'], PATHINFO_EXTENSION));
                if ($fileExtension !== 'csv') {
                    $message = 'Without Composer dependencies, only CSV files are supported. Please upload a CSV file or install Composer for Excel support.';
                    $messageType = 'warning';
                } else {
                    $useCSVProcessor = true;
                }
            } else {
                require_once __DIR__ . '/utils/ExcelProcessor.php';
                $processor = new ExcelProcessor();
                $useCSVProcessor = false;
            }

            if (!isset($message)) {
                // Validate file
                $validationErrors = $processor->validateFile($_FILES['excel_file']);

                if (!empty($validationErrors)) {
                    $message = implode('<br>', $validationErrors);
                    $messageType = 'danger';
                } else {
                // Move uploaded file to uploads directory
                $uploadDir = __DIR__ . '/uploads/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $fileName = time() . '_' . $_FILES['excel_file']['name'];
                $filePath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['excel_file']['tmp_name'], $filePath)) {
                    // Process the file based on processor type
                    if (isset($useCSVProcessor) && $useCSVProcessor) {
                        $result = $processor->processCSVFile($filePath);
                    } else {
                        $result = $processor->processExcelFile($filePath);
                    }

                    if (!empty($result['errors'])) {
                        $message = 'File processed with errors:<br>' . implode('<br>', $result['errors']);
                        $messageType = 'warning';
                        $uploadResult = $result; // Show partial results
                    } else {
                        // Save students to database
                        try {
                            if ($processor->saveStudents($result['students'])) {
                                $count = count($result['students']);
                                $message = "Successfully uploaded {$count} students!";
                                $messageType = 'success';
                                $uploadResult = $result;
                            } else {
                                $message = 'Failed to save students to database.';
                                $messageType = 'danger';
                            }
                        } catch (Exception $e) {
                            $message = 'Database error: ' . $e->getMessage();
                            $messageType = 'danger';
                        }
                    }

                    // Clean up uploaded file
                    unlink($filePath);
                } else {
                    $message = 'Failed to upload file.';
                    $messageType = 'danger';
                }
            }
        }
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Excel - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .upload-area {
            border: 3px dashed #007bff;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background: rgba(255,255,255,0.2);
        }
        .upload-area.dragover {
            border-color: #28a745;
            background: rgba(40,167,69,0.1);
        }
        .file-input {
            display: none;
        }
        .upload-icon {
            font-size: 4rem;
            color: #007bff;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h3><i class="fas fa-upload"></i> Excel File Upload</h3>
                            <div>
                                <a href="download_template.php" class="btn btn-info">
                                    <i class="fas fa-download"></i> Download Template
                                </a>
                                <a href="view_students.php" class="btn btn-secondary">
                                    <i class="fas fa-list"></i> View Students
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                    <?php echo $message; ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <!-- Upload Result Display -->
                            <?php if ($uploadResult): ?>
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> Upload Summary:</h5>
                                    <ul class="mb-0">
                                        <li><strong>Total Rows:</strong> <?php echo $uploadResult['total_rows']; ?></li>
                                        <li><strong>Valid Rows:</strong> <?php echo $uploadResult['valid_rows']; ?></li>
                                        <li><strong>Success:</strong> <?php echo $uploadResult['success'] ? 'Yes' : 'No'; ?></li>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Check if composer is available -->
                            <?php if (!file_exists(__DIR__ . '/vendor/autoload.php')): ?>
                                <div class="alert alert-info">
                                    <h5><i class="fas fa-info-circle"></i> CSV Upload Available</h5>
                                    <p>Excel files (.xlsx, .xls) require Composer dependencies. Currently only CSV files are supported.</p>
                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <p><strong>To enable Excel support:</strong></p>
                                            <a href="auto_install_composer.php" class="btn btn-sm btn-primary me-2">
                                                <i class="fas fa-magic"></i> Auto Install
                                            </a>
                                            <a href="install_composer_dependencies.php" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-book"></i> Manual Guide
                                            </a>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Alternatives:</strong></p>
                                            <a href="#" onclick="downloadSampleCSV()" class="btn btn-sm btn-info me-2"><i class="fas fa-download"></i> Download Sample CSV</a>
                                            <a href="add_student.php" class="btn btn-sm btn-success"><i class="fas fa-plus"></i> Add Student</a>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <form method="POST" enctype="multipart/form-data" id="uploadForm">
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h4>Drag & Drop Excel File Here</h4>
                                    <p class="text-muted">or click to browse files</p>
                                    <input type="file" name="excel_file" id="excelFile" class="file-input"
                                           accept="<?php echo file_exists(__DIR__ . '/vendor/autoload.php') ? '.xlsx,.xls,.csv' : '.csv'; ?>" required>
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('excelFile').click()">
                                        <i class="fas fa-folder-open"></i> Choose File
                                    </button>
                                </div>
                                
                                <div id="fileInfo" class="mt-3" style="display: none;">
                                    <div class="alert alert-info">
                                        <strong>Selected File:</strong> <span id="fileName"></span><br>
                                        <strong>Size:</strong> <span id="fileSize"></span>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <button type="submit" class="btn btn-success btn-lg" id="uploadBtn" disabled>
                                        <i class="fas fa-upload"></i> Upload & Process
                                    </button>
                                </div>
                            </form>

                            <!-- Instructions -->
                            <div class="row mt-5">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h5><i class="fas fa-info-circle"></i> File Requirements</h5>
                                        </div>
                                        <div class="card-body">
                                            <ul class="list-unstyled">
                                                <?php if (file_exists(__DIR__ . '/vendor/autoload.php')): ?>
                                                <li><i class="fas fa-check text-success"></i> Excel files (.xlsx, .xls) or CSV</li>
                                                <?php else: ?>
                                                <li><i class="fas fa-check text-success"></i> CSV files only (Excel requires Composer)</li>
                                                <?php endif; ?>
                                                <li><i class="fas fa-check text-success"></i> Maximum file size: 10MB</li>
                                                <li><i class="fas fa-check text-success"></i> First row should contain headers</li>
                                                <li><i class="fas fa-check text-success"></i> Student Name and Roll Number are required</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h5><i class="fas fa-exclamation-triangle"></i> Column Order</h5>
                                        </div>
                                        <div class="card-body">
                                            <small>
                                                C.Code, EIIN, Roll No., Reg. No., Session, Type, Group, 
                                                Student Name, Father Name, Gender, Sub 1-13
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('excelFile');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // File input change
        fileInput.addEventListener('change', handleFileSelect);

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.style.display = 'block';
                uploadBtn.disabled = false;
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function downloadSampleCSV() {
            // Create sample CSV content
            const csvContent = `C.Code,EIIN,Roll No,Reg No,Session,Type,Group,Student Name,Father Name,Gender,Sub 1,Sub 2,Sub 3,Sub 4,Sub 5,Sub 6,Sub 7,Sub 8,Sub 9,Sub 10,Sub 11,Sub 12,Sub 13
295,136257,100001,REG2023001,2023,Regular,Science,আব্দুল করিম,মোঃ রহিম,Male,101,102,103,104,105,106,107,108,109,110,111,112,113
295,136257,100002,REG2023002,2023,Regular,Science,ফাতেমা খাতুন,মোঃ আলী,Female,101,102,103,104,105,106,107,108,109,110,111,,
295,136257,100003,IMP2023001,2023,Improvement,Science,মোহাম্মদ রহিম,মোঃ করিম,Male,106,107,108,109,,,,,,,,,`;

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'sample_students.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
