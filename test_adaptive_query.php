<?php
// Test Adaptive Query
// This tool helps validate and test the table relationships
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Get form parameters
$teacher_table = $_POST['teacher_table'] ?? '';
$duty_table = $_POST['duty_table'] ?? '';
$teacher_id_field = $_POST['teacher_id_field'] ?? '';
$duty_fk_field = $_POST['duty_fk_field'] ?? '';
$teacher_name_field = $_POST['teacher_name_field'] ?? '';
$duty_date_field = $_POST['duty_date_field'] ?? '';

// Default values for testing
if (empty($teacher_table) || empty($duty_table)) {
    // Find teacher-related tables
    $result = $conn->query("SHOW TABLES");
    if ($result) {
        while ($row = $result->fetch_array()) {
            $table_name = $row[0];
            if (empty($teacher_table) && stripos($table_name, 'teacher') !== false) {
                $teacher_table = $table_name;
            } elseif (empty($duty_table) && (stripos($table_name, 'duty') !== false || stripos($table_name, 'assign') !== false)) {
                $duty_table = $table_name;
            }
        }
    }
}

// Find primary key of teacher table
if (empty($teacher_id_field) && !empty($teacher_table)) {
    $result = $conn->query("SHOW KEYS FROM `$teacher_table` WHERE Key_name = 'PRIMARY'");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $teacher_id_field = $row['Column_name'];
    }
}

// Find teacher name field
if (empty($teacher_name_field) && !empty($teacher_table)) {
    $result = $conn->query("DESCRIBE `$teacher_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'name') !== false) {
                $teacher_name_field = $row['Field'];
                break;
            }
        }
    }
}

// Find foreign key in duty table
if (empty($duty_fk_field) && !empty($duty_table)) {
    $result = $conn->query("DESCRIBE `$duty_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'teacher') !== false && stripos($row['Field'], 'id') !== false) {
                $duty_fk_field = $row['Field'];
                break;
            } elseif ($row['Field'] == 'teacher_id') {
                $duty_fk_field = $row['Field'];
                break;
            }
        }
    }
}

// Find date field in duty table
if (empty($duty_date_field) && !empty($duty_table)) {
    $result = $conn->query("DESCRIBE `$duty_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'date') !== false) {
                $duty_date_field = $row['Field'];
                break;
            }
        }
    }
}

// Test simple join query
$join_query = "";
$join_result = null;
$error_message = "";
$query_explanation = "";

if (!empty($teacher_table) && !empty($duty_table) && !empty($teacher_id_field) && !empty($duty_fk_field)) {
    // Basic validation
    $valid = true;
    $validation_messages = [];
    
    // Check if tables exist
    $tables_result = $conn->query("SHOW TABLES");
    $tables = [];
    while ($tables_result && $row = $tables_result->fetch_array()) {
        $tables[] = $row[0];
    }
    
    if (!in_array($teacher_table, $tables)) {
        $valid = false;
        $validation_messages[] = "শিক্ষক টেবিল ($teacher_table) পাওয়া যায়নি";
    }
    
    if (!in_array($duty_table, $tables)) {
        $valid = false;
        $validation_messages[] = "ডিউটি টেবিল ($duty_table) পাওয়া যায়নি";
    }
    
    // Check if fields exist in tables
    if ($valid) {
        $teacher_fields_result = $conn->query("DESCRIBE `$teacher_table`");
        $teacher_fields = [];
        while ($teacher_fields_result && $row = $teacher_fields_result->fetch_assoc()) {
            $teacher_fields[] = $row['Field'];
        }
        
        if (!in_array($teacher_id_field, $teacher_fields)) {
            $valid = false;
            $validation_messages[] = "শিক্ষক আইডি ফিল্ড ($teacher_id_field) $teacher_table টেবিলে পাওয়া যায়নি";
        }
        
        if (!in_array($teacher_name_field, $teacher_fields)) {
            $valid = false;
            $validation_messages[] = "শিক্ষকের নাম ফিল্ড ($teacher_name_field) $teacher_table টেবিলে পাওয়া যায়নি";
        }
        
        $duty_fields_result = $conn->query("DESCRIBE `$duty_table`");
        $duty_fields = [];
        while ($duty_fields_result && $row = $duty_fields_result->fetch_assoc()) {
            $duty_fields[] = $row['Field'];
        }
        
        if (!in_array($duty_fk_field, $duty_fields)) {
            $valid = false;
            $validation_messages[] = "ডিউটি টেবিলে শিক্ষক আইডি ফিল্ড ($duty_fk_field) পাওয়া যায়নি";
        }
        
        if (!in_array($duty_date_field, $duty_fields)) {
            $valid = false;
            $validation_messages[] = "ডিউটি তারিখ ফিল্ড ($duty_date_field) পাওয়া যায়নি";
        }
    }
    
    if ($valid) {
        // Construct the query
        $join_query = "
            SELECT d.*, t.$teacher_name_field AS teacher_name 
            FROM `$duty_table` d 
            JOIN `$teacher_table` t ON d.$duty_fk_field = t.$teacher_id_field
            LIMIT 10
        ";
        
        $query_explanation = "
            <div class='alert alert-primary'>
                <h5>কুয়েরি ব্যাখ্যা:</h5>
                <ul>
                    <li><strong>$teacher_table</strong> টেবিলের <code>$teacher_id_field</code> ফিল্ড দিয়ে</li>
                    <li><strong>$duty_table</strong> টেবিলের <code>$duty_fk_field</code> ফিল্ডের সাথে জয়েন করা হয়েছে</li>
                    <li>শিক্ষকের নাম <code>$teacher_name_field</code> ফিল্ড থেকে নেওয়া হয়েছে</li>
                    <li>ডিউটি তারিখ <code>$duty_date_field</code> ফিল্ডে আছে</li>
                </ul>
            </div>
        ";
        
        try {
            $join_result = $conn->query($join_query);
            
            if ($join_result === false) {
                $error_message = $conn->error;
            }
        } catch (mysqli_sql_exception $e) {
            $error_message = $e->getMessage();
        }
    } else {
        $error_message = implode("<br>", $validation_messages);
    }
}

// Generate the adaptive query code for teacher_honorarium_fix.php
$adaptive_code = "";
if (!empty($teacher_table) && !empty($duty_table) && !empty($teacher_id_field) && !empty($duty_fk_field)) {
    $adaptive_code = "
    // Detect database structure
    \$db_structure = [
        'teachers_table' => '$teacher_table',
        'duties_table' => '$duty_table',
        'teacher_id_field' => '$teacher_id_field',
        'teacher_name_field' => '$teacher_name_field',
        'duty_date_field' => '$duty_date_field',
        'room_field' => 'room_number', // Adjust as needed
        'shift_field' => 'duty_shift'  // Adjust as needed
    ];
    
    // Dynamic SQL for getting teacher duties
    \$sql = \"SELECT d.*, 
               t.$teacher_name_field AS teacher_name,
               t.designation AS designation,
               t.subject AS subject
            FROM $duty_table d
            JOIN $teacher_table t ON d.$duty_fk_field = t.$teacher_id_field
            WHERE 
                ($duty_date_field BETWEEN '\$start_date' AND '\$end_date')
                OR (STR_TO_DATE($duty_date_field, '%Y-%m-%d') BETWEEN '\$start_date' AND '\$end_date')
                OR (STR_TO_DATE($duty_date_field, '%d-%m-%Y') BETWEEN '\$start_date' AND '\$end_date')
            ORDER BY $duty_date_field\";
    ";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>এডাপ্টিভ কুয়েরী টেস্ট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card-header {
            background: #eaf4ff;
            border-radius: 10px 10px 0 0;
            border-bottom: 2px solid #007bff;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">
            <i class="fas fa-database me-2"></i>
            এডাপ্টিভ কুয়েরী টেস্ট
        </h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>টেবিল এবং ফিল্ড তথ্য</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>শিক্ষক টেবিল তথ্য:</h5>
                        <ul>
                            <li><strong>টেবিল নাম:</strong> <?php echo $teacher_table ?: 'অজানা'; ?></li>
                            <li><strong>আইডি ফিল্ড:</strong> <?php echo $teacher_id_field ?: 'অজানা'; ?></li>
                            <li><strong>নাম ফিল্ড:</strong> <?php echo $teacher_name_field ?: 'অজানা'; ?></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>ডিউটি টেবিল তথ্য:</h5>
                        <ul>
                            <li><strong>টেবিল নাম:</strong> <?php echo $duty_table ?: 'অজানা'; ?></li>
                            <li><strong>শিক্ষক আইডি (ফরেন কি):</strong> <?php echo $duty_fk_field ?: 'অজানা'; ?></li>
                            <li><strong>তারিখ ফিল্ড:</strong> <?php echo $duty_date_field ?: 'অজানা'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if (!empty($query_explanation)): ?>
            <?php echo $query_explanation; ?>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>টেস্ট কুয়েরী</h3>
            </div>
            <div class="card-body">
                <?php if (!empty($join_query)): ?>
                    <h5>SQL কোয়েরী:</h5>
                    <pre><code><?php echo htmlspecialchars($join_query); ?></code></pre>
                    
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>এরর:</strong> <?php echo $error_message; ?>
                        </div>
                    <?php elseif ($join_result && $join_result->num_rows > 0): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            কুয়েরী সফল! <?php echo $join_result->num_rows; ?> টি রেজাল্ট পাওয়া গেছে।
                        </div>
                        
                        <h5>প্রথম <?php echo min($join_result->num_rows, 10); ?> রেজাল্ট:</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <?php
                                        $first_row = $join_result->fetch_assoc();
                                        $join_result->data_seek(0);
                                        foreach ($first_row as $field => $value) {
                                            echo "<th>$field</th>";
                                        }
                                        ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($row = $join_result->fetch_assoc()): ?>
                                    <tr>
                                        <?php foreach ($row as $value): ?>
                                        <td><?php echo htmlspecialchars($value); ?></td>
                                        <?php endforeach; ?>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            কুয়েরী সফলভাবে চালানো হয়েছে কিন্তু কোন রেজাল্ট পাওয়া যায়নি।
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        কুয়েরী তৈরি করার জন্য পর্যাপ্ত তথ্য নেই।
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if (!empty($adaptive_code)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3>এডাপ্টিভ কোড বিভাগ</h3>
            </div>
            <div class="card-body">
                <p>আপনার <code>teacher_honorarium_fix.php</code> ফাইলে নিম্নলিখিত কোড ব্যবহার করুন:</p>
                <pre><code><?php echo htmlspecialchars($adaptive_code); ?></code></pre>
                
                <form action="update_honorarium_config.php" method="post" class="mt-3">
                    <input type="hidden" name="teachers_table" value="<?php echo htmlspecialchars($teacher_table); ?>">
                    <input type="hidden" name="duties_table" value="<?php echo htmlspecialchars($duty_table); ?>">
                    <input type="hidden" name="teacher_id_field" value="<?php echo htmlspecialchars($teacher_id_field); ?>">
                    <input type="hidden" name="duty_fk_field" value="<?php echo htmlspecialchars($duty_fk_field); ?>">
                    <input type="hidden" name="teacher_name_field" value="<?php echo htmlspecialchars($teacher_name_field); ?>">
                    <input type="hidden" name="duty_date_field" value="<?php echo htmlspecialchars($duty_date_field); ?>">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> কনফিগারেশন আপডেট করুন
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="text-center mt-5 mb-3">
            <a href="database_field_explorer.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-arrow-left me-2"></i>
                ফিল্ড এক্সপ্লোরারে ফিরে যান
            </a>
            <a href="teacher_honorarium_fix.php" class="btn btn-outline-success">
                <i class="fas fa-calculator me-2"></i>
                সন্মানী হিসাব পেজে যান
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>