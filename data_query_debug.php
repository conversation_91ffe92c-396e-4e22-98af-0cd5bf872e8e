<?php
// This is a debugging script to show all duty assignments in the database
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

echo '<h2>ডিউটি অ্যাসাইনমেন্ট ডেটা ডিবাগিং</h2>';

function showTableData($conn, $table) {
    echo "<h3>টেবিল: $table</h3>";
    
    // Get column information
    $columns_query = "SHOW COLUMNS FROM $table";
    $columns_result = $conn->query($columns_query);
    
    if ($columns_result === false) {
        echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
        return;
    }
    
    $columns = [];
    while ($column = $columns_result->fetch_assoc()) {
        $columns[] = $column['Field'];
    }
    
    // Get table data
    $query = "SELECT * FROM $table LIMIT 100";
    $result = $conn->query($query);
    
    if ($result === false) {
        echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
        return;
    }
    
    if ($result->num_rows === 0) {
        echo "<div style='color:orange'>টেবিলে কোন ডাটা নেই।</div>";
        return;
    }
    
    echo "<div style='overflow-x:auto;'>";
    echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
    
    // Headers
    echo "<tr style='background:#f0f0f0;'>";
    foreach ($columns as $column) {
        echo "<th>$column</th>";
    }
    echo "</tr>";
    
    // Data
    $count = 0;
    while ($row = $result->fetch_assoc()) {
        $count++;
        echo "<tr>";
        foreach ($columns as $column) {
            echo "<td>" . htmlspecialchars($row[$column] ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
    
    echo "<p>মোট $count রেকর্ড দেখানো হয়েছে</p>";
    
    // Additional date format info for duty_assignments
    if ($table == 'duty_assignments' && $count > 0) {
        // Get duty_date format
        $date_query = "SELECT duty_date FROM $table LIMIT 5";
        $date_result = $conn->query($date_query);
        
        if ($date_result && $date_result->num_rows > 0) {
            echo "<h4>ডিউটি তারিখ ফরম্যাট:</h4>";
            echo "<ul>";
            
            while ($date_row = $date_result->fetch_assoc()) {
                $date = $date_row['duty_date'];
                echo "<li>Original: $date</li>";
                
                // Try to parse with different formats
                $formats = ['Y-m-d', 'd-m-Y', 'm/d/Y', 'Y/m/d'];
                foreach ($formats as $format) {
                    $parsed = date_create_from_format($format, $date);
                    if ($parsed !== false) {
                        echo "<li>Format seems to be: $format</li>";
                        break;
                    }
                }
            }
            
            echo "</ul>";
        }
        
        // Date range query example
        $start_date = date('Y-m-d', strtotime('-7 days'));
        $end_date = date('Y-m-d');
        
        echo "<h4>সাম্প্রতিক তারিখ সীমা কুয়েরী:</h4>";
        echo "<p>Start date: $start_date</p>";
        echo "<p>End date: $end_date</p>";
        
        $range_query = "SELECT COUNT(*) as count FROM $table WHERE duty_date BETWEEN '$start_date' AND '$end_date'";
        $range_result = $conn->query($range_query);
        
        if ($range_result && $range_row = $range_result->fetch_assoc()) {
            echo "<p>এই সময়ে পাওয়া ডিউটি: " . $range_row['count'] . "</p>";
        } else {
            echo "<p style='color:red'>কুয়েরী এরর: " . $conn->error . "</p>";
        }
        
        // Show distinct dates
        $dates_query = "SELECT DISTINCT duty_date FROM $table ORDER BY duty_date DESC LIMIT 20";
        $dates_result = $conn->query($dates_query);
        
        if ($dates_result) {
            echo "<h4>ডাটাবেসে উপস্থিত তারিখসমূহ:</h4>";
            echo "<ul>";
            
            while ($date_row = $dates_result->fetch_assoc()) {
                echo "<li>" . $date_row['duty_date'] . "</li>";
            }
            
            echo "</ul>";
        }
    }
}

// Show duty_assignments table data
showTableData($conn, 'duty_assignments');

// Show joined data between duty_assignments and teachers
echo "<h3>জয়েন্ট কুয়েরী: duty_assignments এবং teachers</h3>";
$join_query = "
    SELECT a.id, a.duty_date, a.room_number, a.duty_shift, t.teacher_name, t.designation
    FROM duty_assignments a
    JOIN teachers t ON a.teacher_id = t.id
    ORDER BY a.duty_date DESC
    LIMIT 20
";

$join_result = $conn->query($join_query);

if ($join_result === false) {
    echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
} else {
    if ($join_result->num_rows === 0) {
        echo "<div style='color:orange'>কোন জয়েন্ট ডাটা পাওয়া যায়নি।</div>";
    } else {
        echo "<div style='overflow-x:auto;'>";
        echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
        
        // Headers
        echo "<tr style='background:#f0f0f0;'>";
        echo "<th>ID</th>";
        echo "<th>তারিখ</th>";
        echo "<th>রুম</th>";
        echo "<th>শিফট</th>";
        echo "<th>শিক্ষকের নাম</th>";
        echo "<th>পদবি</th>";
        echo "</tr>";
        
        // Data
        while ($row = $join_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['duty_date']) . "</td>";
            echo "<td>" . htmlspecialchars($row['room_number']) . "</td>";
            echo "<td>" . htmlspecialchars($row['duty_shift']) . "</td>";
            echo "<td>" . htmlspecialchars($row['teacher_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['designation']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
}

// Form to test date range query
echo "<h3>তারিখ সীমা কুয়েরী টেস্ট:</h3>";
echo "<form method='get'>";
echo "<div style='margin-bottom:10px'>";
echo "<label for='start_date'>শুরুর তারিখ: </label>";
echo "<input type='date' id='start_date' name='start_date' value='" . ($_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'))) . "'>";
echo "</div>";

echo "<div style='margin-bottom:10px'>";
echo "<label for='end_date'>শেষের তারিখ: </label>";
echo "<input type='date' id='end_date' name='end_date' value='" . ($_GET['end_date'] ?? date('Y-m-d')) . "'>";
echo "</div>";

echo "<button type='submit'>কুয়েরী টেস্ট করুন</button>";
echo "</form>";

// Process date range test query if submitted
if (isset($_GET['start_date']) && isset($_GET['end_date'])) {
    $test_start = $_GET['start_date'];
    $test_end = $_GET['end_date'];
    
    echo "<h4>রিজাল্ট:</h4>";
    echo "<p>শুরুর তারিখ: $test_start</p>";
    echo "<p>শেষের তারিখ: $test_end</p>";
    
    // Run test query
    $test_query = "
        SELECT a.id, a.duty_date, a.room_number, a.duty_shift, t.teacher_name
        FROM duty_assignments a
        JOIN teachers t ON a.teacher_id = t.id
        WHERE a.duty_date BETWEEN '$test_start' AND '$test_end'
        ORDER BY a.duty_date
    ";
    
    $test_result = $conn->query($test_query);
    
    if ($test_result === false) {
        echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
    } else {
        $count = $test_result->num_rows;
        echo "<p>পাওয়া ডিউটি: $count</p>";
        
        if ($count > 0) {
            echo "<div style='overflow-x:auto;'>";
            echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
            
            // Headers
            echo "<tr style='background:#f0f0f0;'>";
            echo "<th>ID</th>";
            echo "<th>তারিখ</th>";
            echo "<th>রুম</th>";
            echo "<th>শিফট</th>";
            echo "<th>শিক্ষকের নাম</th>";
            echo "</tr>";
            
            // Data
            while ($row = $test_result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                echo "<td>" . htmlspecialchars($row['duty_date']) . "</td>";
                echo "<td>" . htmlspecialchars($row['room_number']) . "</td>";
                echo "<td>" . htmlspecialchars($row['duty_shift']) . "</td>";
                echo "<td>" . htmlspecialchars($row['teacher_name']) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div style='color:orange'>এই তারিখ সীমার মধ্যে কোন ডিউটি পাওয়া যায়নি।</div>";
        }
    }
}

// Back to honorarium calculator
echo "<div style='margin-top:30px'>";
echo "<a href='teacher_honorarium_calculator.php' style='text-decoration:none;color:#007bff;'>← সন্মানী হিসাব পেজে ফিরে যান</a>";
echo "</div>";
?> 