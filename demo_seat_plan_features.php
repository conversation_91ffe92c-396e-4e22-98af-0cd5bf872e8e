<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডাইনামিক সীট প্লান ফিচার ডেমো - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .feature-section {
            padding: 30px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-section:last-child {
            border-bottom: none;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .demo-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .demo-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .demo-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .btn-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .single-seat-demo {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
            font-size: 12px;
        }
        
        .regular-seat-demo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px;
            display: inline-block;
            font-size: 12px;
        }
        
        .subject-demo {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 3px;
            display: inline-block;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="mb-0">
                <i class="fas fa-magic me-3"></i>
                ডাইনামিক সীট প্লান সিস্টেম
            </h1>
            <p class="mb-0 mt-3">সম্পূর্ণ ফিচার ডেমো ও গাইড</p>
        </div>

        <!-- Feature 1: Subject-based Filtering -->
        <div class="feature-section">
            <h3><i class="fas fa-book me-2"></i>বিষয় ভিত্তিক ফিল্টারিং</h3>
            <div class="feature-card">
                <h5>নতুন ফিচার: বিষয় কোড অনুযায়ী শিক্ষার্থী নির্বাচন</h5>
                <p>এখন আপনি নির্দিষ্ট বিষয়ের শিক্ষার্থীদের জন্য আলাদা সীট প্লান তৈরি করতে পারবেন।</p>
                
                <div class="feature-highlight">
                    <strong>সাপোর্টেড বিষয়সমূহ:</strong>
                </div>
                
                <div class="demo-grid">
                    <div class="subject-demo">১০১ - বাংলা</div>
                    <div class="subject-demo">১০৭ - ইংরেজি</div>
                    <div class="subject-demo">১৭৪ - পদার্থবিজ্ঞান</div>
                    <div class="subject-demo">১৭৬ - রসায়ন</div>
                    <div class="subject-demo">১৭৮ - জীববিজ্ঞান</div>
                    <div class="subject-demo">২৬৫ - উচ্চতর গণিত</div>
                    <div class="subject-demo">২৫৩ - হিসাববিজ্ঞান</div>
                    <div class="subject-demo">২৯২ - ফিন্যান্স ও ব্যাংকিং</div>
                    <div class="subject-demo">২৭৭ - ব্যবসায় সংগঠন</div>
                    <div class="subject-demo">২৮৬ - উৎপাদন ব্যবস্থাপনা</div>
                    <div class="subject-demo">১০৯ - অর্থনীতি</div>
                    <div class="subject-demo">১২১ - যুক্তিবিদ্যা</div>
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>কিভাবে কাজ করে:</strong> বিষয় কোড নির্বাচন করলে শুধুমাত্র সেই বিষয়ে অংশগ্রহণকারী শিক্ষার্থীরা সীট প্লানে অন্তর্ভুক্ত হবে।
                </div>
            </div>
        </div>

        <!-- Feature 2: Column-wise Bench Configuration -->
        <div class="feature-section">
            <h3><i class="fas fa-columns me-2"></i>কলাম ভিত্তিক বেঞ্চ কনফিগারেশন</h3>
            <div class="feature-card">
                <h5>নতুন ফিচার: প্রতি কলামে আলাদা বেঞ্চ সংখ্যা</h5>
                <p>এখন প্রতি কলামে আলাদা আলাদা সংখ্যক বেঞ্চ রাখতে পারবেন।</p>
                
                <div class="feature-highlight">
                    <strong>উদাহরণ কনফিগারেশন:</strong>
                </div>
                
                <div class="demo-grid">
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-columns"></i></div>
                        <h6>কলাম ১</h6>
                        <p>৫টি বেঞ্চ</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-columns"></i></div>
                        <h6>কলাম ২</h6>
                        <p>৭টি বেঞ্চ</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-columns"></i></div>
                        <h6>কলাম ৩</h6>
                        <p>৪টি বেঞ্চ</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-columns"></i></div>
                        <h6>কলাম ৪</h6>
                        <p>৬টি বেঞ্চ</p>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    <i class="fas fa-calculator me-2"></i>
                    <strong>স্বয়ংক্রিয় গণনা:</strong> সিস্টেম প্রতি রুমের মোট সীট ক্ষমতা স্বয়ংক্রিয়ভাবে গণনা করে।
                </div>
            </div>
        </div>

        <!-- Feature 3: Single Seat Bench -->
        <div class="feature-section">
            <h3><i class="fas fa-user me-2"></i>একক সীট বেঞ্চ কনফিগারেশন</h3>
            <div class="feature-card">
                <h5>নতুন ফিচার: ১ সীটের বেঞ্চ সাপোর্ট</h5>
                <p>কিছু কলামে ১ সীটের বেঞ্চ রাখতে পারবেন যা বিশেষ প্রয়োজনে কাজে আসে।</p>
                
                <div class="feature-highlight">
                    <strong>একক সীট বেঞ্চের সুবিধা:</strong>
                </div>
                
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success me-2"></i>বিশেষ প্রয়োজনের শিক্ষার্থীদের জন্য</li>
                    <li><i class="fas fa-check text-success me-2"></i>চিটিং প্রতিরোধে কার্যকর</li>
                    <li><i class="fas fa-check text-success me-2"></i>রুমের লেআউট অনুযায়ী ফ্লেক্সিবল</li>
                    <li><i class="fas fa-check text-success me-2"></i>ভিজ্যুয়াল আইডেন্টিফিকেশন</li>
                </ul>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>নিয়মিত বেঞ্চ (২ সীট)</h6>
                        <div class="regular-seat-demo">সীট ১</div>
                        <div class="regular-seat-demo">সীট ২</div>
                    </div>
                    <div class="col-md-6">
                        <h6>একক সীট বেঞ্চ (১ সীট)</h6>
                        <div class="single-seat-demo">একক সীট</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feature 4: Seating Patterns -->
        <div class="feature-section">
            <h3><i class="fas fa-th-large me-2"></i>সীটিং প্যাটার্ন</h3>
            <div class="feature-card">
                <h5>৪টি ভিন্ন সীটিং প্যাটার্ন</h5>
                
                <div class="demo-grid">
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-list-ol"></i></div>
                        <h6>সিরিয়াল</h6>
                        <p>ক্রমানুসারে বসানো</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-wave-square"></i></div>
                        <h6>ঝিকঝাক</h6>
                        <p>বাম-দান পরিবর্তন</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-slash"></i></div>
                        <h6>কোনাকোনি</h6>
                        <p>তির্যক বিন্যাস</p>
                    </div>
                    <div class="demo-item">
                        <div class="demo-icon"><i class="fas fa-exchange-alt"></i></div>
                        <h6>বিকল্প</h6>
                        <p>একটি ছেড়ে একটি</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Start Guide -->
        <div class="feature-section">
            <h3><i class="fas fa-rocket me-2"></i>দ্রুত শুরু করার গাইড</h3>
            <div class="feature-card">
                <ol class="list-group list-group-numbered">
                    <li class="list-group-item">বিষয় কোড নির্বাচন করুন (ঐচ্ছিক)</li>
                    <li class="list-group-item">রুম সংখ্যা ও কলাম নির্ধারণ করুন</li>
                    <li class="list-group-item">প্রতি কলামে বেঞ্চ সংখ্যা সেট করুন</li>
                    <li class="list-group-item">একক সীট বেঞ্চ প্রয়োজন হলে সক্রিয় করুন</li>
                    <li class="list-group-item">সীটিং প্যাটার্ন নির্বাচন করুন</li>
                    <li class="list-group-item">"সীট প্লান জেনারেট করুন" বাটনে ক্লিক করুন</li>
                </ol>
                
                <div class="text-center mt-4">
                    <a href="dynamic_seat_plan.php" class="btn-demo">
                        <i class="fas fa-play me-2"></i>
                        এখনই শুরু করুন
                    </a>
                    <a href="test_seat_plan.php" class="btn btn-outline-primary ms-3">
                        <i class="fas fa-vial me-2"></i>
                        টেস্ট ডেটা দেখুন
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
