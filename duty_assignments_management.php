<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
$activeTab = isset($_GET['tab']) ? $_GET['tab'] : 'view';

// Handle form submissions for creating duty date with metadata
if (isset($_POST['create_duty_date'])) {
    try {
        $dutyDate = $_POST['duty_date'];
        $examType = $_POST['exam_type'];
        $examShift = $_POST['exam_shift'];
        $examSubject = $_POST['exam_subject'] ?? '';
        $examCode = $_POST['exam_code'] ?? '';
        $notes = $_POST['notes'] ?? '';
        
        $teacherManager->saveDutyDateMetadata($dutyDate, $examType, $examShift, $examSubject, $examCode, $notes);
        
        $message = "ডিউটি তারিখ সফলভাবে সংরক্ষণ করা হয়েছে!";
        $messageType = "success";
        $activeTab = 'create';
    } catch (Exception $e) {
        $message = "ত্রুটি: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Handle teacher multi-duty assignment
if (isset($_POST['assign_teacher_duties'])) {
    try {
        $teacherId = $_POST['teacher_id'];
        $dutyDates = isset($_POST['duty_dates']) ? $_POST['duty_dates'] : [];
        $roomNumber = $_POST['room_number'] ?? '';
        $dutyShift = $_POST['duty_shift'] ?? 'Morning';
        
        if (empty($teacherId)) {
            throw new Exception("অনুগ্রহ করে একজন শিক্ষক নির্বাচন করুন!");
        }
        
        if (empty($dutyDates)) {
            throw new Exception("অনুগ্রহ করে কমপক্ষে একটি তারিখ নির্বাচন করুন!");
        }
        
        // Update database structure once to support shift-specific duties
        $teacherManager->updateDutyAssignmentsTableStructure();
        
        $successCount = 0;
        foreach ($dutyDates as $dutyDate) {
            // Check if teacher already has duty on this date and shift
            $exists = $teacherManager->checkTeacherDutyExists($teacherId, $dutyDate, $dutyShift);
            
            if (!$exists) {
                $teacherManager->assignTeacherToDuty($teacherId, $dutyDate, $roomNumber, $dutyShift);
                $successCount++;
            }
        }
        
        $message = "$successCount টি তারিখে শিক্ষকের ডিউটি সফলভাবে যোগ করা হয়েছে!";
        $messageType = "success";
        $activeTab = 'teacher-duty';
    } catch (Exception $e) {
        $message = "ত্রুটি: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Handle update of duty date metadata
if (isset($_POST['update_duty_date'])) {
    try {
        $dutyDate = $_POST['edit_duty_date'];
        $examType = $_POST['edit_exam_type'];
        $examShift = $_POST['edit_exam_shift'];
        $examSubject = $_POST['edit_exam_subject'] ?? '';
        $examCode = $_POST['edit_exam_code'] ?? '';
        $notes = $_POST['edit_notes'] ?? '';
        $teacherManager->saveDutyDateMetadata($dutyDate, $examType, $examShift, $examSubject, $examCode, $notes);
        $message = "ডিউটি তারিখ সফলভাবে আপডেট করা হয়েছে!";
        $messageType = "success";
        $activeTab = 'view';
    } catch (Exception $e) {
        $message = "ত্রুটি: " . $e->getMessage();
        $messageType = "danger";
    }
}

// Get all duty dates for view tab
$dutyDates = $teacherManager->getAllDutyDates();

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}

// Get all teachers for bulk update tab
$teachers = $teacherManager->getAllTeachers();

if (isset($_POST['confirm_delete_duty_date']) && !empty($_POST['delete_duty_date'])) {
    $deleteDate = $_POST['delete_duty_date'];
    try {
        // First delete from duty_dates table
        $sqlDutyDates = "DELETE FROM duty_dates WHERE duty_date = ?";
        $stmtDutyDates = $pdo->prepare($sqlDutyDates);
        $stmtDutyDates->execute([$deleteDate]);
        
        // Then delete from duty_assignments table
        $sqlDutyAssignments = "DELETE FROM duty_assignments WHERE duty_date = ?";
        $stmtDutyAssignments = $pdo->prepare($sqlDutyAssignments);
        $stmtDutyAssignments->execute([$deleteDate]);
        
        echo '<div class="alert alert-success mt-3">'
            . formatDateBengali($deleteDate) . ' তারিখের সকল ডিউটি সফলভাবে ডিলিট হয়েছে!'
            . '</div>';
        // Remove from $dutyDates for UI update
        $dutyDates = array_filter($dutyDates, function($d) use ($deleteDate) { return $d !== $deleteDate; });
    } catch (Exception $e) {
        echo '<div class="alert alert-danger mt-3">ত্রুটি: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি বন্টন ব্যবস্থাপনা - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
            border: none;
            border-bottom: 3px solid transparent;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
        }
        
        .nav-tabs .nav-link.active {
            color: #764ba2;
            border-bottom-color: #764ba2;
            background: transparent;
        }
        
        .nav-tabs .nav-link:hover:not(.active) {
            border-bottom-color: rgba(118, 75, 162, 0.3);
        }
        
        .tab-content {
            padding-top: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .duty-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .duty-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            background-color: white;
        }
        
        .teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transform: translateY(-2px);
        }
        
        .duty-date-checkbox {
            margin-bottom: 10px;
        }
        
        .selected-teacher {
            background-color: #f0f4ff;
            border-color: #667eea;
        }
        
        #dutyDatesList {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-tasks me-3"></i>ডিউটি বন্টন ব্যবস্থাপনা
                </h1>
                <p class="text-white-50">সব ডিউটি বন্টন একত্রে ব্যবস্থাপনা করুন</p>
                
                <!-- Quick Actions -->
                <div class="mt-3">
                    <a href="room_distribution.php" class="btn btn-light btn-lg">
                        <i class="fas fa-door-open me-2"></i>তারিখ ভিত্তিক রুম বন্টন
                    </a>
                    <a href="date_wise_duty_assignment.php" class="btn btn-outline-light ms-2">
                        <i class="fas fa-calendar-plus me-2"></i>তারিখ ভিত্তিক ডিউটি বন্টন
                    </a>
                </div>
            </div>
            
            <!-- Message Display -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <strong><?php echo $message; ?></strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Tabs Navigation -->
            <div class="main-card">
                <ul class="nav nav-tabs" id="dutyTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $activeTab == 'view' ? 'active' : ''; ?>" 
                           href="?tab=view" role="tab">
                            <i class="fas fa-list me-2"></i>সব দেখুন
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $activeTab == 'create' ? 'active' : ''; ?>" 
                           href="?tab=create" role="tab">
                            <i class="fas fa-plus me-2"></i>নতুন তৈরি
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $activeTab == 'room-duty' ? 'active' : ''; ?>" 
                           href="?tab=room-duty" role="tab">
                            <i class="fas fa-door-open me-2"></i>রুম ডিউটি
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $activeTab == 'teacher-duty' ? 'active' : ''; ?>" 
                           href="?tab=teacher-duty" role="tab">
                            <i class="fas fa-user-clock me-2"></i>শিক্ষক ডিউটি
                        </a>
                    </li>
                    <li class="nav-item" role="presentation">
                        <a class="nav-link <?php echo $activeTab == 'bulk-update' ? 'active' : ''; ?>" 
                           href="?tab=bulk-update" role="tab">
                            <i class="fas fa-edit me-2"></i>একত্রে আপডেট
                        </a>
                    </li>
                </ul>
                
                <div class="tab-content" id="dutyTabsContent">
                    <!-- View Tab -->
                    <?php if ($activeTab == 'view'): ?>
                        <div class="tab-pane fade show active">
                            <h4 class="mb-4">
                                <i class="fas fa-calendar-check text-primary me-2"></i>সব ডিউটি তারিখ
                            </h4>
                            
                            <?php if (empty($dutyDates)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    কোন ডিউটি বন্টন পাওয়া যায়নি। নতুন ডিউটি বন্টন করতে "নতুন তৈরি" ট্যাবে যান।
                                </div>
                            <?php else: ?>
                                <div class="mb-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <a href="date_wise_duty_assignment.php" class="btn btn-outline-primary">
                                                <i class="fas fa-calendar-plus me-2"></i>তারিখ ভিত্তিক ডিউটি বন্টন
                                            </a>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <a href="room_distribution.php" class="btn btn-outline-success">
                                                <i class="fas fa-door-open me-2"></i>তারিখ ভিত্তিক রুম বন্টন
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th scope="col">#</th>
                                                <th scope="col">তারিখ</th>
                                                <th scope="col">পরীক্ষার ধরন</th>
                                                <th scope="col">শিফট</th>
                                                <th scope="col">বিষয়</th>
                                                <th scope="col">শিক্ষক সংখ্যা</th>
                                                <th scope="col">অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $i = 1; foreach ($dutyDates as $date): ?>
                                                <?php 
                                                $assignments = $teacherManager->getDutyAssignments($date);
                                                $dateDetails = $teacherManager->getDutyDateDetails($date);
                                                ?>
                                                <tr>
                                                    <td><?php echo $i++; ?></td>
                                                    <td><?php echo formatDateBengali($date); ?></td>
                                                    <td>
                                                        <?php if (!empty($dateDetails)): ?>
                                                            <?php echo $dateDetails['exam_type'] == 'theory' ? 'তত্ত্বীয়' : 'ব্যবহারিক'; ?>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">সেট করা হয়নি</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($dateDetails)): ?>
                                                            <?php 
                                                            if ($dateDetails['exam_shift'] == 'Morning') echo 'সকাল';
                                                            elseif ($dateDetails['exam_shift'] == 'Afternoon') echo 'বিকাল';
                                                            else echo 'উভয়';
                                                            ?>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">সেট করা হয়নি</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($dateDetails) && !empty($dateDetails['exam_subject'])): ?>
                                                            <?php echo htmlspecialchars($dateDetails['exam_subject']); ?>
                                                        <?php else: ?>
                                                            <span class="badge bg-secondary">সেট করা হয়নি</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary"><?php echo count($assignments); ?> জন</span>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="date_wise_duty.php?date=<?php echo urlencode($date); ?>" 
                                                               class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="date_wise_duty_assignment.php?date=<?php echo urlencode($date); ?>" 
                                                               class="btn btn-sm btn-primary">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <div class="btn-group">
                                                                <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="fas fa-print"></i>
                                                                </button>
                                                                <ul class="dropdown-menu">
                                                                    <li>
                                                                        <a class="dropdown-item" href="print_duty_list.php?date=<?php echo urlencode($date); ?>" target="_blank">
                                                                            <i class="fas fa-table me-2"></i>তালিকা আকারে
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a class="dropdown-item" href="print_room_duty_image.php?date=<?php echo urlencode($date); ?>" target="_blank">
                                                                            <i class="fas fa-id-card me-2"></i>ছবির আকারে
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                            <a href="#" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editDutyDateModal_<?php echo $date; ?>">
                                                                <i class="fas fa-pen"></i>
                                                            </a>
                                                            <a href="#" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteDutyDateModal_<?php echo $date; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <div class="modal fade" id="editDutyDateModal_<?php echo $date; ?>" tabindex="-1" aria-labelledby="editDutyDateModalLabel_<?php echo $date; ?>" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <form method="POST">
                                                                <div class="modal-header bg-warning">
                                                                    <h5 class="modal-title" id="editDutyDateModalLabel_<?php echo $date; ?>">
                                                                        <i class="fas fa-pen me-2"></i>ডিউটি তারিখ সম্পাদনা করুন
                                                                    </h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <input type="hidden" name="edit_duty_date" value="<?php echo $date; ?>">
                                                                    <div class="mb-3">
                                                                        <label class="form-label">পরীক্ষার ধরন</label>
                                                                        <select name="edit_exam_type" class="form-select" required>
                                                                            <option value="theory" <?php if(($dateDetails['exam_type'] ?? '')=='theory') echo 'selected'; ?>>তত্ত্বীয়</option>
                                                                            <option value="practical" <?php if(($dateDetails['exam_type'] ?? '')=='practical') echo 'selected'; ?>>ব্যবহারিক</option>
                                                                        </select>
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">শিফট</label>
                                                                        <select name="edit_exam_shift" class="form-select" required>
                                                                            <option value="Morning" <?php if(($dateDetails['exam_shift'] ?? '')=='Morning') echo 'selected'; ?>>সকাল</option>
                                                                            <option value="Afternoon" <?php if(($dateDetails['exam_shift'] ?? '')=='Afternoon') echo 'selected'; ?>>বিকাল</option>
                                                                            <option value="Both" <?php if(($dateDetails['exam_shift'] ?? '')=='Both') echo 'selected'; ?>>উভয়</option>
                                                                        </select>
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">বিষয়</label>
                                                                        <input type="text" name="edit_exam_subject" class="form-control" value="<?php echo htmlspecialchars($dateDetails['exam_subject'] ?? ''); ?>">
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">বিষয় কোড</label>
                                                                        <input type="text" name="edit_exam_code" class="form-control" value="<?php echo htmlspecialchars($dateDetails['exam_code'] ?? ''); ?>">
                                                                    </div>
                                                                    <div class="mb-3">
                                                                        <label class="form-label">নোট</label>
                                                                        <textarea name="edit_notes" class="form-control"><?php echo htmlspecialchars($dateDetails['notes'] ?? ''); ?></textarea>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                                    <button type="submit" name="update_duty_date" class="btn btn-warning">
                                                                        <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal fade" id="deleteDutyDateModal_<?php echo $date; ?>" tabindex="-1" aria-labelledby="deleteDutyDateModalLabel_<?php echo $date; ?>" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <form method="POST">
                                                                <div class="modal-header bg-danger text-white">
                                                                    <h5 class="modal-title" id="deleteDutyDateModalLabel_<?php echo $date; ?>">
                                                                        <i class="fas fa-trash me-2"></i>ডিউটি ডিলিট নিশ্চিত করুন
                                                                    </h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <input type="hidden" name="delete_duty_date" value="<?php echo $date; ?>">
                                                                    <p>আপনি কি নিশ্চিতভাবে <strong><?php echo formatDateBengali($date); ?></strong> তারিখের সকল ডিউটি ডিলিট করতে চান?</p>
                                                                    <p class="text-danger">এই ডিউটি ডিলিট করলে সংশ্লিষ্ট সকল শিক্ষক বরাদ্দও মুছে যাবে।</p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                                                                    <button type="submit" name="confirm_delete_duty_date" class="btn btn-danger">
                                                                        <i class="fas fa-trash me-2"></i>ডিলিট করুন
                                                                    </button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    
                    <!-- Create Tab -->
                    <?php elseif ($activeTab == 'create'): ?>
                        <div class="tab-pane fade show active">
                            <h4 class="mb-4">
                                <i class="fas fa-plus-circle text-success me-2"></i>নতুন ডিউটি তারিখ তৈরি করুন
                            </h4>
                            
                            <div class="form-section">
                                <form method="POST">
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="duty_date" class="form-label">তারিখ</label>
                                            <input type="date" class="form-control" id="duty_date" name="duty_date" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="exam_type" class="form-label">পরীক্ষার ধরন</label>
                                            <select class="form-select" id="exam_type" name="exam_type" required>
                                                <option value="theory">তত্ত্বীয়</option>
                                                <option value="practical">ব্যবহারিক</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="exam_shift" class="form-label">শিফট</label>
                                            <select class="form-select" id="exam_shift" name="exam_shift" required>
                                                <option value="Morning">সকাল</option>
                                                <option value="Afternoon">বিকাল</option>
                                                <option value="Both">উভয়</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="exam_subject" class="form-label">বিষয় (ঐচ্ছিক)</label>
                                            <input type="text" class="form-control" id="exam_subject" name="exam_subject">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="exam_code" class="form-label">কোড (ঐচ্ছিক)</label>
                                            <input type="text" class="form-control" id="exam_code" name="exam_code">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">নোট (ঐচ্ছিক)</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" name="create_duty_date" class="btn btn-custom">
                                            <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                তারিখ তৈরি করার পর, শিক্ষক নিয়োগ করতে "রুম ডিউটি" ট্যাবে যান।
                            </div>
                        </div>
                    
                    <!-- Room Duty Tab -->
                    <?php elseif ($activeTab == 'room-duty'): ?>
                        <div class="tab-pane fade show active">
                            <h4 class="mb-4">
                                <i class="fas fa-door-open text-warning me-2"></i>রুম ডিউটি প্রিন্ট
                            </h4>
                            
                            <div class="form-section">
                                <form action="print_room_duty.php" method="GET" target="_blank">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="print_date" class="form-label">তারিখ নির্বাচন করুন</label>
                                            <select class="form-select" id="print_date" name="date" required>
                                                <option value="">তারিখ নির্বাচন করুন</option>
                                                <?php foreach ($dutyDates as $date): ?>
                                                    <option value="<?php echo $date; ?>">
                                                        <?php echo formatDateBengali($date); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="print_shift" class="form-label">শিফট নির্বাচন করুন</label>
                                            <select class="form-select" id="print_shift" name="shift">
                                                <option value="all">সব শিফট</option>
                                                <option value="Morning">সকাল</option>
                                                <option value="Afternoon">বিকাল</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <label class="form-label">ফরম্যাট নির্বাচন করুন</label>
                                            <div class="d-flex gap-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="format" id="format_table" value="table" checked>
                                                    <label class="form-check-label" for="format_table">
                                                        <i class="fas fa-table me-1"></i> তালিকা আকারে
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="format" id="format_image" value="image">
                                                    <label class="form-check-label" for="format_image">
                                                        <i class="fas fa-id-card me-1"></i> ছবির আকারে
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-custom" id="printBtn">
                                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <div class="form-section mt-4">
                                <h5 class="mb-3">
                                    <i class="fas fa-tasks text-primary me-2"></i>রুম বন্টন ব্যবস্থাপনা
                                </h5>
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <p>
                                            <i class="fas fa-info-circle me-2"></i>
                                            একটি তারিখ নির্বাচন করে সবগুলো শিক্ষকের জন্য রুম বন্টন করুন
                                        </p>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <a href="room_distribution.php" class="btn btn-custom">
                                            <i class="fas fa-door-open me-2"></i>রুম বন্টন করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    <!-- Teacher Duty Tab (New) -->
                    <?php elseif ($activeTab == 'teacher-duty'): ?>
                        <div class="tab-pane fade show active">
                            <h4 class="mb-4">
                                <i class="fas fa-user-clock text-primary me-2"></i>শিক্ষক ভিত্তিক একাধিক ডিউটি বরাদ্দ
                            </h4>
                            
                            <div class="form-section">
                                <form method="POST">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <h5 class="mb-3">১. শিক্ষক নির্বাচন করুন</h5>
                                            <div class="mb-3">
                                                <input type="text" class="form-control" id="teacherSearch" 
                                                       placeholder="শিক্ষকের নাম, বিষয় বা পদবী দিয়ে অনুসন্ধান করুন..." 
                                                       onkeyup="filterTeachers()">
                                            </div>
                                            
                                            <div id="teachersList" style="max-height: 300px; overflow-y: auto;">
                                                <?php foreach ($teachers as $teacher): ?>
                                                    <?php if ($teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়'): ?>
                                                        <div class="teacher-card" onclick="selectTeacher(<?php echo $teacher['id']; ?>, '<?php echo htmlspecialchars($teacher['name']); ?>')">
                                                            <div class="row">
                                                                <div class="col-md-1">
                                                                    <input type="radio" name="teacher_id" value="<?php echo $teacher['id']; ?>" class="teacher-radio">
                                                                </div>
                                                                <div class="col-md-11">
                                                                    <div class="fw-bold"><?php echo htmlspecialchars($teacher['name']); ?></div>
                                                                    <div class="small text-muted">
                                                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($teacher['sl_number']); ?></span>
                                                                        <?php echo htmlspecialchars($teacher['subject']); ?> | 
                                                                        <?php echo htmlspecialchars($teacher['designation']); ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                            
                                            <div class="mt-4">
                                                <h5 class="mb-3">২. রুম নম্বর (ঐচ্ছিক)</h5>
                                                <input type="text" class="form-control" name="room_number" placeholder="রুম নম্বর">
                                            </div>
                                            
                                            <div class="mt-4">
                                                <h5 class="mb-3">৩. শিফট নির্বাচন করুন</h5>
                                                <select class="form-select" name="duty_shift">
                                                    <option value="Morning">সকাল</option>
                                                    <option value="Afternoon">বিকাল</option>
                                                    <option value="Both">উভয় শিফট</option>
                                                </select>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-4">
                                            <h5 class="mb-3">৪. তারিখ নির্বাচন করুন</h5>
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i>
                                                একাধিক তারিখ নির্বাচন করতে পারেন
                                            </div>
                                            
                                            <div id="dutyDatesList">
                                                <?php foreach ($dutyDates as $date): ?>
                                                    <div class="duty-date-checkbox">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="duty_dates[]" value="<?php echo $date; ?>" id="date_<?php echo $date; ?>">
                                                            <label class="form-check-label" for="date_<?php echo $date; ?>">
                                                                <?php echo formatDateBengali($date); ?>
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                            
                                            <div class="mt-4">
                                                <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllDates()">সব নির্বাচন করুন</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="deselectAllDates()">সব বাদ দিন</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mt-4">
                                        <div id="selectedTeacherInfo" class="mb-3" style="display: none;">
                                            <span class="badge bg-primary fs-6 p-2">নির্বাচিত শিক্ষক: <span id="selectedTeacherName"></span></span>
                                        </div>
                                        
                                        <button type="submit" name="assign_teacher_duties" class="btn btn-lg btn-custom">
                                            <i class="fas fa-save me-2"></i>ডিউটি বরাদ্দ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    
                    <!-- Bulk Update Tab -->
                    <?php elseif ($activeTab == 'bulk-update'): ?>
                        <div class="tab-pane fade show active">
                            <h4 class="mb-4">
                                <i class="fas fa-users-cog text-info me-2"></i>একত্রে শিক্ষক ডিউটি আপডেট
                            </h4>
                            
                            <div class="form-section">
                                <form method="POST" action="update_duty_assignments.php">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="bulk_date" class="form-label">তারিখ নির্বাচন করুন</label>
                                            <select class="form-select" id="bulk_date" name="duty_date" required>
                                                <option value="">তারিখ নির্বাচন করুন</option>
                                                <?php foreach ($dutyDates as $date): ?>
                                                    <option value="<?php echo $date; ?>">
                                                        <?php echo formatDateBengali($date); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="bulk_action" class="form-label">অ্যাকশন নির্বাচন করুন</label>
                                            <select class="form-select" id="bulk_action" name="bulk_action" required>
                                                <option value="add">শিক্ষক যুক্ত করুন</option>
                                                <option value="remove">শিক্ষক বাদ দিন</option>
                                                <option value="update_room">রুম আপডেট করুন</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive mt-4">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th scope="col" width="50">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="select-all">
                                                        </div>
                                                    </th>
                                                    <th scope="col">নাম</th>
                                                    <th scope="col">বিষয়</th>
                                                    <th scope="col">পদবি</th>
                                                    <th scope="col">রুম নং</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($teachers as $teacher): ?>
                                                    <?php if ($teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়'): ?>
                                                        <tr>
                                                            <td>
                                                                <div class="form-check">
                                                                    <input class="form-check-input teacher-checkbox" 
                                                                           type="checkbox" 
                                                                           name="selected_teachers[]" 
                                                                           value="<?php echo $teacher['id']; ?>">
                                                                </div>
                                                            </td>
                                                            <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                                            <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                                            <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                                            <td>
                                                                <input type="text" 
                                                                       name="room_assignments[<?php echo $teacher['id']; ?>]" 
                                                                       class="form-control form-control-sm" 
                                                                       placeholder="রুম নং">
                                                            </td>
                                                        </tr>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="text-center mt-4">
                                        <button type="submit" name="update_duties" class="btn btn-custom">
                                            <i class="fas fa-save me-2"></i>আপডেট করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Select all checkbox functionality
        document.getElementById('select-all')?.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.teacher-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Teacher duty tab functions
        function filterTeachers() {
            const searchText = document.getElementById('teacherSearch').value.toLowerCase();
            const teacherCards = document.querySelectorAll('.teacher-card');
            
            teacherCards.forEach(card => {
                const cardText = card.textContent.toLowerCase();
                if (cardText.includes(searchText)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        }
        
        function selectTeacher(teacherId, teacherName) {
            // Reset all cards
            document.querySelectorAll('.teacher-card').forEach(card => {
                card.classList.remove('selected-teacher');
            });
            
            // Select the clicked card
            const selectedCard = event.currentTarget;
            selectedCard.classList.add('selected-teacher');
            
            // Select the radio button
            const radio = selectedCard.querySelector('.teacher-radio');
            radio.checked = true;
            
            // Show selected teacher info
            document.getElementById('selectedTeacherInfo').style.display = 'block';
            document.getElementById('selectedTeacherName').textContent = teacherName;
        }
        
        function selectAllDates() {
            const dateCheckboxes = document.querySelectorAll('input[name="duty_dates[]"]');
            dateCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }
        
        function deselectAllDates() {
            const dateCheckboxes = document.querySelectorAll('input[name="duty_dates[]"]');
            dateCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }
        
        // Print format handling for room duty tab
        document.addEventListener('DOMContentLoaded', function() {
            const printForm = document.querySelector('form[action="print_room_duty.php"]');
            const printBtn = document.getElementById('printBtn');
            
            if (printForm && printBtn) {
                const formatImageRadio = document.getElementById('format_image');
                
                printForm.addEventListener('submit', function(e) {
                    if (formatImageRadio && formatImageRadio.checked) {
                        e.preventDefault();
                        const date = document.getElementById('print_date').value;
                        const shift = document.getElementById('print_shift').value;
                        
                        if (!date) {
                            alert('অনুগ্রহ করে একটি তারিখ নির্বাচন করুন!');
                            return;
                        }
                        
                        // Redirect to image format print page
                        const url = `print_room_duty_image.php?date=${encodeURIComponent(date)}&shift=${encodeURIComponent(shift)}`;
                        window.open(url, '_blank');
                    }
                });
            }
        });
    </script>
</body>
</html>