<?php
// Database Connection Troubleshooting

// Display all PHP errors
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo '<h2>ডাটাবেজ কানেকশন ট্রাবলশুটিং</h2>';

// Check if XAMPP services are running
echo '<h3>১. XAMPP সার্ভিস চেক:</h3>';
echo '<p>প্রথমে নিশ্চিত করুন XAMPP কন্ট্রোল প্যানেল থেকে MySQL সার্ভিস চালু আছে কিনা।</p>';
echo '<div style="background-color:#f8f9fa; padding:10px; border-left:4px solid #007bff;">';
echo '<strong>করণীয়:</strong> XAMPP কন্ট্রোল প্যানেল ওপেন করুন > MySQL এর পাশে "Start" বাটনে ক্লিক করুন</div>';
echo '<p>অথবা, নিম্নলিখিত কমান্ড রান করুন:</p>';
echo '<code>net start mysql</code> (Windows)<br>';
echo '<code>sudo service mysql start</code> (Linux)<br><br>';

// Read database config
echo '<h3>২. ডাটাবেজ কনফিগ চেক:</h3>';

$config_files = [
    'config/database.php',
    'db_connect.php'
];

$config_found = false;
$host = 'localhost';
$port = '3306';

foreach ($config_files as $config_file) {
    if (file_exists($config_file)) {
        echo "<p>কনফিগ ফাইল পাওয়া গেছে: $config_file</p>";
        $config_found = true;
        
        // Try to extract config info
        $file_content = file_get_contents($config_file);
        
        if (preg_match('/[\'"]host[\'"]\s*=>\s*[\'"]([^\'"]+)[\'"]/i', $file_content, $matches)) {
            $host = $matches[1];
        } elseif (preg_match('/\$host\s*=\s*[\'"]([^\'"]+)[\'"]/i', $file_content, $matches)) {
            $host = $matches[1];
        }
        
        if (preg_match('/[\'"]port[\'"]\s*=>\s*[\'"]?(\d+)[\'"]?/i', $file_content, $matches)) {
            $port = $matches[1];
        } elseif (preg_match('/\$port\s*=\s*[\'"]?(\d+)[\'"]?/i', $file_content, $matches)) {
            $port = $matches[1];
        }
        
        echo "<p>হোস্ট: <code>$host</code>, পোর্ট: <code>$port</code></p>";
        break;
    }
}

if (!$config_found) {
    echo "<p style='color:red;'>কোন ডাটাবেজ কনফিগ ফাইল পাওয়া যায়নি!</p>";
}

// Test connection
echo '<h3>৩. ডাটাবেজ কানেকশন টেস্ট:</h3>';

function testConnection($host, $port, $user = 'root', $pass = '') {
    try {
        $start_time = microtime(true);
        $conn = new PDO("mysql:host=$host;port=$port", $user, $pass);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $end_time = microtime(true);
        
        echo "<p style='color:green;'><b>✓ কানেকশন সফল!</b> (" . number_format(($end_time - $start_time) * 1000, 2) . " ms)</p>";
        
        // Get MySQL version
        $stmt = $conn->query('SELECT VERSION() as version');
        $version = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
        echo "<p>MySQL ভার্শন: <code>$version</code></p>";
        
        return true;
    } catch (PDOException $e) {
        echo "<p style='color:red;'><b>✗ কানেকশন ব্যর্থ:</b> " . $e->getMessage() . "</p>";
        return false;
    }
}

// Test default connection
echo "<h4>ডিফল্ট কানেকশন (root@$host:$port):</h4>";
$success = testConnection($host, $port);

// If localhost failed, try 127.0.0.1
if (!$success && $host === 'localhost') {
    echo "<h4>অল্টারনেটিভ IP এড্রেস চেক (root@127.0.0.1:$port):</h4>";
    $success = testConnection('127.0.0.1', $port);
}

// Common solutions
echo '<h3>৪. সমাধান:</h3>';
echo '<ol>';
echo '<li><strong>XAMPP ক্লোজ করে আবার স্টার্ট করুন</strong> - কখনো কখনো সার্ভিস রিস্টার্ট করলে সমস্যা দূর হয়</li>';
echo '<li><strong>পোর্ট চেক করুন</strong> - পোর্ট <code>3306</code> ডিফল্ট, নিশ্চিত করুন যে এটি অন্য অ্যাপ্লিকেশন ব্যবহার করছে না</li>';
echo '<li><strong>কনফিগ ফাইল চেক করুন</strong> - <code>config/database.php</code> বা <code>db_connect.php</code> ফাইলে সঠিক কানেকশন তথ্য আছে কিনা</li>';
echo '<li><strong>ফায়ারওয়াল চেক করুন</strong> - ফায়ারওয়াল MySQL পোর্ট ব্লক করছে কিনা</li>';
echo '<li><strong>xampp_auto_start.bat ফাইল রান করুন</strong> - আপনার প্রজেক্টে আছে XAMPP অটো স্টার্ট স্ক্রিপ্ট</li>';
echo '</ol>';

// Auto-fix script
echo '<h3>৫. অটো-ফিক্স:</h3>';
?>

<div style="margin-bottom:20px;">
    <button onclick="runAutoFix()" style="background:#007bff; color:white; border:none; padding:10px 15px; border-radius:4px; cursor:pointer;">
        অটো-ফিক্স রান করুন
    </button>
</div>

<script>
function runAutoFix() {
    if (confirm('আপনি কি নিশ্চিত যে আপনি অটো-ফিক্স চালাতে চান? এটি XAMPP সার্ভিস রিস্টার্ট করার চেষ্টা করবে।')) {
        window.location.href = 'auto_startup_fix.bat';
        setTimeout(function() {
            alert('অটো-ফিক্স স্ক্রিপ্ট চালানো হয়েছে। কয়েক মিনিট অপেক্ষা করে পেজ রিফ্রেশ করুন।');
            setTimeout(function() {
                window.location.reload();
            }, 5000);
        }, 1000);
    }
}
</script>

<?php
// Back link
echo '<p><a href="index.php" style="text-decoration:none;">← মূল পেজে ফিরে যান</a></p>';
?> 