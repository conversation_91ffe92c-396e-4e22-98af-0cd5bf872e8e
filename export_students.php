<?php
// Check if vendor/autoload.php exists
if (!file_exists(__DIR__ . '/vendor/autoload.php')) {
    die('Composer dependencies not installed. Please run "composer install" first.');
}

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/models/Student.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

try {
    // Get all students
    $student = new Student();
    $students = $student->getAll();
    
    if (empty($students)) {
        die('No students found to export.');
    }
    
    // Create new spreadsheet
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set headers
    $headers = [
        'ID', 'C.Code', 'EIIN', 'Roll No.', 'Reg. No.', 'Session', 'Type', 'Group',
        'Student Name', 'Father Name', 'Gender',
        'Sub 1', 'Sub 2', 'Sub 3', 'Sub 4', 'Sub 5', 'Sub 6', 'Sub 7',
        'Sub 8', 'Sub 9', 'Sub 10', 'Sub 11', 'Sub 12', 'Sub 13', 'Created At'
    ];
    
    // Set header row
    $column = 'A';
    foreach ($headers as $header) {
        $sheet->setCellValue($column . '1', $header);
        $column++;
    }
    
    // Style the header row
    $headerStyle = [
        'font' => [
            'bold' => true,
            'color' => ['rgb' => 'FFFFFF']
        ],
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => ['rgb' => '4472C4']
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                'color' => ['rgb' => '000000']
            ]
        ]
    ];
    
    $sheet->getStyle('A1:Y1')->applyFromArray($headerStyle);
    
    // Add student data
    $row = 2;
    foreach ($students as $studentData) {
        // Handle both new and old subject formats
        $subjects = [];
        if (!empty($studentData['subjects'])) {
            $subjectArray = explode(',', $studentData['subjects']);
            $subjectArray = array_map('trim', $subjectArray);
            $subjects = array_pad($subjectArray, 13, ''); // Pad to 13 elements
        } else {
            // Check if old format columns exist
            $hasOldColumns = isset($studentData['sub_1']);

            if ($hasOldColumns) {
                // Use old format
                for ($i = 1; $i <= 13; $i++) {
                    $subjects[] = $studentData["sub_$i"] ?? '';
                }
            } else {
                // No subject data available, fill with empty strings
                $subjects = array_fill(0, 13, '');
            }
        }

        $data = [
            $studentData['id'] ?? '',
            $studentData['c_code'] ?? '',
            $studentData['eiin'] ?? '',
            $studentData['roll_no'] ?? $studentData['roll'] ?? '',
            $studentData['reg_no'] ?? $studentData['registration'] ?? '',
            $studentData['session'] ?? $studentData['academic_year'] ?? '',
            $studentData['type'] ?? $studentData['student_type'] ?? '',
            $studentData['group_name'] ?? $studentData['department'] ?? '',
            $studentData['student_name'] ?? $studentData['name'] ?? '',
            $studentData['father_name'] ?? '',
            $studentData['gender'] ?? '',
            $subjects[0], $subjects[1], $subjects[2], $subjects[3], $subjects[4],
            $subjects[5], $subjects[6], $subjects[7], $subjects[8], $subjects[9],
            $subjects[10], $subjects[11], $subjects[12],
            $studentData['created_at'] ?? ''
        ];
        
        $column = 'A';
        foreach ($data as $value) {
            $sheet->setCellValue($column . $row, $value);
            $column++;
        }
        $row++;
    }
    
    // Auto-size columns
    foreach (range('A', 'Y') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Set filename
    $filename = 'EXMM_Students_Export_' . date('Y-m-d_H-i-s') . '.xlsx';
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    
    // Create writer and output
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    
} catch (Exception $e) {
    // If there's an error, show a simple error page
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Export Error - EXMM</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    </head>
    <body>
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-6">
                    <div class="alert alert-danger">
                        <h4>Export Error</h4>
                        <p>Unable to export students: <?php echo htmlspecialchars($e->getMessage()); ?></p>
                        <p>Please make sure Composer dependencies are installed by running:</p>
                        <code>composer install</code>
                    </div>
                    <a href="view_students.php" class="btn btn-primary">Back to Students</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
