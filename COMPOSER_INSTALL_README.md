# EXMM Composer Auto Installation Guide

This guide provides multiple ways to automatically install Composer and dependencies for Excel upload functionality.

## 🚀 Quick Installation Options

### Option 1: Web-based Auto Installer (Recommended)
1. Open your browser
2. Go to: `http://localhost/exmm/auto_install_composer.php`
3. Click "Auto Install" button
4. Wait for installation to complete

### Option 2: Windows Batch Script
1. Download: `install_composer.bat`
2. Double-click the file to run
3. Follow the on-screen instructions

### Option 3: PowerShell Script (Advanced)
1. Download: `install_composer.ps1`
2. Right-click → "Run with PowerShell"
3. Or open PowerShell and run: `.\install_composer.ps1`

### Option 4: Manual Command Line
```bash
# Download Composer
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php composer-setup.php --install-dir=. --filename=composer.phar
php -r "unlink('composer-setup.php');"

# Install dependencies
php composer.phar install --no-dev --optimize-autoloader
```

## 📋 Prerequisites

- **PHP 7.4 or higher** must be installed
- **Internet connection** for downloading packages
- **Write permissions** in the project directory

## 🔧 What Gets Installed

- **Composer**: PHP package manager
- **PhpSpreadsheet**: Library for reading/writing Excel files
- **Dependencies**: Required supporting libraries

## ✅ Verification

After installation, check:
1. `vendor/` directory exists
2. `vendor/autoload.php` file exists
3. Excel upload works at: `http://localhost/exmm/upload.php`

## 🐛 Troubleshooting

### "PHP command not found"
- Install PHP from: https://www.php.net/downloads
- Add PHP to your system PATH

### "Permission denied"
- Run as Administrator (Windows)
- Use `sudo` on Linux/Mac
- Check folder permissions

### "Composer installation failed"
- Check internet connection
- Try manual installation
- Use alternative CSV upload (no Composer required)

### "Dependencies installation failed"
- Delete `vendor/` folder and try again
- Check available disk space
- Try: `composer install --no-scripts`

## 🔄 Alternative: CSV Upload

If Composer installation fails, you can still upload student data using CSV files:

1. Go to: `http://localhost/exmm/upload.php`
2. Download sample CSV template
3. Fill in your student data
4. Upload the CSV file

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Try the web-based auto installer
3. Use CSV upload as fallback
4. Contact system administrator

## 🎯 Next Steps

After successful installation:
1. Go to: `http://localhost/exmm/upload.php`
2. Upload your Excel (.xlsx, .xls) or CSV files
3. View uploaded students at: `http://localhost/exmm/view_students.php`

---

**Note**: This installation only needs to be done once. After successful installation, Excel upload functionality will be permanently available.
