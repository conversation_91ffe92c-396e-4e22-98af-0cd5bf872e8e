<?php
require_once 'includes/teacher_db.php';

// Get teacher ID if specified (for single teacher print)
$teacherId = $_GET['teacher_id'] ?? '';

// Get teachers
if ($teacherId) {
    // Single teacher
    $teacher = $teacherManager->getTeacherById($teacherId);
    $teachers = $teacher ? [$teacher] : [];
} else {
    // All teachers
    $teachers = $teacherManager->getAllTeachers();
}

if (empty($teachers)) {
    die('কোন শিক্ষক পাওয়া যায়নি!');
}

// Get principal signature
$principalSignature = $teacherManager->getActiveSignature('principal');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক আইডি কার্ড - প্রিন্ট</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 10mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 10px;
            background: white;
            color: #000;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            row-gap: 30px;
            column-gap: 20px;
            width: 100%;
            padding: 8px;
        }
        
        .id-card {
            width: 85mm;
            height: 60mm;
            border: 3px solid #000;
            border-radius: 8px;
            padding: 8px;
            background: linear-gradient(135deg, #e8f0fe 0%, #f3e5f5 100%);
            color: #000;
            position: relative;
            overflow: hidden;
            page-break-inside: avoid;
            box-sizing: border-box;
        }
        
        .id-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(0,0,0,0.05)" stroke-width="1"/></svg>');
            opacity: 0.2;
            z-index: 1;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 8px;
            position: relative;
        }

        .college-logo {
            position: absolute;
            top: 0;
            left: 0;
            width: 25px;
            height: 25px;
            opacity: 0.9;
            z-index: 3;
        }

        .college-name {
            font-size: 12px;
            margin: 0 0 4px 0;
            font-weight: bold;
            color: #000;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .college-address {
            font-size: 10px;
            margin: 0 0 6px 0;
            font-weight: 600;
            color: #000;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .card-header h1 {
            font-size: 11px;
            margin: 0 0 3px 0;
            font-weight: bold;
            color: #000;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
            line-height: 1.3;
        }

        .card-header h2 {
            font-size: 9px;
            margin: 3px 0;
            font-weight: 600;
            color: #000;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
            line-height: 1.3;
        }
        
        .card-body {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex-grow: 1;
            margin-bottom: 5px;
            text-align: center;
            padding: 3px 0;
        }

        .photo-section {
            width: 45px;
            height: 45px;
            margin-bottom: 8px;
            flex-shrink: 0;
        }
        
        .teacher-photo {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #333;
            box-shadow: 0 3px 6px rgba(0,0,0,0.3);
        }
        
        .photo-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid #333;
            box-shadow: 0 3px 6px rgba(0,0,0,0.3);
            color: #333;
        }
        
        .teacher-info {
            flex-grow: 1;
            min-width: 0;
            text-align: center;
            margin-top: 5px;
            padding: 0 5px;
        }

        .teacher-name {
            font-size: 11px;
            font-weight: bold;
            margin: 0 0 3px 0;
            color: #000;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.4);
            line-height: 1.2;
        }

        .teacher-designation {
            font-size: 9px;
            margin: 0 0 2px 0;
            color: #000;
            line-height: 1.2;
            font-weight: 600;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
        }

        .teacher-subject {
            font-size: 9px;
            margin: 0;
            color: #000;
            line-height: 1.3;
            font-weight: 600;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
        }
        
        .card-footer {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            border-top: 1px solid rgba(0,0,0,0.2);
            padding-top: 6px;
            min-height: 22px;
            margin-top: 5px;
        }
        
        .text-center {
            text-align: center;
            width: auto;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }
        
        .principal-signature {
            width: 35px;
            height: 16px;
            object-fit: contain;
            opacity: 0.9;
            margin-bottom: 2px;
        }
        
        .signature-text {
            font-size: 9px;
            text-align: right;
            margin-top: 2px;
            color: #000;
            font-weight: 700;
            text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);
            display: block !important;
            width: auto;
            line-height: 1.2;
            position: relative;
            clear: both;
            visibility: visible !important;
            z-index: 5;
            opacity: 1 !important;
        }
        
        @media print {
            body {
                padding: 0;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .no-print {
                display: none !important;
            }

            .cards-container {
                row-gap: 25px;
                column-gap: 15px;
                margin: 3px;
            }

            .id-card {
                border: 4px solid #000 !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                print-color-adjust: exact;
            }

            .college-name,
            .college-address,
            .card-header h1,
            .card-header h2,
            .teacher-name,
            .teacher-designation,
            .teacher-subject,
            .signature-text {
                text-shadow: 3px 3px 8px rgba(255,255,255,0.9), 2px 2px 4px rgba(0,0,0,0.6) !important;
                font-weight: bold !important;
                color: #000 !important;
            }

            .card-footer {
                border-top: 2px solid rgba(0,0,0,0.4) !important;
            }
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            font-size: 14px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button onclick="window.print()" class="print-button no-print">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="cards-container">
        <?php foreach ($teachers as $teacher): ?>
            <div class="id-card">
                <div class="card-content">
                    <!-- Header with Logo -->
                    <div class="card-header">
                        <!-- College Logo on the left -->
                        <?php if (file_exists('uploads/college_logo.jpg')): ?>
                            <img src="uploads/college_logo.jpg" alt="College Logo" class="college-logo">
                        <?php endif; ?>

                        <div class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
                        <div class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</div>
                        <h1>HSC পরীক্ষা-২০২৫</h1>
                        <h2>শিক্ষক আইডি কার্ড</h2>
                    </div>

                    <!-- Body -->
                    <div class="card-body">
                        <!-- Photo -->
                        <div class="photo-section">
                            <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                <img src="<?php echo htmlspecialchars($teacher['photo']); ?>"
                                     alt="Teacher Photo" class="teacher-photo">
                            <?php else: ?>
                                <div class="photo-placeholder">
                                    <span style="font-size: 14px;">👤</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Teacher Info -->
                        <div class="teacher-info">
                            <div class="teacher-name"><?php echo htmlspecialchars($teacher['name']); ?></div>
                            <div class="teacher-designation"><?php echo htmlspecialchars($teacher['designation']); ?></div>
                            <div class="teacher-subject"><?php echo htmlspecialchars($teacher['subject']); ?></div>
                        </div>
                    </div>

                    <!-- Footer with Principal Signature -->
                    <div class="card-footer">
                        <div class="text-center">
                            <?php if (!empty($principalSignature['signature_path']) && file_exists($principalSignature['signature_path'])): ?>
                                <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>"
                                     alt="Principal Signature" class="principal-signature">
                            <?php endif; ?>
                            <span class="signature-text">অধ্যক্ষ</span>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <script>
        // Auto print when page loads (optional)
        // window.onload = function() { window.print(); }
        
        // Print function
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>
