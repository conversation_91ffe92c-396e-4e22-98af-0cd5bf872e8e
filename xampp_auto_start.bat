@echo off
REM XAMPP Auto Start Script for EXMM System
REM Place this file in Windows Startup folder for automatic execution

echo ========================================
echo EXMM - XAMPP Auto Start
echo ========================================
echo.

REM Wait for Windows to fully load
echo Waiting for system to stabilize...
timeout /t 30 >nul

REM Check if XAMPP directory exists
if not exist "C:\xampp" (
    echo ❌ XAMPP not found at C:\xampp
    echo Please install XAMPP or update the path in this script
    pause
    exit /b 1
)

echo Starting XAMPP services...
cd /d "C:\xampp"

REM Start Apache
echo Starting Apache...
if exist "apache_start.bat" (
    start /min apache_start.bat
) else (
    start /min xampp_start.exe
)

REM Wait a moment
timeout /t 5 >nul

REM Start MySQL
echo Starting MySQL...
if exist "mysql_start.bat" (
    start /min mysql_start.bat
) else (
    REM Alternative method using net start
    net start mysql >nul 2>&1
)

echo.
echo Waiting for services to initialize...
timeout /t 15 >nul

REM Verify services are running
echo Checking services...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Apache: Running
) else (
    echo ❌ Apache: Not running
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL: Running
) else (
    echo ❌ MySQL: Not running
)

echo.
echo ========================================
echo XAMPP Auto Start Completed
echo ========================================
echo.
echo EXMM System is ready to use!
echo Access: http://localhost/exmm/
echo.

REM Optional: Open system check page
REM start http://localhost/exmm/system_startup_check.php

REM Close this window after 10 seconds
timeout /t 10 >nul
exit
