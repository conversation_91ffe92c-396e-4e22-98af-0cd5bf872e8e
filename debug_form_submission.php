<?php
session_start();
require_once 'db_connect.php';

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফর্ম সাবমিশন ডিবাগ - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .debug-card {
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4">ফর্ম সাবমিশন ডিবাগ</h1>
        
        <div class="row">
            <div class="col-12">
                <div class="card debug-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">POST ডাটা</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($_POST)): ?>
                            <pre><?php print_r($_POST); ?></pre>
                            
                            <h6 class="mt-4">গুরুত্বপূর্ণ ফিল্ডস:</h6>
                            <ul class="list-group">
                                <li class="list-group-item">
                                    <strong>action:</strong> 
                                    <?php echo isset($_POST['action']) ? htmlspecialchars($_POST['action']) : '<span class="text-danger">সেট করা নেই!</span>'; ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>date:</strong> 
                                    <?php echo isset($_POST['date']) ? htmlspecialchars($_POST['date']) : '<span class="text-danger">সেট করা নেই!</span>'; ?>
                                </li>
                                <li class="list-group-item">
                                    <strong>force_message:</strong> 
                                    <?php echo isset($_POST['force_message']) ? htmlspecialchars($_POST['force_message']) : '<span class="text-danger">সেট করা নেই!</span>'; ?>
                                </li>
                            </ul>
                        <?php else: ?>
                            <div class="alert alert-warning">কোনো POST ডাটা পাওয়া যায়নি।</div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card debug-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">ফর্ম সাবমিশন টেস্ট</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="duty_assignments_management.php" id="testForm">
                            <input type="hidden" name="action" value="bulk_update_individual">
                            <input type="hidden" name="date" value="<?php echo isset($_POST['date']) ? htmlspecialchars($_POST['date']) : ''; ?>">
                            <input type="hidden" name="current_tab" value="manage">
                            <input type="hidden" name="force_message" value="ডিউটি তথ্য সফলভাবে সংরক্ষণ করা হয়েছে!">
                            
                            <?php if (!empty($_POST['room_number'])): ?>
                                <?php foreach ($_POST['room_number'] as $teacherId => $roomNumber): ?>
                                    <input type="hidden" name="room_number[<?php echo htmlspecialchars($teacherId); ?>]" value="<?php echo htmlspecialchars($roomNumber); ?>">
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <?php if (!empty($_POST['duty_shift'])): ?>
                                <?php foreach ($_POST['duty_shift'] as $teacherId => $shift): ?>
                                    <input type="hidden" name="duty_shift[<?php echo htmlspecialchars($teacherId); ?>]" value="<?php echo htmlspecialchars($shift); ?>">
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <button type="submit" class="btn btn-primary">সাবমিট টেস্ট</button>
                        </form>
                    </div>
                </div>
                
                <div class="card debug-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">সেশন ডাটা</h5>
                    </div>
                    <div class="card-body">
                        <pre><?php print_r($_SESSION); ?></pre>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="duty_assignments_management.php" class="btn btn-secondary">ফিরে যান</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 