<?php
// Bulk Seat Card Generator
require_once __DIR__ . '/config/database.php';

// Get parameters
$operation = $_GET['operation'] ?? 'dashboard';
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);

// Database connection
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Subject names mapping
$subjectNames = [
    '101' => 'Bangla',
    '107' => 'English',
    '275' => 'Information & Technology',
    '174' => 'Physics',
    '176' => 'Chemistry',
    '178' => 'Biology',
    '265' => 'Higher Math',
    '253' => 'Accounting',
    '292' => 'Finance & Banking',
    '277' => 'Business Organization & Management',
    '286' => 'Production Management & Marketing',
    '109' => 'Economics',
    '121' => 'Logic',
    '117' => 'Sociology',
    '249' => 'Study Of Islam',
    '271' => 'Social Work',
    '273' => 'Home Science',
    '267' => 'Islamic History',
    '269' => 'Civics & Good Governance',
    '304' => 'History',
    '129' => 'Statistics'
];

// Get statistics
function getStudentStats($db) {
    $stats = [];

    // Total students
    $stmt = $db->query("SELECT COUNT(*) as total FROM students");
    $stats['total'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // By student type
    $stmt = $db->query("SELECT student_type, COUNT(*) as count FROM students GROUP BY student_type");
    $typeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($typeStats as $type) {
        $stats['by_type'][$type['student_type']] = $type['count'];
    }

    // By department
    $stmt = $db->query("SELECT department, COUNT(*) as count FROM students GROUP BY department ORDER BY department");
    $deptStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($deptStats as $dept) {
        $stats['by_department'][$dept['department']] = $dept['count'];
    }

    // Complete vs Incomplete subjects
    $stmt = $db->query("SELECT
        SUM(CASE WHEN subjects IS NOT NULL AND subjects != '' AND (LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1) = 13 THEN 1 ELSE 0 END) as complete,
        SUM(CASE WHEN subjects IS NULL OR subjects = '' OR (LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1) < 13 THEN 1 ELSE 0 END) as incomplete
        FROM students");
    $subjectStats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['complete_subjects'] = $subjectStats['complete'];
    $stats['incomplete_subjects'] = $subjectStats['incomplete'];

    return $stats;
}

// Get subject statistics
function getSubjectStats($db, $subjectNames) {
    $subjectStats = [];

    foreach ($subjectNames as $code => $name) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM students WHERE subjects LIKE :pattern");
        $stmt->execute(['pattern' => '%' . $code . '%']);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

        if ($count > 0) {
            $subjectStats[$code] = [
                'name' => $name,
                'count' => $count
            ];
        }
    }

    return $subjectStats;
}

$stats = getStudentStats($db);
$subjectStats = getSubjectStats($db, $subjectNames);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বাল্ক সীট কার্ড জেনারেটর - HSC Exam 2025</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .stats-section {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 1rem;
            color: #666;
            margin-top: 5px;
        }
        
        .bulk-options {
            padding: 30px;
        }
        
        .option-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .option-card:hover {
            border-color: #667eea;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        }
        
        .option-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        
        .option-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .option-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .btn-generate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-generate:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .cards-per-page-selector {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .option-card .row {
                text-align: center;
            }

            .option-card .col-md-2,
            .option-card .col-md-3 {
                margin-bottom: 10px;
            }

            .stat-card {
                margin-bottom: 15px;
            }

            .header h1 {
                font-size: 1.8rem;
            }

            .option-icon {
                font-size: 2rem !important;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="main-container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-id-card me-3"></i>বাল্ক সীট কার্ড জেনারেটর</h1>
            <p>HSC পরীক্ষা-২০২৫ | আব্দুল ওদুদ শাহ ডিগ্রি কলেজ, দামুড়হুদা, চুয়াডাঙ্গা</p>
        </div>

        <!-- Statistics Section -->
        <div class="stats-section">
            <h3 class="mb-4"><i class="fas fa-chart-bar me-2"></i>শিক্ষার্থী পরিসংখ্যান</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['total']; ?></div>
                        <div class="stat-label">মোট শিক্ষার্থী</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['complete_subjects']; ?></div>
                        <div class="stat-label">সম্পূর্ণ বিষয়</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo $stats['incomplete_subjects']; ?></div>
                        <div class="stat-label">অসম্পূর্ণ বিষয়</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($stats['by_department'] ?? []); ?></div>
                        <div class="stat-label">বিভাগ সংখ্যা</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cards Per Page Selector -->
        <div class="bulk-options">
            <div class="cards-per-page-selector">
                <h5><i class="fas fa-cog me-2"></i>প্রতি পৃষ্ঠায় কার্ড সংখ্যা</h5>
                <div class="row">
                    <div class="col-md-6">
                        <select id="cardsPerPage" class="form-select">
                            <option value="12" <?php echo $cardsPerPage == 12 ? 'selected' : ''; ?>>১২টি কার্ড (৪×৩)</option>
                            <option value="9" <?php echo $cardsPerPage == 9 ? 'selected' : ''; ?>>৯টি কার্ড (৩×৩)</option>
                            <option value="6" <?php echo $cardsPerPage == 6 ? 'selected' : ''; ?>>৬টি কার্ড (৩×২)</option>
                            <option value="4" <?php echo $cardsPerPage == 4 ? 'selected' : ''; ?>>৪টি কার্ড (২×২)</option>
                        </select>
                    </div>
                </div>
            </div>

            <h3 class="mb-4"><i class="fas fa-layer-group me-2"></i>বাল্ক জেনারেশন অপশন</h3>

            <!-- All Students -->
            <div class="option-card" onclick="generateBulkCards('all')">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-users"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">সব শিক্ষার্থীর সীট কার্ড</div>
                        <div class="option-description">সমস্ত শিক্ষার্থীর জন্য একসাথে সীট কার্ড তৈরি করুন (<?php echo $stats['total']; ?> জন)</div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <!-- By Student Type -->
            <?php if (!empty($stats['by_type'])): ?>
                <?php foreach ($stats['by_type'] as $type => $count): ?>
                    <div class="option-card" onclick="generateBulkCards('type', '<?php echo $type; ?>')">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <div class="option-icon">
                                    <i class="fas fa-<?php echo $type == 'Regular' ? 'user-graduate' : ($type == 'Improvement' ? 'user-edit' : 'user-clock'); ?>"></i>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="option-title"><?php echo $type; ?> শিক্ষার্থীদের সীট কার্ড</div>
                                <div class="option-description"><?php echo $type; ?> ধরনের সব শিক্ষার্থীর সীট কার্ড (<?php echo $count; ?> জন)</div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-generate">
                                    <i class="fas fa-print me-2"></i>জেনারেট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Complete vs Incomplete -->
            <div class="option-card" onclick="generateBulkCards('complete')">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-check-circle"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">সম্পূর্ণ বিষয়ের শিক্ষার্থী</div>
                        <div class="option-description">যাদের ১৩টি বিষয় আছে তাদের সীট কার্ড (<?php echo $stats['complete_subjects']; ?> জন)</div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <div class="option-card" onclick="generateBulkCards('incomplete')">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-exclamation-circle"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">অসম্পূর্ণ বিষয়ের শিক্ষার্থী</div>
                        <div class="option-description">যাদের ১৩টির কম বিষয় আছে তাদের সীট কার্ড (<?php echo $stats['incomplete_subjects']; ?> জন)</div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <!-- By Subject -->
            <?php if (!empty($subjectStats)): ?>
                <h4 class="mt-4 mb-3"><i class="fas fa-book me-2"></i>বিষয় অনুযায়ী</h4>
                <div class="row">
                    <?php foreach ($subjectStats as $code => $data): ?>
                        <div class="col-md-6 mb-3">
                            <div class="option-card" onclick="generateBulkCards('subject', '<?php echo $code; ?>')">
                                <div class="row align-items-center">
                                    <div class="col-md-3 text-center">
                                        <div class="option-icon" style="font-size: 2rem;"><i class="fas fa-book-open"></i></div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="option-title" style="font-size: 1.1rem;"><?php echo $code; ?> - <?php echo htmlspecialchars($data['name']); ?></div>
                                        <div class="option-description"><?php echo $data['count']; ?> জন শিক্ষার্থী</div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-generate btn-sm">
                                            <i class="fas fa-print me-1"></i>জেনারেট
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- By Department -->
            <?php if (!empty($stats['by_department'])): ?>
                <h4 class="mt-4 mb-3"><i class="fas fa-building me-2"></i>বিভাগ অনুযায়ী</h4>
                <?php foreach ($stats['by_department'] as $dept => $count): ?>
                    <div class="option-card" onclick="generateBulkCards('department', '<?php echo htmlspecialchars($dept); ?>')">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <div class="option-icon"><i class="fas fa-graduation-cap"></i></div>
                            </div>
                            <div class="col-md-7">
                                <div class="option-title"><?php echo htmlspecialchars($dept); ?> বিভাগ</div>
                                <div class="option-description"><?php echo htmlspecialchars($dept); ?> বিভাগের সব শিক্ষার্থীর সীট কার্ড (<?php echo $count; ?> জন)</div>
                            </div>
                            <div class="col-md-3 text-end">
                                <button class="btn btn-generate">
                                    <i class="fas fa-print me-2"></i>জেনারেট করুন
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Custom Range -->
            <h4 class="mt-4 mb-3"><i class="fas fa-sliders-h me-2"></i>কাস্টম রেঞ্জ</h4>
            <div class="option-card">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-filter"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">রোল নম্বর রেঞ্জ অনুযায়ী</div>
                        <div class="option-description">নির্দিষ্ট রোল নম্বর রেঞ্জের শিক্ষার্থীদের সীট কার্ড</div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <input type="number" id="rollStart" class="form-control" placeholder="শুরুর রোল নম্বর">
                            </div>
                            <div class="col-md-6">
                                <input type="number" id="rollEnd" class="form-control" placeholder="শেষ রোল নম্বর">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate" onclick="generateRangeCards()">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <!-- Gender Based -->
            <div class="option-card" onclick="generateBulkCards('gender', 'Male')">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-male"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">পুরুষ শিক্ষার্থীদের সীট কার্ড</div>
                        <div class="option-description">সব পুরুষ শিক্ষার্থীর সীট কার্ড</div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <div class="option-card" onclick="generateBulkCards('gender', 'Female')">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        <div class="option-icon"><i class="fas fa-female"></i></div>
                    </div>
                    <div class="col-md-7">
                        <div class="option-title">মহিলা শিক্ষার্থীদের সীট কার্ড</div>
                        <div class="option-description">সব মহিলা শিক্ষার্থীর সীট কার্ড</div>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="btn btn-generate">
                            <i class="fas fa-print me-2"></i>জেনারেট করুন
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Summary -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5><i class="fas fa-info-circle me-2"></i>দ্রুত সারসংক্ষেপ</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>মোট শিক্ষার্থী:</strong> <?php echo $stats['total']; ?> জন
                                </div>
                                <div class="col-md-3">
                                    <strong>সম্পূর্ণ বিষয়:</strong> <?php echo $stats['complete_subjects']; ?> জন
                                </div>
                                <div class="col-md-3">
                                    <strong>অসম্পূর্ণ বিষয়:</strong> <?php echo $stats['incomplete_subjects']; ?> জন
                                </div>
                                <div class="col-md-3">
                                    <strong>বিষয় সংখ্যা:</strong> <?php echo count($subjectStats); ?>টি
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-home me-2"></i>হোম পেজে ফিরুন</h5>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>হোম পেজ
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-cog me-2"></i>সীট কার্ড সেটিংস</h5>
                            <a href="seat_card_generator.php" class="btn btn-info">
                                <i class="fas fa-cogs me-2"></i>সেটিংস
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-light">
                        <div class="card-body text-center">
                            <h5><i class="fas fa-filter me-2"></i>বিষয় ফিল্টার</h5>
                            <a href="subject_filter.php" class="btn btn-warning">
                                <i class="fas fa-search me-2"></i>ফিল্টার
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center py-3">
                            <p class="mb-1"><strong>বাল্ক সীট কার্ড জেনারেটর</strong> - HSC পরীক্ষা ২০২৫</p>
                            <p class="mb-0 small">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ, দামুড়হুদা, চুয়াডাঙ্গা | EXMM System v2.0</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        function generateBulkCards(type, value = '') {
            // Show loading feedback
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>জেনারেট হচ্ছে...';
            button.disabled = true;

            const cardsPerPage = document.getElementById('cardsPerPage').value;
            let url = 'working_seat_cards.php?cards_per_page=' + cardsPerPage;

            switch(type) {
                case 'all':
                    // No additional parameters needed
                    break;
                case 'type':
                    url += '&type=' + encodeURIComponent(value);
                    break;
                case 'complete':
                    url += '&complete_only=1';
                    break;
                case 'incomplete':
                    url += '&incomplete_only=1';
                    break;
                case 'department':
                    url += '&group=' + encodeURIComponent(value);
                    break;
                case 'gender':
                    url += '&gender=' + encodeURIComponent(value);
                    break;
                case 'subject':
                    url += '&subject=' + encodeURIComponent(value);
                    break;
            }

            // Open in new window
            window.open(url, '_blank');

            // Reset button after a short delay
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }

        function generateRangeCards() {
            const cardsPerPage = document.getElementById('cardsPerPage').value;
            const rollStart = document.getElementById('rollStart').value;
            const rollEnd = document.getElementById('rollEnd').value;

            if (!rollStart || !rollEnd) {
                alert('অনুগ্রহ করে শুরু এবং শেষ রোল নম্বর দিন');
                return;
            }

            if (parseInt(rollStart) > parseInt(rollEnd)) {
                alert('শুরুর রোল নম্বর শেষের রোল নম্বরের চেয়ে বড় হতে পারে না');
                return;
            }

            // Show loading feedback
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>জেনারেট হচ্ছে...';
            button.disabled = true;

            // Generate roll numbers array
            const rolls = [];
            for (let i = parseInt(rollStart); i <= parseInt(rollEnd); i++) {
                rolls.push(i);
            }

            const url = 'working_seat_cards.php?cards_per_page=' + cardsPerPage + '&rolls=' + rolls.join(',');
            window.open(url, '_blank');

            // Reset button after a short delay
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }

        // Auto-refresh stats every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl + A for All Students
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                generateBulkCards('all');
            }
            // Ctrl + C for Complete Students
            if (e.ctrlKey && e.key === 'c') {
                e.preventDefault();
                generateBulkCards('complete');
            }
            // Ctrl + I for Incomplete Students
            if (e.ctrlKey && e.key === 'i') {
                e.preventDefault();
                generateBulkCards('incomplete');
            }
            // Ctrl + H for Home
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                window.location.href = 'index.php';
            }
        });

        // Show keyboard shortcuts help
        function showKeyboardHelp() {
            alert('কীবোর্ড শর্টকাট:\n\nCtrl + A = সব শিক্ষার্থী\nCtrl + C = সম্পূর্ণ বিষয়\nCtrl + I = অসম্পূর্ণ বিষয়\nCtrl + H = হোম পেজ');
        }

        // Add help button functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add help button to header
            const header = document.querySelector('.header');
            if (header) {
                const helpBtn = document.createElement('button');
                helpBtn.className = 'btn btn-light btn-sm position-absolute';
                helpBtn.style.cssText = 'top: 10px; right: 10px;';
                helpBtn.innerHTML = '<i class="fas fa-keyboard"></i> শর্টকাট';
                helpBtn.onclick = showKeyboardHelp;
                header.style.position = 'relative';
                header.appendChild(helpBtn);
            }
        });
    </script>
</body>
</html>
