<?php
/**
 * Database Structure Checker
 * This script checks the current database structure and provides recommendations
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/utils/DatabaseHelper.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    $dbHelper = new DatabaseHelper($db);
    
    echo "<!DOCTYPE html><html><head><title>Database Structure Check</title>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: #4caf50; }
        .warning { color: #ff9800; }
        .error { color: #f44336; }
        .info { color: #2196f3; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; }
    </style></head><body>";
    
    echo "<h1>🔍 Database Structure Analysis</h1>";
    
    // Check if students table exists
    $stmt = $db->query("SHOW TABLES LIKE 'students'");
    if ($stmt->rowCount() > 0) {
        echo "<p class='success'>✅ Students table exists</p>";
        
        // Get table structure
        $columns = $dbHelper->getStudentsTableColumns();
        echo "<h2>📋 Table Columns</h2>";
        echo "<table>";
        echo "<tr><th>Column Name</th><th>Status</th><th>Usage</th></tr>";
        
        // Check important columns
        $importantColumns = [
            'id' => 'Primary key',
            'roll' => 'Roll number (new format)',
            'roll_no' => 'Roll number (old format)', 
            'name' => 'Student name (new format)',
            'student_name' => 'Student name (old format)',
            'registration' => 'Registration number (new format)',
            'reg_no' => 'Registration number (old format)',
            'department' => 'Department (new format)',
            'group_name' => 'Department (old format)',
            'student_type' => 'Student type (new format)',
            'type' => 'Student type (old format)',
            'academic_year' => 'Academic year (new format)',
            'session' => 'Academic year (old format)',
            'subjects' => 'Subjects (new format)',
            'sub_1' => 'Subject 1 (old format)',
            'sub_2' => 'Subject 2 (old format)',
            'sub_13' => 'Subject 13 (old format)'
        ];
        
        foreach ($importantColumns as $col => $desc) {
            $exists = in_array($col, $columns);
            $status = $exists ? "<span class='success'>✅ Exists</span>" : "<span class='warning'>❌ Missing</span>";
            echo "<tr><td>$col</td><td>$status</td><td>$desc</td></tr>";
        }
        echo "</table>";
        
        // Analysis
        echo "<h2>📊 Structure Analysis</h2>";
        
        $hasNewSubjects = $dbHelper->hasNewSubjectsField();
        $hasOldSubjects = $dbHelper->hasOldSubjectColumns();
        $rollColumn = $dbHelper->getRollColumnName();
        
        echo "<div class='info'>";
        echo "<h3>Subject Storage Format:</h3>";
        if ($hasNewSubjects && !$hasOldSubjects) {
            echo "<p class='success'>✅ <strong>Modern Format:</strong> Using 'subjects' field (comma-separated)</p>";
            echo "<p>Example: '101,107,275,174,176'</p>";
        } elseif (!$hasNewSubjects && $hasOldSubjects) {
            echo "<p class='warning'>⚠️ <strong>Legacy Format:</strong> Using individual 'sub_1' to 'sub_13' columns</p>";
            echo "<p>Recommendation: Consider migrating to new format</p>";
        } elseif ($hasNewSubjects && $hasOldSubjects) {
            echo "<p class='info'>🔄 <strong>Hybrid Format:</strong> Both formats available</p>";
            echo "<p>System will prioritize new format</p>";
        } else {
            echo "<p class='error'>❌ <strong>No Subject Columns Found!</strong></p>";
        }
        
        echo "<h3>Roll Number Field:</h3>";
        echo "<p class='info'>📋 Using: <strong>$rollColumn</strong></p>";
        
        echo "</div>";
        
        // Recommendations
        echo "<h2>💡 Recommendations</h2>";
        echo "<ul>";
        
        if (!$hasNewSubjects && $hasOldSubjects) {
            echo "<li class='warning'>Consider migrating to new 'subjects' field format for better performance</li>";
        }
        
        if (!in_array('roll', $columns) && !in_array('roll_no', $columns)) {
            echo "<li class='error'>Add a roll number field (either 'roll' or 'roll_no')</li>";
        }
        
        if (!in_array('name', $columns) && !in_array('student_name', $columns)) {
            echo "<li class='error'>Add a student name field (either 'name' or 'student_name')</li>";
        }
        
        echo "<li class='info'>All CSV exports and seat card generations should work with current structure</li>";
        echo "</ul>";
        
        // Sample data check
        echo "<h2>📈 Sample Data</h2>";
        $stmt = $db->query("SELECT COUNT(*) as total FROM students");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p>Total students: <strong>$total</strong></p>";
        
        if ($total > 0) {
            $stmt = $db->query("SELECT * FROM students LIMIT 3");
            $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Sample Records:</h3>";
            echo "<table>";
            echo "<tr>";
            foreach (array_keys($samples[0]) as $col) {
                echo "<th>$col</th>";
            }
            echo "</tr>";
            
            foreach ($samples as $sample) {
                echo "<tr>";
                foreach ($sample as $value) {
                    $displayValue = strlen($value) > 30 ? substr($value, 0, 30) . '...' : $value;
                    echo "<td>" . htmlspecialchars($displayValue) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p class='error'>❌ Students table not found!</p>";
    }
    
    // Test queries
    echo "<h2>🧪 Test Queries</h2>";
    
    try {
        $testQueries = [
            "Basic select" => "SELECT COUNT(*) as count FROM students",
            "Roll ordering" => "SELECT COUNT(*) as count FROM students ORDER BY CAST($rollColumn AS UNSIGNED) ASC LIMIT 1",
            "Subject filtering" => $hasNewSubjects ? 
                "SELECT COUNT(*) as count FROM students WHERE subjects LIKE '%101%'" : 
                "SELECT COUNT(*) as count FROM students WHERE sub_1 = '101'"
        ];
        
        foreach ($testQueries as $name => $query) {
            try {
                $stmt = $db->query($query);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo "<p class='success'>✅ $name: Works (found {$result['count']} records)</p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ $name: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>Error running test queries: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<hr>";
    echo "<p><a href='index.php'>← Back to Home</a> | ";
    echo "<a href='subject_filter.php'>Subject Filter</a> | ";
    echo "<a href='export_subject.php?code=101&debug=1'>Test Export</a></p>";
    
    echo "</body></html>";
    
} catch (Exception $e) {
    echo "Error: " . htmlspecialchars($e->getMessage());
}
?>
