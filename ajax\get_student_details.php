<?php
require_once '../models/Student.php';
require_once '../utils/DatabaseHelper.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $studentId = (int)$_POST['id'];
    
    try {
        $student = new Student();
        $studentData = $student->getById($studentId);

        if ($studentData) {
            // Create database helper for field mapping
            require_once '../config/database.php';
            $database = new Database();
            $db = $database->getConnection();
            $dbHelper = new DatabaseHelper($db);
            ?>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">ব্যক্তিগত তথ্য</h6>
                    <table class="table table-sm">
                        <tr>
                            <td><strong>ID:</strong></td>
                            <td><?php echo htmlspecialchars($studentData['id']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>রোল নং:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getRollNumber($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>রেজি. নং:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getRegistrationNumber($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>শিক্ষার্থীর নাম:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getStudentName($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>পিতার নাম:</strong></td>
                            <td><?php echo htmlspecialchars($studentData['father_name']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>লিঙ্গ:</strong></td>
                            <td><?php echo htmlspecialchars($studentData['gender']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>গ্রুপ:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getDepartment($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>সেশন:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getAcademicYear($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>ধরন:</strong></td>
                            <td><?php echo htmlspecialchars($dbHelper->getStudentType($studentData)); ?></td>
                        </tr>
                        <tr>
                            <td><strong>কলেজ কোড:</strong></td>
                            <td><?php echo htmlspecialchars($studentData['c_code']); ?></td>
                        </tr>
                        <tr>
                            <td><strong>EIIN:</strong></td>
                            <td><?php echo htmlspecialchars($studentData['eiin']); ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">বিষয়সমূহ</h6>
                    <table class="table table-sm">
                        <?php
                        // Use helper to extract subjects
                        $subjects = $dbHelper->extractSubjects($studentData);

                        if (!empty($subjects)):
                            foreach ($subjects as $index => $subject):
                                if (!empty($subject)): ?>
                                    <tr>
                                        <td><strong>বিষয় <?php echo $index + 1; ?>:</strong></td>
                                        <td>
                                            <span class="badge <?php echo ($subject === '101') ? 'bg-success' : 'bg-secondary'; ?>">
                                                <?php echo htmlspecialchars($subject); ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endif;
                            endforeach;
                        else: ?>
                            <tr>
                                <td colspan="2" class="text-muted text-center">কোনো বিষয় পাওয়া যায়নি</td>
                            </tr>
                        <?php endif; ?>
                    </table>
                    
                    <?php if (!empty($studentData['created_at'])): ?>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar"></i> 
                                যোগ করা হয়েছে: <?php echo date('d/m/Y H:i', strtotime($studentData['created_at'])); ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="d-flex justify-content-end">
                        <a href="edit_student.php?id=<?php echo $studentData['id']; ?>" class="btn btn-warning btn-sm me-2">
                            <i class="fas fa-edit"></i> সম্পাদনা করুন
                        </a>
                        <button type="button" class="btn btn-danger btn-sm delete-student"
                                data-id="<?php echo $studentData['id']; ?>"
                                data-name="<?php echo htmlspecialchars($dbHelper->getStudentName($studentData)); ?>"
                                data-roll="<?php echo htmlspecialchars($dbHelper->getRollNumber($studentData)); ?>"
                                data-bs-dismiss="modal">
                            <i class="fas fa-trash"></i> মুছে ফেলুন
                        </button>
                    </div>
                </div>
            </div>
            <?php
        } else {
            echo '<div class="alert alert-warning">শিক্ষার্থীর তথ্য পাওয়া যায়নি।</div>';
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">তথ্য লোড করতে সমস্যা হয়েছে: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
} else {
    echo '<div class="alert alert-danger">অবৈধ অনুরোধ।</div>';
}
?>
