<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Handle delete duty assignment
if (isset($_POST['delete_duty'])) {
    $dateToDelete = $_POST['date_to_delete'];
    try {
        $teacherManager->removeDutyAssignment($dateToDelete);
        $message = 'ডিউটি বন্টন সফলভাবে মুছে ফেলা হয়েছে!';
        $messageType = 'success';
        // Refresh duty dates
        $dutyDates = $teacherManager->getAllDutyDates();
    } catch (Exception $e) {
        $message = 'ডিউটি মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বন্টনকৃত ডিউটি তালিকা - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .duty-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .duty-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .duty-date {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .teacher-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .teacher-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        
        .teacher-item:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .teacher-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .teacher-details {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-calendar-check me-3"></i>বন্টনকৃত ডিউটি তালিকা
                </h1>
                <p class="text-white-50">সব ডিউটি বন্টনের তালিকা দেখুন ও ব্যবস্থাপনা করুন</p>
            </div>

            <!-- Message Display -->
            <?php if (isset($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                    <strong><?php echo $message; ?></strong>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-card">
                <div class="row">
                    <div class="col-md-4">
                        <h3><?php echo count($dutyDates); ?></h3>
                        <p class="mb-0">মোট ডিউটি তারিখ</p>
                    </div>
                    <div class="col-md-4">
                        <h3>
                            <?php 
                            $totalAssignments = 0;
                            foreach ($dutyDates as $date) {
                                $assignments = $teacherManager->getDutyAssignments($date);
                                $totalAssignments += count($assignments);
                            }
                            echo $totalAssignments;
                            ?>
                        </h3>
                        <p class="mb-0">মোট ডিউটি বন্টন</p>
                    </div>
                    <div class="col-md-4">
                        <h3><?php echo count($teacherManager->getAllTeachers()); ?></h3>
                        <p class="mb-0">মোট শিক্ষক</p>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="main-card">
                <h4 class="mb-4">
                    <i class="fas fa-bolt text-warning me-2"></i>দ্রুত অ্যাকশন
                </h4>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="teacher_duty_management.php" class="btn btn-success w-100">
                            <i class="fas fa-plus me-2"></i>নতুন ডিউটি বন্টন
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="date_wise_duty_assignment.php" class="btn btn-info w-100">
                            <i class="fas fa-calendar me-2"></i>তারিখ ভিত্তিক বন্টন
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="duty_letter_generator.php" class="btn btn-primary w-100">
                            <i class="fas fa-file-alt me-2"></i>ডিউটি লেটার
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="teacher_id_card_generator.php" class="btn btn-warning w-100">
                            <i class="fas fa-id-card me-2"></i>আইডি কার্ড
                        </a>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <a href="date_wise_duty_assignment.php" class="btn btn-primary">
                        <i class="fas fa-calendar-plus me-2"></i>নতুন ডিউটি বন্টন করুন
                    </a>
                    <a href="print_duty_list.php" class="btn btn-success ms-2">
                        <i class="fas fa-print me-2"></i>ডিউটি তালিকা প্রিন্ট করুন
                    </a>
                    <a href="reset_duty_assignment.php" class="btn btn-danger ms-2">
                        <i class="fas fa-trash me-2"></i>ডিউটি রিসেট করুন
                    </a>
                </div>
            </div>

            <!-- Duty Assignments List -->
            <?php if (empty($dutyDates)): ?>
                <div class="main-card">
                    <div class="empty-state">
                        <i class="fas fa-calendar-times"></i>
                        <h4>কোন ডিউটি বন্টন পাওয়া যায়নি!</h4>
                        <p>এখনো কোন ডিউটি বন্টন করা হয়নি। নতুন ডিউটি বন্টন করতে নিচের বাটনে ক্লিক করুন।</p>
                        <a href="teacher_duty_management.php" class="btn btn-custom">
                            <i class="fas fa-plus me-2"></i>প্রথম ডিউটি বন্টন করুন
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($dutyDates as $date): ?>
                    <?php 
                    $assignments = $teacherManager->getDutyAssignments($date);
                    $teacherCount = count($assignments);
                    ?>
                    <div class="duty-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <div class="duty-date">
                                    <i class="fas fa-calendar-day text-primary me-2"></i>
                                    <?php echo formatDateBengali($date); ?>
                                </div>
                                <p class="text-muted mb-0">
                                    <i class="fas fa-users me-1"></i><?php echo $teacherCount; ?> জন শিক্ষক নিয়োজিত
                                </p>
                            </div>
                            <div class="action-buttons">
                                <a href="print_duty_list.php?date=<?php echo urlencode($date); ?>" 
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-print me-1"></i>প্রিন্ট
                                </a>
                                <a href="duty_letter_generator.php?date=<?php echo urlencode($date); ?>" 
                                   class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-file-alt me-1"></i>লেটার
                                </a>
                                <a href="teacher_id_card_generator.php?date=<?php echo urlencode($date); ?>" 
                                   class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-id-card me-1"></i>আইডি কার্ড
                                </a>
                                <form method="POST" class="d-inline" 
                                      onsubmit="return confirm('এই তারিখের ডিউটি বন্টন মুছে ফেলতে চান?')">
                                    <input type="hidden" name="date_to_delete" value="<?php echo $date; ?>">
                                    <button type="submit" name="delete_duty" class="btn btn-outline-danger btn-sm">
                                        <i class="fas fa-trash me-1"></i>মুছুন
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Teachers Grid -->
                        <div class="teacher-grid">
                            <?php foreach ($assignments as $assignment): ?>
                                <div class="teacher-item">
                                    <div class="teacher-name">
                                        <?php echo htmlspecialchars($assignment['teacher_name']); ?>
                                    </div>
                                    <div class="teacher-details">
                                        <i class="fas fa-book me-1"></i><?php echo htmlspecialchars($assignment['subject']); ?><br>
                                        <i class="fas fa-id-badge me-1"></i><?php echo htmlspecialchars($assignment['designation']); ?><br>
                                        <i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($assignment['mobile']); ?>
                                        <?php if (!empty($assignment['room_number'])): ?>
                                            <br><i class="fas fa-door-open me-1"></i>রুম: <?php echo htmlspecialchars($assignment['room_number']); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
