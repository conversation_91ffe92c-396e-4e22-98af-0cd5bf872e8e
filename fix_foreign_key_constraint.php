<?php
// fix_foreign_key_constraint.php
// এই স্ক্রিপ্টটি duty_assignments টেবিলে ফরেন কী কনস্ট্রেইন্ট সমস্যা সমাধান করবে

// ডাটাবেস কানেকশন
require_once 'includes/teacher_db.php';

// পেইজ হেডার
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফরেন কী কনস্ট্রেইন্ট সমাধান</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .alert {
            margin-top: 20px;
        }
        .code-block {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="my-4">ফরেন কী কনস্ট্রেইন্ট সমাধান</h1>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">সমস্যা নির্ণয়</h5>
            </div>
            <div class="card-body">
                <?php
                // সমস্যাযুক্ত রেকর্ড খুঁজে বের করা
                $sql = "SELECT da.teacher_id, da.duty_date, da.room_number 
                        FROM duty_assignments da 
                        LEFT JOIN teachers t ON da.teacher_id = t.id 
                        WHERE t.id IS NULL";
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $invalidRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // ফরেন কী কনস্ট্রেইন্ট চেক
                $fkQuery = "SELECT TABLE_NAME, COLUMN_NAME, CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME 
                            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                            WHERE REFERENCED_TABLE_SCHEMA = 'exmm' 
                            AND TABLE_NAME = 'duty_assignments'";
                $fkResult = $pdo->query($fkQuery)->fetchAll();
                
                // ফরেন কী কনস্ট্রেইন্ট স্টেটাস
                if (count($fkResult) > 0) {
                    echo "<div class='alert alert-info'>";
                    foreach ($fkResult as $fk) {
                        echo "<p>টেবিল <strong>{$fk['TABLE_NAME']}</strong> এর <strong>{$fk['COLUMN_NAME']}</strong> কলাম 
                              <strong>{$fk['REFERENCED_TABLE_NAME']}</strong> টেবিলের <strong>{$fk['REFERENCED_COLUMN_NAME']}</strong> কলামকে রেফারেন্স করে।</p>";
                    }
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-warning'>duty_assignments টেবিলে কোন ফরেন কী কনস্ট্রেইন্ট পাওয়া যায়নি।</div>";
                }
                
                // সমস্যাযুক্ত রেকর্ড দেখানো
                if (count($invalidRecords) > 0) {
                    echo "<div class='alert alert-danger'>";
                    echo "<p><strong>" . count($invalidRecords) . "টি সমস্যাযুক্ত রেকর্ড পাওয়া গেছে:</strong></p>";
                    echo "<table class='table table-bordered table-striped'>";
                    echo "<thead><tr><th>শিক্ষক আইডি</th><th>ডিউটি তারিখ</th><th>রুম নম্বর</th></tr></thead>";
                    echo "<tbody>";
                    foreach ($invalidRecords as $record) {
                        echo "<tr>";
                        echo "<td>{$record['teacher_id']}</td>";
                        echo "<td>{$record['duty_date']}</td>";
                        echo "<td>{$record['room_number']}</td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                    echo "</div>";
                } else {
                    echo "<div class='alert alert-success'>কোন সমস্যাযুক্ত রেকর্ড পাওয়া যায়নি। ফরেন কী কনস্ট্রেইন্ট ঠিক আছে।</div>";
                }
                ?>
            </div>
        </div>
        
        <?php if (count($invalidRecords) > 0): ?>
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">সমাধান</h5>
            </div>
            <div class="card-body">
                <h6>সমাধানের বিকল্পসমূহ:</h6>
                
                <div class="mb-4">
                    <h6>অপশন ১: সমস্যাযুক্ত রেকর্ডগুলি মুছে ফেলা</h6>
                    <div class="code-block mb-3">
                        DELETE FROM duty_assignments 
                        WHERE teacher_id IN (
                            SELECT da.teacher_id 
                            FROM duty_assignments da 
                            LEFT JOIN teachers t ON da.teacher_id = t.id 
                            WHERE t.id IS NULL
                        );
                    </div>
                    <form method="post" action="">
                        <input type="hidden" name="action" value="delete_records">
                        <button type="submit" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্যাযুক্ত রেকর্ডগুলি মুছতে চান?');">সমস্যাযুক্ত রেকর্ডগুলি মুছুন</button>
                    </form>
                </div>
                
                <div class="mb-4">
                    <h6>অপশন ২: ফরেন কী কনস্ট্রেইন্ট সরিয়ে ফেলা এবং পুনরায় যোগ করা</h6>
                    <div class="code-block mb-3">
                        ALTER TABLE duty_assignments DROP FOREIGN KEY duty_assignments_ibfk_1;
                        ALTER TABLE duty_assignments ADD CONSTRAINT duty_assignments_ibfk_1 
                        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE;
                    </div>
                    <form method="post" action="">
                        <input type="hidden" name="action" value="recreate_constraint">
                        <button type="submit" class="btn btn-warning" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি ফরেন কী কনস্ট্রেইন্ট পুনরায় তৈরি করতে চান?');">ফরেন কী কনস্ট্রেইন্ট পুনরায় তৈরি করুন</button>
                    </form>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php
        // সমাধান প্রক্রিয়া
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'delete_records') {
                try {
                    // সমস্যাযুক্ত রেকর্ডগুলি মুছে ফেলা
                    $deleteSql = "DELETE FROM duty_assignments 
                                  WHERE teacher_id IN (
                                      SELECT da.teacher_id 
                                      FROM (SELECT * FROM duty_assignments) da 
                                      LEFT JOIN teachers t ON da.teacher_id = t.id 
                                      WHERE t.id IS NULL
                                  )";
                    $stmt = $pdo->prepare($deleteSql);
                    $stmt->execute();
                    $deletedCount = $stmt->rowCount();
                    
                    echo "<div class='alert alert-success mt-4'>$deletedCount টি সমস্যাযুক্ত রেকর্ড সফলভাবে মুছে ফেলা হয়েছে।</div>";
                    echo "<div class='alert alert-info'>পেইজটি রিফ্রেশ করুন সমস্যা সমাধান হয়েছে কিনা তা দেখতে।</div>";
                    echo "<a href='fix_foreign_key_constraint.php' class='btn btn-primary'>পেইজ রিফ্রেশ করুন</a>";
                } catch (PDOException $e) {
                    echo "<div class='alert alert-danger mt-4'>এরর: " . $e->getMessage() . "</div>";
                }
            } elseif ($action === 'recreate_constraint') {
                try {
                    // ফরেন কী কনস্ট্রেইন্ট সরিয়ে ফেলা
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
                    
                    // আগের কনস্ট্রেইন্ট সরিয়ে ফেলা
                    $dropConstraintSql = "ALTER TABLE duty_assignments DROP FOREIGN KEY duty_assignments_ibfk_1";
                    $pdo->exec($dropConstraintSql);
                    
                    // নতুন কনস্ট্রেইন্ট যোগ করা
                    $addConstraintSql = "ALTER TABLE duty_assignments ADD CONSTRAINT duty_assignments_ibfk_1 FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE";
                    $pdo->exec($addConstraintSql);
                    
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                    
                    echo "<div class='alert alert-success mt-4'>ফরেন কী কনস্ট্রেইন্ট সফলভাবে পুনরায় তৈরি করা হয়েছে।</div>";
                    echo "<div class='alert alert-info'>পেইজটি রিফ্রেশ করুন সমস্যা সমাধান হয়েছে কিনা তা দেখতে।</div>";
                    echo "<a href='fix_foreign_key_constraint.php' class='btn btn-primary'>পেইজ রিফ্রেশ করুন</a>";
                } catch (PDOException $e) {
                    echo "<div class='alert alert-danger mt-4'>এরর: " . $e->getMessage() . "</div>";
                    // ফরেন কী চেক পুনরায় চালু করা
                    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
                }
            }
        }
        ?>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">হোম পেইজে ফিরে যান</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>