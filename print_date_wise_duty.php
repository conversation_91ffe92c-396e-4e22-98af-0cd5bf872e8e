<?php
require_once 'includes/teacher_db.php';

// Get all duty dates from database
try {
    $dutyDates = $teacherManager->getAllDutyDates();
} catch (Exception $e) {
    $error = 'ডিউটি তারিখ লোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
}

// Bengali month names
$bengaliMonths = [
    1 => 'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 'মে', 'জুন',
    'জুলাই', 'আগস্ট', 'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
];

function formatDateBengali($dateString) {
    global $bengaliMonths;
    $date = new DateTime($dateString);
    $day = $date->format('d');
    $month = $bengaliMonths[(int)$date->format('m')];
    $year = $date->format('Y');
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>তারিখ ভিত্তিক ডিউটি লিস্ট প্রিন্ট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 500;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd5 0%, #6a4294 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        .date-select {
            border-radius: 10px;
            padding: 10px;
            border: 2px solid #ddd;
        }
        .date-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        }
        .instructions {
            background-color: #f0f4ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content p-4">
        <div class="container mt-4">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="mb-0"><i class="fas fa-print me-2"></i>তারিখ ভিত্তিক ডিউটি লিস্ট প্রিন্ট</h4>
                        </div>
                        <div class="card-body">
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    <?php echo $error; ?>
                                </div>
                            <?php else: ?>
                                <div class="instructions mb-4">
                                    <p class="mb-0"><i class="fas fa-info-circle me-2"></i>নির্দেশনা: নিচে থেকে একটি তারিখ নির্বাচন করুন এবং "ডিউটি লিস্ট প্রিন্ট করুন" বাটনে ক্লিক করুন। এটি নির্বাচিত তারিখের জন্য ডিউটি লিস্ট প্রিন্ট করবে।</p>
                                </div>
                                
                                <?php if (empty($dutyDates)): ?>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>কোন ডিউটি তারিখ পাওয়া যায়নি। প্রথমে তারিখ ভিত্তিক ডিউটি বন্টন করুন।
                                    </div>
                                <?php else: ?>
                                    <form action="print_duty_list.php" method="get" target="_blank">
                                        <div class="mb-4">
                                            <label for="date" class="form-label fw-bold">তারিখ নির্বাচন করুন:</label>
                                            <select name="date" id="date" class="form-select date-select" required>
                                                <option value="">-- তারিখ নির্বাচন করুন --</option>
                                                <?php foreach ($dutyDates as $date): ?>
                                                    <option value="<?php echo $date; ?>">
                                                        <?php echo formatDateBengali($date); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-print me-2"></i>ডিউটি লিস্ট প্রিন্ট করুন
                                            </button>
                                        </div>
                                    </form>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>