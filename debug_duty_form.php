<?php
session_start();
require_once 'db_connect.php';
require_once 'includes/teacher_db.php';

// Initialize TeacherManager
$teacherManager = new TeacherManager($pdo);

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Get selected date
$selectedDate = $_GET['date'] ?? ($dutyDates[0] ?? date('Y-m-d'));

// Get assignments for selected date
$assignments = $teacherManager->getDutyAssignments($selectedDate);

header('Content-Type: text/html; charset=utf-8');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডিউটি ফর্ম ডিবাগ - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .debug-card {
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <h1 class="mb-4">ডিউটি ফর্ম ডিবাগ</h1>
        
        <div class="row">
            <div class="col-12">
                <div class="card debug-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">ফর্ম স্ট্রাকচার</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="duty_assignments_management.php?tab=manage&date=<?php echo urlencode($selectedDate); ?>&nocache=<?php echo time(); ?>" id="bulkUpdateForm">
                            <input type="hidden" name="action" value="bulk_update_individual">
                            <input type="hidden" name="date" value="<?php echo htmlspecialchars($selectedDate); ?>">
                            <input type="hidden" name="current_tab" value="manage">
                            <input type="hidden" name="force_message" value="ডিউটি তথ্য সফলভাবে সংরক্ষণ করা হয়েছে!">
                            
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>ক্রমিক নং</th>
                                            <th>নাম ও পদবী</th>
                                            <th>কলেজ ও মোবাইল</th>
                                            <th style="width: 120px;">রুম নম্বর</th>
                                            <th style="width: 150px;">ডিউটি শিফট</th>
                                            <th style="width: 80px;">অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($assignments)): ?>
                                            <tr><td colspan="6" class="text-center">এই তারিখে কোনো ডিউটি বন্টন পাওয়া যায়নি।</td></tr>
                                        <?php else: ?>
                                            <?php foreach ($assignments as $index => $assignment): ?>
                                                <tr>
                                                    <td><?php echo $index + 1; ?></td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($assignment['teacher_name']); ?></strong><br>
                                                        <small><?php echo htmlspecialchars($assignment['designation']); ?></small>
                                                    </td>
                                                    <td>
                                                        <?php echo htmlspecialchars($assignment['college']); ?><br>
                                                        <small><?php echo htmlspecialchars($assignment['mobile']); ?></small>
                                                    </td>
                                                    <td><input type="text" name="room_number[<?php echo $assignment['teacher_id']; ?>]" class="form-control form-control-sm" value="<?php echo htmlspecialchars($assignment['room_number']); ?>"></td>
                                                    <td>
                                                        <select name="duty_shift[<?php echo $assignment['teacher_id']; ?>]" class="form-select form-select-sm">
                                                            <option value="Morning" <?php echo ($assignment['duty_shift'] === 'Morning') ? 'selected' : ''; ?>>সকাল</option>
                                                            <option value="Afternoon" <?php echo ($assignment['duty_shift'] === 'Afternoon') ? 'selected' : ''; ?>>বিকাল</option>
                                                        </select>
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-danger btn-sm"><i class="fas fa-trash-alt"></i></button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-end mt-3">
                                <button type="button" class="btn btn-primary" id="saveChangesBtn"><i class="fas fa-save"></i> সকল পরিবর্তন সংরক্ষণ করুন</button>
                                <button type="button" class="btn btn-info ms-2" id="debugBtn"><i class="fas fa-bug"></i> ডিবাগ</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card debug-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">ফর্ম ডাটা</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <strong>ফর্ম অ্যাকশন:</strong> 
                                <code>duty_assignments_management.php?tab=manage&date=<?php echo urlencode($selectedDate); ?>&nocache=<?php echo time(); ?></code>
                            </div>
                            <div class="col-md-4">
                                <strong>মেথড:</strong> <code>POST</code>
                            </div>
                            <div class="col-md-4">
                                <strong>অ্যাকশন ফিল্ড:</strong> <code>bulk_update_individual</code>
                            </div>
                        </div>
                        
                        <h6 class="mt-4">হিডেন ফিল্ডস:</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><strong>action:</strong> bulk_update_individual</span>
                                <span class="badge bg-success">OK</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><strong>date:</strong> <?php echo htmlspecialchars($selectedDate); ?></span>
                                <span class="badge bg-success">OK</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><strong>current_tab:</strong> manage</span>
                                <span class="badge bg-success">OK</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><strong>force_message:</strong> ডিউটি তথ্য সফলভাবে সংরক্ষণ করা হয়েছে!</span>
                                <span class="badge bg-success">OK</span>
                            </li>
                        </ul>
                        
                        <h6 class="mt-4">ডাটা ফিল্ডস:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead>
                                    <tr>
                                        <th>শিক্ষক আইডি</th>
                                        <th>রুম নম্বর ফিল্ড</th>
                                        <th>শিফট ফিল্ড</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($assignments)): ?>
                                        <?php foreach ($assignments as $assignment): ?>
                                            <tr>
                                                <td><?php echo $assignment['teacher_id']; ?></td>
                                                <td><code>room_number[<?php echo $assignment['teacher_id']; ?>]</code></td>
                                                <td><code>duty_shift[<?php echo $assignment['teacher_id']; ?>]</code></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="3" class="text-center">কোনো ডাটা নেই</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="card debug-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="card-title mb-0">ম্যানুয়াল টেস্ট ফর্ম</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="duty_assignments_management.php" id="manualTestForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="test_action" class="form-label">অ্যাকশন:</label>
                                    <input type="text" id="test_action" name="action" class="form-control" value="bulk_update_individual">
                                </div>
                                <div class="col-md-6">
                                    <label for="test_date" class="form-label">তারিখ:</label>
                                    <input type="text" id="test_date" name="date" class="form-control" value="<?php echo htmlspecialchars($selectedDate); ?>">
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="test_tab" class="form-label">ট্যাব:</label>
                                    <input type="text" id="test_tab" name="current_tab" class="form-control" value="manage">
                                </div>
                                <div class="col-md-6">
                                    <label for="test_message" class="form-label">বার্তা:</label>
                                    <input type="text" id="test_message" name="force_message" class="form-control" value="ডিউটি তথ্য সফলভাবে সংরক্ষণ করা হয়েছে!">
                                </div>
                            </div>
                            
                            <?php if (!empty($assignments)): ?>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">রুম নম্বর (<?php echo $assignments[0]['teacher_id']; ?>):</label>
                                        <input type="text" name="room_number[<?php echo $assignments[0]['teacher_id']; ?>]" class="form-control" value="<?php echo htmlspecialchars($assignments[0]['room_number']); ?>">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">ডিউটি শিফট (<?php echo $assignments[0]['teacher_id']; ?>):</label>
                                        <select name="duty_shift[<?php echo $assignments[0]['teacher_id']; ?>]" class="form-select">
                                            <option value="Morning" <?php echo ($assignments[0]['duty_shift'] === 'Morning') ? 'selected' : ''; ?>>সকাল</option>
                                            <option value="Afternoon" <?php echo ($assignments[0]['duty_shift'] === 'Afternoon') ? 'selected' : ''; ?>>বিকাল</option>
                                        </select>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="text-end">
                                <button type="submit" class="btn btn-primary">ম্যানুয়াল টেস্ট সাবমিট</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4">
                    <a href="duty_assignments_management.php" class="btn btn-secondary">ফিরে যান</a>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const saveChangesBtn = document.getElementById('saveChangesBtn');
            const bulkUpdateForm = document.getElementById('bulkUpdateForm');
            const debugBtn = document.getElementById('debugBtn');
            
            if (saveChangesBtn && bulkUpdateForm) {
                saveChangesBtn.addEventListener('click', function() {
                    console.log('Save button clicked');
                    
                    try {
                        // Set the action field
                        const actionField = bulkUpdateForm.querySelector('input[name="action"]');
                        if (actionField) {
                            actionField.value = 'bulk_update_individual';
                            console.log('Action set to: ' + actionField.value);
                        }
                        
                        // Add force message field
                        let msgField = bulkUpdateForm.querySelector('input[name="force_message"]');
                        if (!msgField) {
                            msgField = document.createElement('input');
                            msgField.type = 'hidden';
                            msgField.name = 'force_message';
                            bulkUpdateForm.appendChild(msgField);
                        }
                        msgField.value = 'ডিউটি তথ্য সফলভাবে সংরক্ষণ করা হয়েছে!';
                        
                        // Show saving indicator
                        saveChangesBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> সংরক্ষণ হচ্ছে...';
                        saveChangesBtn.disabled = true;
                        
                        // Submit the form
                        console.log('Submitting form...');
                        bulkUpdateForm.submit();
                    } catch (error) {
                        console.error('Error submitting form:', error);
                        alert('ফর্ম সাবমিট করতে সমস্যা হয়েছে: ' + error.message);
                    }
                });
            }
            
            if (debugBtn && bulkUpdateForm) {
                debugBtn.addEventListener('click', function() {
                    console.log('Debug button clicked');
                    
                    // Create debug form
                    const debugForm = document.createElement('form');
                    debugForm.method = 'POST';
                    debugForm.action = 'debug_form_submission.php';
                    
                    // Copy all form fields from bulkUpdateForm
                    const formData = new FormData(bulkUpdateForm);
                    for (const [name, value] of formData.entries()) {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = name;
                        input.value = value;
                        debugForm.appendChild(input);
                    }
                    
                    // Add debug flag
                    const debugFlag = document.createElement('input');
                    debugFlag.type = 'hidden';
                    debugFlag.name = 'debug_mode';
                    debugFlag.value = '1';
                    debugForm.appendChild(debugFlag);
                    
                    // Submit to debug page
                    document.body.appendChild(debugForm);
                    debugForm.submit();
                });
            }
        });
    </script>
</body>
</html> 