<?php
require_once __DIR__ . '/models/Student.php';

// Get parameters
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$searchName = $_GET['search'] ?? '';
$subjectCode = $_GET['subject'] ?? '';
$groupName = $_GET['group'] ?? '';

// Validate cards per page
if ($cardsPerPage < 1 || $cardsPerPage > 30) {
    $cardsPerPage = 12;
}

// Get students
$student = new Student();
$students = [];
$error = '';

try {
    // Start with all students
    $students = $student->getAll();

    // Apply subject filter
    if (!empty($subjectCode)) {
        $students = $student->getStudentsWithSubjectCode($subjectCode);
    }

    // Apply additional filters
    if (!empty($searchName)) {
        $students = array_filter($students, function($s) use ($searchName) {
            return stripos($s['student_name'], $searchName) !== false;
        });
    }

    if (!empty($groupName)) {
        $students = array_filter($students, function($s) use ($groupName) {
            return stripos($s['group_name'], $groupName) !== false;
        });
    }

    // Convert filtered array back to indexed array
    $students = array_values($students);

} catch (Exception $e) {
    $error = $e->getMessage();
    $students = [];
}

$totalStudents = count($students);
$totalPages = ceil($totalStudents / $cardsPerPage);
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classic Seat Cards - HSC Exam 2025</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 8mm;
            height: 277mm;
        }
        
        .seat-card {
            border: 3px double #333;
            padding: 10px;
            background: #fefefe;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
            height: 65mm;
        }
        
        .card-header {
            background: #f0f0f0;
            border: 1px solid #333;
            padding: 6px;
            margin: -10px -10px 8px -10px;
            border-bottom: 2px solid #333;
        }
        
        .exam-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 2px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .college-info {
            font-size: 10px;
            color: #666;
            font-style: italic;
        }
        
        .student-name {
            font-size: 15px;
            font-weight: 600;
            color: #333;
            margin: 8px 0;
            line-height: 1.2;
            min-height: 38px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-bottom: 1px dotted #999;
            padding-bottom: 4px;
        }
        
        .roll-number {
            background: #f8f8f8;
            border: 2px solid #333;
            padding: 10px;
            margin: 8px 0;
            font-size: 24px;
            font-weight: bold;
            color: #333;
            position: relative;
        }
        
        .roll-number::before {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            border: 1px solid #666;
        }
        
        .student-details {
            font-size: 11px;
            color: #555;
            line-height: 1.3;
            border-top: 1px solid #ddd;
            padding-top: 4px;
        }
        
        .detail-row {
            margin: 2px 0;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px dotted #ddd;
            padding-bottom: 1px;
        }
        
        .label {
            font-weight: 600;
            color: #333;
        }
        
        .value {
            font-weight: 500;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                padding: 10mm;
                box-shadow: none;
            }
            
            .seat-card {
                border: 3px double #000 !important;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .card-header {
                background: #f0f0f0 !important;
                border: 1px solid #000 !important;
                border-bottom: 2px solid #000 !important;
            }
            
            .roll-number {
                background: #f8f8f8 !important;
                border: 2px solid #000 !important;
                color: #000 !important;
            }
            
            .roll-number::before {
                border: 1px solid #000 !important;
            }
            
            .exam-title, .student-name, .label {
                color: #000 !important;
            }
        }
        
        @page {
            size: A4;
            margin: 0;
        }
    </style>
</head>
<body>
    <?php if ($totalStudents > 0): ?>
        <?php for ($page = 0; $page < $totalPages; $page++): ?>
            <div class="page">
                <div class="cards-container">
                    <?php 
                    $startIndex = $page * $cardsPerPage;
                    $endIndex = min($startIndex + $cardsPerPage, $totalStudents);
                    
                    for ($i = $startIndex; $i < $endIndex; $i++): 
                        $s = $students[$i];
                    ?>
                        <div class="seat-card">
                            <div class="card-header">
                                <div class="exam-title">HSC Exam-2025</div>
                                <div class="college-info">দামুড়হুদা, কোড. 295</div>
                            </div>
                            
                            <div class="student-name">
                                <?php echo htmlspecialchars($s['student_name']); ?>
                            </div>
                            
                            <div class="roll-number">
                                <?php echo htmlspecialchars($s['roll_no']); ?>
                            </div>
                            
                            <div class="student-details">
                                <div class="detail-row">
                                    <span class="label">রেজিঃ</span>
                                    <span class="value"><?php echo htmlspecialchars($s['reg_no']); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">বিভাগ:</span>
                                    <span class="value"><?php echo htmlspecialchars($s['group_name']); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="label">শিক্ষাবর্ষ:</span>
                                    <span class="value"><?php echo htmlspecialchars($s['session']); ?></span>
                                </div>
                            </div>
                        </div>
                    <?php endfor; ?>
                    
                    <?php 
                    // Fill remaining slots with empty cards if needed
                    $remainingSlots = $cardsPerPage - ($endIndex - $startIndex);
                    for ($j = 0; $j < $remainingSlots; $j++): 
                    ?>
                        <div class="seat-card" style="border: 2px dashed #ccc; background: #f9f9f9;">
                            <div style="color: #ccc; font-size: 12px; margin: auto;">Empty</div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
        <?php endfor; ?>
    <?php else: ?>
        <div class="page">
            <div style="text-align: center; margin-top: 100px; font-size: 18px; color: #666;">
                কোন শিক্ষার্থী পাওয়া যায়নি
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
