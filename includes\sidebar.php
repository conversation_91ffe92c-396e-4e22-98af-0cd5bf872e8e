<?php
// Simple sidebar with basic subject filtering
$currentPage = basename($_SERVER['PHP_SELF']);

$successMessage = $_GET['success'] ?? '';
$errorMessage = $_GET['error'] ?? '';

if (!empty($successMessage)) {
    header('Location: duty_assignments_management.php?success=' . urlencode($successMessage));
    exit;
}
if (!empty($errorMessage)) {
    header('Location: duty_assignments_management.php?error=' . urlencode($errorMessage));
    exit;
}
?>

<style>
.sidebar {
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    transform: translateX(-280px);
}

.sidebar-header {
    padding: 1rem;
    background: rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-nav {
    padding: 0;
}

.sidebar-nav .nav-item {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-nav .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    border: none;
    transition: all 0.3s ease;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background: rgba(255,255,255,0.1);
}

.sidebar-nav .nav-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    border-left: 4px solid #ffc107;
}

.sidebar-toggle {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1001;
    background: #667eea;
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 5px;
    transition: left 0.3s ease;
}

.sidebar-toggle.moved {
    left: 290px;
}

.main-content {
    margin-left: 280px;
    transition: margin-left 0.3s ease;
}

.main-content.expanded {
    margin-left: 0;
}

.subject-category {
    background: rgba(0,0,0,0.1);
    margin: 0.5rem 0;
    border-radius: 5px;
}

.subject-category-header {
    padding: 0.5rem 1rem;
    font-weight: bold;
    font-size: 0.9rem;
    background: rgba(0,0,0,0.1);
    cursor: pointer;
}

.subject-links {
    padding: 0.5rem 0;
}

.subject-link {
    display: block;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 0.4rem 1.5rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.subject-link:hover {
    color: white;
    background: rgba(255,255,255,0.1);
    text-decoration: none;
}

.subject-link.active {
    color: white;
    background: rgba(255,255,255,0.2);
    border-left: 3px solid #ffc107;
}

.subject-count {
    float: right;
    background: rgba(255,255,255,0.2);
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    font-size: 0.7rem;
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-280px);
        box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        left: 10px;
        background: #667eea !important;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .sidebar-toggle.moved {
        left: 10px;
    }
}
</style>

<!-- Sidebar Toggle Button -->
<button class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebar()" title="Toggle Sidebar">
    <i class="fas fa-bars"></i>
</button>

<!-- Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <h5 class="mb-1">📚 EXMM System</h5>
        <small class="text-light">Student Management</small>
    </div>
    
    <!-- Main Navigation -->
    <ul class="sidebar-nav nav flex-column">
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'index.php') ? 'active' : ''; ?>" href="index.php">
                🏠 Home
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'view_students.php') ? 'active' : ''; ?>" href="view_students.php">
                👥 All Students
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'add_student.php') ? 'active' : ''; ?>" href="add_student.php">
                ➕ Add Student
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'subject_filter.php') ? 'active' : ''; ?>" href="subject_filter.php">
                🔍 Filter by Subject
            </a>
        </li>
        
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'teacher_wise_duty_management.php') ? 'active' : ''; ?>" href="teacher_wise_duty_management.php">
                📋 শিক্ষক অনুযায়ী ডিউটি
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'seat_card_generator.php') ? 'active' : ''; ?>" href="seat_card_generator.php">
                🎫 Seat Card Generator
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'thirteen_subject_students.php') ? 'active' : ''; ?>" href="thirteen_subject_students.php">
                🎓 ১৩ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'incomplete_subject_students.php') ? 'active' : ''; ?>" href="incomplete_subject_students.php">
                📝 অসম্পূর্ণ বিষয়ের শিক্ষার্থী
            </a>
        </li>

        <!-- Subject Count Based Pages -->
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'one_subject_students.php') ? 'active' : ''; ?>" href="one_subject_students.php">
                1️⃣ ১ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'two_subject_students.php') ? 'active' : ''; ?>" href="two_subject_students.php">
                2️⃣ ২ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'three_subject_students.php') ? 'active' : ''; ?>" href="three_subject_students.php">
                3️⃣ ৩ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'four_subject_students.php') ? 'active' : ''; ?>" href="four_subject_students.php">
                4️⃣ ৪ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'five_subject_students.php') ? 'active' : ''; ?>" href="five_subject_students.php">
                5️⃣ ৫ বিষয়ের শিক্ষার্থী
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'bulk_seat_cards.php') ? 'active' : ''; ?>" href="bulk_seat_cards.php">
                📄 Bulk Seat Cards
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'custom_seat_cards.php') ? 'active' : ''; ?>" href="custom_seat_cards.php">
                🎛️ Custom Cards
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'dynamic_seat_plan.php') ? 'active' : ''; ?>" href="dynamic_seat_plan.php">
                🏢 ডাইনামিক সীট প্লান
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'group_seat_card_generator.php') ? 'active' : ''; ?>" href="group_seat_card_generator.php">
                🎫 অসম্পূর্ণ গ্রুপ সীট কার্ড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'complete_group_seat_card_generator.php') ? 'active' : ''; ?>" href="complete_group_seat_card_generator.php">
                ✅ সম্পূর্ণ গ্রুপ সীট কার্ড
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'teacher_status.php') ? 'active' : ''; ?>" href="teacher_status.php">
                <i class="fas fa-user-check"></i> শিক্ষক স্ট্যাটাস
            </a>
        </li>

        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'subject_code_incomplete_students.php') ? 'active' : ''; ?>" href="subject_code_incomplete_students.php">
                📝 কোড ভিত্তিক অসম্পূর্ণ শিক্ষার্থী
            </a>
        </li>

        <!-- Teacher Honorarium Calculator -->
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'final_teacher_honorarium_fix.php' || $currentPage === 'teacher_honorarium_calculator.php' || $currentPage === 'teacher_honorarium_fix.php') ? 'active' : ''; ?>" href="final_teacher_honorarium_fix.php">
                <i class="fas fa-calculator me-2"></i>
                শিক্ষক সন্মানী হিসাব
            </a>
        </li>

        <!-- Database Tools -->
        <li class="nav-item">
            <a class="nav-link <?php echo ($currentPage === 'database_field_explorer.php' || $currentPage === 'db_structure_check.php' || $currentPage === 'test_adaptive_query.php') ? 'active' : ''; ?>" href="database_field_explorer.php">
                <i class="fas fa-database me-2"></i> ডাটাবেস টুল
            </a>
        </li>
    </ul>

    <!-- Teacher Management Section -->
    <div class="px-3 mt-3">
        <h6 class="text-light mb-2">👨‍🏫 শিক্ষক ব্যবস্থাপনা:</h6>
        <ul class="sidebar-nav nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'teacher_duty_management.php') ? 'active' : ''; ?>" href="teacher_duty_management.php">
                    📋 ডিউটি ব্যবস্থাপনা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'room_wise_duty_assignment.php') ? 'active' : ''; ?>" href="room_wise_duty_assignment.php">
                    🏢 রুম ওয়াইজ ডিউটি
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'room_distribution.php') ? 'active' : ''; ?>" href="room_distribution.php">
                    🚪 তারিখ ভিত্তিক রুম বন্টন
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'teacher_list_view.php') ? 'active' : ''; ?>" href="teacher_list_view.php">
                    👥 শিক্ষক তালিকা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'duty_assignments_list.php') ? 'active' : ''; ?>" href="duty_assignments_list.php">
                    📅 বন্টনকৃত ডিউটি তালিকা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'duty_assignments_management.php') ? 'active' : ''; ?>" href="duty_assignments_management.php">
                    ⚙️ ডিউটি বন্টন ব্যবস্থাপনা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'date_wise_duty_assignment.php') ? 'active' : ''; ?>" href="date_wise_duty_assignment.php">
                    🗓️ তারিখ ভিত্তিক ডিউটি বন্টন
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'reset_duty_assignment.php') ? 'active' : ''; ?>" href="reset_duty_assignment.php">
                    <i class="fas fa-trash-alt me-2"></i>ডিউটি রিসেট করুন
                </a>
            </li>
        </ul>
    </div>

    <!-- Document Generation Section -->
    <div class="px-3 mt-3">
        <h6 class="text-light mb-2">📄 ডকুমেন্ট জেনারেশন:</h6>
        <ul class="sidebar-nav nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'duty_letter_generator.php') ? 'active' : ''; ?>" href="duty_letter_generator.php">
                    📝 ডিউটি লেটার জেনারেটর
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'individual_duty_letter_generator.php') ? 'active' : ''; ?>" href="individual_duty_letter_generator.php">
                    📋 ব্যক্তিগত ডিউটি লেটার
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'teacher_id_card_generator.php') ? 'active' : ''; ?>" href="teacher_id_card_generator.php">
                    🆔 তারিখ ভিত্তিক আইডি কার্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'all_teachers_id_cards.php') ? 'active' : ''; ?>" href="all_teachers_id_cards.php">
                    🆔 সকল শিক্ষকের আইডি কার্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'print_teachers_list.php') ? 'active' : ''; ?>" href="print_teachers_list.php" target="_blank">
                    <i class="fas fa-print me-2"></i>কক্ষ পরিদর্শকের তালিকা
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'print_date_wise_duty.php') ? 'active' : ''; ?>" href="print_date_wise_duty.php">
                    <i class="fas fa-calendar-alt me-2"></i>তারিখ ভিত্তিক ডিউটি লিস্ট
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'signature_upload.php') ? 'active' : ''; ?>" href="signature_upload.php">
                    ✍️ স্বাক্ষর আপলোড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'logo_upload.php') ? 'active' : ''; ?>" href="logo_upload.php">
                    🖼️ কলেজ লোগো আপলোড
                </a>
            </li>
        </ul>
    </div>

    <!-- Tools Section -->
    <div class="px-3 mt-3">
        <h6 class="text-light mb-2">🔧 Tools:</h6>
        <ul class="sidebar-nav nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'upload.php') ? 'active' : ''; ?>" href="upload.php">
                    📤 Upload Excel
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage === 'csv_upload.php') ? 'active' : ''; ?>" href="csv_upload.php">
                    📄 CSV Upload
                </a>
            </li>
        </ul>
    </div>

    <!-- Sidebar Footer -->
    <div class="px-3 py-2 mt-auto" style="border-top: 1px solid rgba(255,255,255,0.1);">
        <small class="text-light d-block text-center">
            <strong>EXMM v2.0</strong><br>
            Student Management<br>
            System
        </small>
    </div>
</div>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const toggleBtn = document.getElementById('sidebarToggle');
    
    sidebar.classList.toggle('collapsed');
    if (mainContent) {
        mainContent.classList.toggle('expanded');
    }
    toggleBtn.classList.toggle('moved');
}

function toggleCategory(categoryId) {
    const category = document.getElementById(categoryId);
    const header = category.previousElementSibling;
    const arrow = header.querySelector('.float-end');
    
    if (category.style.display === 'none') {
        category.style.display = 'block';
        arrow.textContent = '▼';
    } else {
        category.style.display = 'none';
        arrow.textContent = '▶';
    }
}

// Auto-hide sidebar on mobile and initialize
function initializeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    const toggleBtn = document.getElementById('sidebarToggle');

    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        if (mainContent) {
            mainContent.classList.add('expanded');
        }
        if (toggleBtn) {
            toggleBtn.classList.remove('moved');
        }
    }
}

// Initialize on load
initializeSidebar();

// Re-initialize on window resize
window.addEventListener('resize', function() {
    initializeSidebar();
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    if (window.innerWidth <= 768) {
        const sidebar = document.getElementById('sidebar');
        const toggleBtn = document.getElementById('sidebarToggle');
        
        if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
            sidebar.classList.add('collapsed');
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.classList.add('expanded');
            }
        }
    }
});
</script>
