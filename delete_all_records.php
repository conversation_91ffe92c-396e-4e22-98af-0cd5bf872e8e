<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
        try {
            $student = new Student();
            if ($student->deleteAll()) {
                $message = 'সকল স্টুডেন্ট রেকর্ড সফলভাবে মুছে ফেলা হয়েছে!';
                $messageType = 'success';
            } else {
                $message = 'স্টুডেন্ট রেকর্ড মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get current student count
$student = new Student();
$students = $student->getAll();
$totalStudents = count($students);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সকল রেকর্ড মুছুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background: rgba(220, 53, 69, 0.1);
        }
        .warning-icon {
            font-size: 4rem;
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card danger-zone">
                        <div class="card-header bg-danger text-white text-center">
                            <h3><i class="fas fa-exclamation-triangle"></i> বিপজ্জনক এলাকা</h3>
                            <p class="mb-0">সকল স্টুডেন্ট রেকর্ড মুছে ফেলুন</p>
                        </div>
                        <div class="card-body text-center">
                            <?php if ($message): ?>
                                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                    <?php echo htmlspecialchars($message); ?>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            <?php endif; ?>

                            <div class="warning-icon mb-4">
                                <i class="fas fa-skull-crossbones"></i>
                            </div>

                            <h4 class="text-danger mb-4">⚠️ সতর্কতা ⚠️</h4>
                            
                            <div class="alert alert-warning">
                                <h5><i class="fas fa-info-circle"></i> বর্তমান অবস্থা:</h5>
                                <p class="mb-0">
                                    <strong>মোট স্টুডেন্ট:</strong> <?php echo $totalStudents; ?> জন
                                </p>
                            </div>

                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle"></i> এই অ্যাকশনটি:</h5>
                                <ul class="list-unstyled mb-0">
                                    <li>✗ সকল স্টুডেন্ট রেকর্ড স্থায়ীভাবে মুছে ফেলবে</li>
                                    <li>✗ এই অ্যাকশন আর ফিরিয়ে আনা যাবে না</li>
                                    <li>✗ সকল ডেটা হারিয়ে যাবে</li>
                                </ul>
                            </div>

                            <?php if ($totalStudents > 0): ?>
                                <form method="POST" onsubmit="return confirmDelete()">
                                    <div class="mb-4">
                                        <div class="form-check d-inline-block">
                                            <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                                            <label class="form-check-label text-danger fw-bold" for="confirmCheck">
                                                আমি বুঝতে পারছি যে এই অ্যাকশনটি সকল ডেটা মুছে ফেলবে
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <input type="hidden" name="confirm_delete" value="yes">
                                    <button type="submit" class="btn btn-danger btn-lg me-3" id="deleteBtn" disabled>
                                        <i class="fas fa-trash-alt"></i> সকল রেকর্ড মুছে ফেলুন
                                    </button>
                                </form>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i> কোন স্টুডেন্ট রেকর্ড নেই।
                                </div>
                            <?php endif; ?>

                            <a href="view_students.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-arrow-left"></i> ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enable delete button only when checkbox is checked
        document.getElementById('confirmCheck').addEventListener('change', function() {
            document.getElementById('deleteBtn').disabled = !this.checked;
        });

        function confirmDelete() {
            return confirm('আপনি কি নিশ্চিত যে আপনি সকল স্টুডেন্ট রেকর্ড মুছে ফেলতে চান?\n\nএই অ্যাকশনটি আর ফিরিয়ে আনা যাবে না!');
        }
    </script>
</body>
</html>
