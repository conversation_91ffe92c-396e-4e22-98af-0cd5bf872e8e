<?php
require_once 'includes/teacher_db.php';

// Get all teachers from database
$teachers = $teacherManager->getAllTeachers();

if (empty($teachers)) {
    die('কোন শিক্ষক পাওয়া যায়নি!');
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক তালিকা - প্রিন্ট</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        body { 
            font-family: 'Hind Siliguri', sans-serif; 
            margin: 0;
            padding: 20px;
            background: white;
            color: #000;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .college-logo {
            position: absolute;
            top: 0;
            left: 20px;
            width: 60px;
            height: auto;
        }
        
        .college-name {
            font-size: 22px;
            font-weight: bold;
            margin: 0;
        }
        
        .college-address {
            font-size: 16px;
            margin: 5px 0;
        }
        
        .page-title {
            font-size: 18px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #333;
            padding: 8px 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .teacher-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .photo-placeholder {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #eee;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #777;
        }
        
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Hind Siliguri', sans-serif;
            font-size: 14px;
            z-index: 1000;
        }
        
        .print-button:hover {
            background: #0056b3;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: #555;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .container {
                width: 100%;
            }
            
            table {
                page-break-inside: auto;
            }
            
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            thead {
                display: table-header-group;
            }
            
            tfoot {
                display: table-footer-group;
            }
        }
    </style>
</head>
<body>
    <!-- Print Button -->
    <button onclick="window.print()" class="print-button no-print">
        🖨️ প্রিন্ট করুন
    </button>

    <div class="container">
        <div class="header">
            <?php if (file_exists('uploads/college_logo.jpg')): ?>
                <img src="uploads/college_logo.jpg" alt="College Logo" class="college-logo">
            <?php endif; ?>
            
            <h1 class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h1>
            <p class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</p>
            <h2 class="page-title">HSC পরীক্ষা -২০২৫</h2>
            <h2 class="page-title">কক্ষ পরিদর্শকের তালিকা</h2>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>ক্রমিক</th>
                    <th>ছবি</th>
                    <th>নাম</th>
                    <th>পদবি</th>
                    <th>বিষয়</th>
                    <th>মোবাইল</th>
                    <th>স্বাক্ষর</th>
                </tr>
            </thead>
            <tbody>
                <?php $serial = 1; ?>
                <?php foreach ($teachers as $teacher): ?>
                    <tr>
                        <td><?php echo $serial++; ?></td>
                        <td>
                            <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="Teacher Photo" class="teacher-photo">
                            <?php else: ?>
                                <div class="photo-placeholder">
                                    <span>👤</span>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                        <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                        <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                        <td><?php echo htmlspecialchars($teacher['mobile'] ?? 'N/A'); ?></td>
                        <td></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="footer">
            <p>মোট শিক্ষক: <?php echo count($teachers); ?> জন</p>
            <p>প্রিন্ট তারিখ: <?php echo date('d/m/Y'); ?></p>
        </div>
    </div>

    <script>
        // Print function
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>