<?php
// Working Seat Card Generator
require_once __DIR__ . '/config/database.php';

// Get parameters
$subjectCode = $_GET['subject'] ?? '';
$searchName = $_GET['search'] ?? '';
$searchRoll = $_GET['search_roll'] ?? '';
$groupName = $_GET['group'] ?? '';
$studentType = $_GET['type'] ?? '';
$rollNumbers = $_GET['rolls'] ?? '';
$gender = $_GET['gender'] ?? '';
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$printList = isset($_GET['print_list']); // Check if print list mode is enabled

// Database connection
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Build query
    $sql = "SELECT * FROM students WHERE 1=1";
    $params = [];
    
    // Add subject filter
    if (!empty($subjectCode)) {
        $sql .= " AND subjects LIKE :subject_pattern";
        $params['subject_pattern'] = '%' . $subjectCode . '%';
    }
    
    // Add name filter
    if (!empty($searchName)) {
        $sql .= " AND name LIKE :name";
        $params['name'] = '%' . $searchName . '%';
    }
    
    // Add roll filter
    if (!empty($searchRoll)) {
        $sql .= " AND roll LIKE :roll";
        $params['roll'] = '%' . $searchRoll . '%';
    }
    
    // Add group filter
    if (!empty($groupName)) {
        $sql .= " AND department LIKE :group";
        $params['group'] = '%' . $groupName . '%';
    }

    // Add type filter
    if (!empty($studentType)) {
        $sql .= " AND student_type = :type";
        $params['type'] = $studentType;
    }

    // Add gender filter
    if (!empty($gender)) {
        $sql .= " AND gender = :gender";
        $params['gender'] = $gender;
    }

    // Add roll numbers filter
    if (!empty($rollNumbers)) {
        $rollArray = explode(',', $rollNumbers);
        $rollArray = array_map('trim', $rollArray);
        $rollArray = array_filter($rollArray);

        if (!empty($rollArray)) {
            $placeholders = [];
            foreach ($rollArray as $index => $roll) {
                $placeholder = 'roll' . $index;
                $placeholders[] = ':' . $placeholder;
                $params[$placeholder] = $roll;
            }
            $sql .= " AND roll IN (" . implode(',', $placeholders) . ")";
        }
    }

    // Filter incomplete students (less than 13 subjects)
    if (isset($_GET['incomplete_only'])) {
        $sql .= " AND (subjects IS NULL OR subjects = '' OR LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1 < 13)";
    }

    // Filter complete students (all 13 subjects)
    if (isset($_GET['complete_only'])) {
        $sql .= " AND subjects IS NOT NULL AND subjects != '' AND LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1 = 13";
    }

    $sql .= " ORDER BY CAST(roll AS UNSIGNED) ASC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);


    
} catch (Exception $e) {
    $students = [];
    $error = $e->getMessage();
}

$totalStudents = count($students);
$totalPages = ceil($totalStudents / $cardsPerPage);

// Debug information
$debugInfo = [
    'subjectCode' => $subjectCode,
    'searchName' => $searchName,
    'searchRoll' => $searchRoll,
    'groupName' => $groupName,
    'studentType' => $studentType,
    'rollNumbers' => $rollNumbers,
    'gender' => $gender,
    'totalStudents' => $totalStudents,
    'sql' => $sql ?? 'N/A',
    'params' => $params ?? []
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HSC Exam 2025 - <?php echo $printList ? 'Students List' : 'Seat Cards'; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: white;
            color: #333;
        }
        
        <?php if (!$printList): // Styles for seat cards ?>
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 10mm;
            background: white;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(4, 1fr);
            gap: 8mm;
            height: 277mm;
        }
        
        .seat-card {
            border: 2px solid #333;
            border-radius: 8px;
            padding: 8px;
            background: #fafafa;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            position: relative;
            height: 65mm;
        }
        
        .card-header {
            border-bottom: 1px solid #666;
            padding-bottom: 4px;
            margin-bottom: 6px;
        }
        
        .exam-title {
            font-size: 14px;
            font-weight: bold;
            color: black;
            margin-bottom: 2px;
        }
        
        .college-info {
            font-size: 10px;
            color: #666;
        }
        
        .student-name {
            font-size: 16px;
            font-weight: bold;
            color: black;
            margin: 8px 0;
            line-height: 1.2;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .roll-number {
            background: #f0f0f0;
            border: 2px solid black;
            border-radius: 6px;
            padding: 8px;
            margin: 8px 0;
            font-size: 24px;
            font-weight: bold;
            color: black;
        }
        
        .student-details {
            font-size: 11px;
            color: black;
            line-height: 1.3;
        }
        
        .detail-row {
            margin: 2px 0;
        }
        
        .label {
            font-weight: 600;
        }
        
        .no-students {
            text-align: center;
            margin-top: 100px;
            font-size: 18px;
            color: #666;
        }
        <?php else: // Styles for print list ?>
        .page {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 15mm;
            background: white;
            page-break-after: always;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 15mm;
        }
        
        .college-name {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .exam-title {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .list-title {
            font-size: 16px;
            margin-bottom: 15px;
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        table th, table td {
            border: 1px solid #333;
            padding: 5px 8px;
            font-size: 12px;
        }
        
        table th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        table td {
            vertical-align: middle;
        }
        
        .text-center {
            text-align: center;
        }
        
        .badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 10px;
            color: white;
            background-color: #6c757d;
            margin: 1px;
        }
        
        .badge-primary { background-color: #007bff; }
        .badge-success { background-color: #28a745; }
        .badge-danger { background-color: #dc3545; }
        .badge-warning { background-color: #ffc107; color: #333; }
        .badge-info { background-color: #17a2b8; }
        
        .page-number {
            text-align: right;
            font-size: 10px;
            margin-top: 8mm;
        }
        
        /* Print specific styles */
        @media print {
            table th {
                background-color: #e9e9e9 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .badge {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        <?php endif; ?>
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                box-shadow: none;
            }
            
            .seat-card {
                border: 2px solid #000 !important;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .exam-title, .roll-number {
                color: #000 !important;
            }
        }
        
        @page {
            size: A4;
            margin: 0;
        }

        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px;
            font-family: monospace;
            font-size: 12px;
        }

        .debug-info h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        @media print {
            .debug-info {
                display: none;
            }
        }
    </style>
</head>
<body>

    <!-- Debug Information (hidden in print) -->
    <?php if (isset($_GET['debug'])): ?>
    <div class="debug-info">
        <h4>ডিবাগ তথ্য</h4>
        <strong>ফিল্টার প্যারামিটার:</strong><br>
        - Subject Code: <?php echo htmlspecialchars($debugInfo['subjectCode'] ?: 'None'); ?><br>
        - Search Name: <?php echo htmlspecialchars($debugInfo['searchName'] ?: 'None'); ?><br>
        - Group Name: <?php echo htmlspecialchars($debugInfo['groupName'] ?: 'None'); ?><br>
        - Student Type: <?php echo htmlspecialchars($debugInfo['studentType'] ?: 'None'); ?><br>
        - Gender: <?php echo htmlspecialchars($debugInfo['gender'] ?: 'None'); ?><br>
        - Roll Numbers: <?php echo htmlspecialchars($debugInfo['rollNumbers'] ?: 'None'); ?><br>
        <br>
        <strong>ফলাফল:</strong><br>
        - Total Students Found: <?php echo $debugInfo['totalStudents']; ?><br>
        - Total Pages: <?php echo $totalPages; ?><br>
        - Cards Per Page: <?php echo $cardsPerPage; ?><br>
        <br>
        <strong>SQL Query:</strong><br>
        <pre><?php echo htmlspecialchars($debugInfo['sql']); ?></pre>
        <br>
        <strong>Parameters:</strong><br>
        <pre><?php print_r($debugInfo['params']); ?></pre>

        <?php if (!empty($students)): ?>
        <br>
        <strong>প্রথম ৫ জন শিক্ষার্থী:</strong><br>
        <?php foreach (array_slice($students, 0, 5) as $s): ?>
            - রোল: <?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? 'N/A'); ?>,
              নাম: <?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? 'N/A'); ?>,
              গ্রুপ: <?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? 'N/A'); ?>,
              টাইপ: <?php echo htmlspecialchars($s['student_type'] ?? $s['type'] ?? 'N/A'); ?><br>
        <?php endforeach; ?>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($totalStudents > 0): ?>
        <?php if ($printList): // Print List Mode ?>
            <?php
            // Constants for print list mode
            $studentsPerPage = 25; 
            $listTotalPages = ceil($totalStudents / $studentsPerPage);
            ?>
            
            <?php for ($page = 0; $page < $listTotalPages; $page++): ?>
                <div class="page">
                    <div class="page-header">
                        <div class="college-name">দামুড়হুদা ডিগ্রী কলেজ</div>
                        <div class="exam-title">HSC পরীক্ষা - ২০২৫</div>
                        <div class="list-title">অসম্পূর্ণ বিষয়ের শিক্ষার্থী তালিকা</div>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th width="5%">ক্রম</th>
                                <th width="10%">রোল নং</th>
                                <th width="20%">নাম</th>
                                <th width="10%">রেজিস্ট্রেশন</th>
                                <th width="10%">গ্রুপ</th>
                                <th width="7%">ধরণ</th>
                                <th width="8%">বিষয়</th>
                                <th>বিষয় কোড</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $startIndex = $page * $studentsPerPage;
                            $endIndex = min($startIndex + $studentsPerPage, $totalStudents);
                            
                            for ($i = $startIndex; $i < $endIndex; $i++): 
                                $s = $students[$i];
                                
                                // Count subjects
                                $subjectCount = 0;
                                $subjectList = [];
                                
                                if (!empty($s['subjects'])) {
                                    $subjects = explode(',', $s['subjects']);
                                    $subjectList = array_filter(array_map('trim', $subjects));
                                    $subjectCount = count($subjectList);
                                } else {
                                    // Check old format
                                    for ($j = 1; $j <= 13; $j++) {
                                        $subField = 'sub_' . $j;
                                        if (!empty($s[$subField])) {
                                            $subjectCount++;
                                            $subjectList[] = $s[$subField];
                                        }
                                    }
                                }
                            ?>
                                <tr>
                                    <td class="text-center"><?php echo $i + 1; ?></td>
                                    <td class="text-center"><?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($s['registration'] ?? $s['reg_no'] ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? 'N/A'); ?></td>
                                    <td class="text-center">
                                        <?php
                                        $type = $s['student_type'] ?? $s['type'] ?? 'Regular';
                                        $badgeClass = '';
                                        switch($type) {
                                            case 'Regular': $badgeClass = 'badge-success'; break;
                                            case 'Irregular': $badgeClass = 'badge-danger'; break;
                                            case 'Improvement': $badgeClass = 'badge-warning'; break;
                                            default: $badgeClass = 'badge-secondary';
                                        }
                                        ?>
                                        <span class="badge <?php echo $badgeClass; ?>"><?php echo $type; ?></span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-primary"><?php echo $subjectCount; ?>/13</span>
                                    </td>
                                    <td>
                                        <?php foreach($subjectList as $subj): ?>
                                            <span class="badge badge-info"><?php echo $subj; ?></span>
                                        <?php endforeach; ?>
                                    </td>
                                </tr>
                            <?php endfor; ?>
                        </tbody>
                    </table>
                    
                    <div class="page-number">পৃষ্ঠা <?php echo ($page + 1); ?> / <?php echo $listTotalPages; ?></div>
                </div>
            <?php endfor; ?>
            
        <?php else: // Seat Cards Mode ?>
            <?php for ($page = 0; $page < $totalPages; $page++): ?>
                <div class="page">
                    <div class="cards-container">
                        <?php 
                        $startIndex = $page * $cardsPerPage;
                        $endIndex = min($startIndex + $cardsPerPage, $totalStudents);
                        
                        for ($i = $startIndex; $i < $endIndex; $i++): 
                            $s = $students[$i];
                        ?>
                            <div class="seat-card">
                                <div class="card-header">
                                    <div class="exam-title">HSC Exam-2025</div>
                                    <div class="college-info">দামুড়হুদা, কোড. 295</div>
                                </div>
                                
                                <div class="student-name">
                                    <?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? ''); ?>
                                </div>

                                <div class="roll-number">
                                    <?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? ''); ?>
                                </div>

                                <div class="student-details">
                                    <div class="detail-row">
                                        <span class="label">রেজিঃ</span> <?php echo htmlspecialchars($s['registration'] ?? $s['reg_no'] ?? ''); ?>
                                    </div>
                                    <div class="detail-row">
                                        <span class="label">বিভাগ:</span> <?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? ''); ?>
                                    </div>
                                    <div class="detail-row">
                                        <span class="label">শিক্ষাবর্ষ:</span> <?php echo htmlspecialchars($s['academic_year'] ?? $s['session'] ?? ''); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endfor; ?>
                        
                        <?php 
                        // Fill remaining slots with empty cards if needed
                        $remainingSlots = $cardsPerPage - ($endIndex - $startIndex);
                        for ($j = 0; $j < $remainingSlots; $j++): 
                        ?>
                            <div class="seat-card" style="border: 1px dashed #ccc; background: #f9f9f9;">
                                <div style="color: #ccc; font-size: 14px; margin: auto;">Empty</div>
                            </div>
                        <?php endfor; ?>
                    </div>
                </div>
            <?php endfor; ?>
        <?php endif; ?>
    <?php else: ?>
        <div class="page">
            <div class="no-students">
                <?php if (isset($error)): ?>
                    <h3>ডেটাবেস এরর</h3>
                    <p><?php echo htmlspecialchars($error); ?></p>
                <?php else: ?>
                    <h3>কোন শিক্ষার্থী পাওয়া যায়নি</h3>
                    <?php if (!empty($subjectCode)): ?>
                        <p>সাবজেক্ট কোড: <?php echo htmlspecialchars($subjectCode); ?></p>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <script>
        // Auto print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
