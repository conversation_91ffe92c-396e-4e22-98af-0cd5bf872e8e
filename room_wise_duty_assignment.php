<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
$dutyAssignments = $_SESSION['duty_assignments'] ?? [];
$uploadedTeachers = $_SESSION['uploaded_teachers'] ?? [];
$roomAssignments = $_SESSION['room_assignments'] ?? [];

// Load duty assignments from database if session is empty or if uploaded_teachers is empty
try {
    if (empty($dutyAssignments) || empty($uploadedTeachers)) {
        $allDutyDates = $teacherManager->getAllDutyDates();
        $allTeachers = $teacherManager->getAllTeachers();

        // Clear and rebuild uploaded_teachers from database
        $_SESSION['uploaded_teachers'] = [];

        // Create a mapping of teacher ID to array index
        $teacherIdToIndex = [];
        foreach ($allTeachers as $index => $teacher) {
            $teacherIdToIndex[$teacher['id']] = $index;
            $_SESSION['uploaded_teachers'][$index] = $teacher;
        }
        $uploadedTeachers = $_SESSION['uploaded_teachers'];

        // Load duty assignments from database
        $dutyAssignments = [];
        foreach ($allDutyDates as $date) {
            $assignments = $teacherManager->getDutyAssignments($date);
            $teacherIndices = [];
            foreach ($assignments as $assignment) {
                if (isset($teacherIdToIndex[$assignment['teacher_id']])) {
                    $teacherIndices[] = $teacherIdToIndex[$assignment['teacher_id']];
                }
            }
            if (!empty($teacherIndices)) {
                $dutyAssignments[$date] = $teacherIndices;
            }
        }

        // Update session
        $_SESSION['duty_assignments'] = $dutyAssignments;

        if (!empty($dutyAssignments)) {
            $message = 'ডাটাবেস থেকে ' . count($dutyAssignments) . ' টি তারিখের ডিউটি বন্টন লোড করা হয়েছে।';
            $messageType = 'info';
        }
    }
} catch (Exception $e) {
    $message = 'ডেটা লোড করতে সমস্যা হয়েছে: ' . $e->getMessage();
    $messageType = 'danger';
}

// Default rooms
$defaultRooms = [
    'পরীক্ষা হল - ১',
    'পরীক্ষা হল - ২', 
    'পরীক্ষা হল - ৩',
    'পরীক্ষা হল - ৪',
    'কম্পিউটার ল্যাব',
    'লাইব্রেরি হল'
];

// Handle room configuration
if (isset($_POST['save_rooms'])) {
    $roomsText = $_POST['rooms'] ?? '';
    $rooms = array_filter(explode("\n", $roomsText), function($room) {
        return !empty(trim($room));
    });
    $rooms = array_map('trim', $rooms);
    $_SESSION['configured_rooms'] = array_values($rooms);
    $message = 'রুম কনফিগারেশন সেভ হয়েছে!';
    $messageType = 'success';
}

$configuredRooms = $_SESSION['configured_rooms'] ?? $defaultRooms;

// Handle room assignment
if (isset($_POST['save_room_assignment'])) {
    $date = $_POST['assignment_date'];
    $assignments = $_POST['room_assignments'] ?? [];
    
    if (!empty($date)) {
        if (!isset($_SESSION['room_assignments'])) {
            $_SESSION['room_assignments'] = [];
        }
        
        $_SESSION['room_assignments'][$date] = $assignments;
        $roomAssignments = $_SESSION['room_assignments'];
        $message = 'রুম ওয়াইজ ডিউটি বন্টন সেভ হয়েছে!';
        $messageType = 'success';
    }
}

// Remove room assignment
if (isset($_POST['remove_room_assignment'])) {
    $dateToRemove = $_POST['date_to_remove'];
    if (isset($_SESSION['room_assignments'][$dateToRemove])) {
        unset($_SESSION['room_assignments'][$dateToRemove]);
        $roomAssignments = $_SESSION['room_assignments'];
        $message = 'রুম ডিউটি বন্টন মুছে ফেলা হয়েছে!';
        $messageType = 'info';
    }
}

// Get available dates from duty assignments
$availableDates = array_keys($dutyAssignments);
sort($availableDates);

// Get custom dates from database if available
$customDates = $teacherManager->getAllDutyDates();

// Filter dates to show only user-provided dates if any are set
$filteredDates = [];
if (!empty($_SESSION['custom_dates'])) {
    foreach ($availableDates as $date) {
        if (in_array($date, $_SESSION['custom_dates'])) {
            $filteredDates[] = $date;
        }
    }
    if (!empty($filteredDates)) {
        $availableDates = $filteredDates;
    }
}

// Handle teacher edit, update, and remove operations
if (isset($_POST['edit_teacher'])) {
    $teacherId = $_POST['teacher_id'];
    $date = $_POST['date'];
    $teacher = $teacherManager->getTeacherById($teacherId);
    $_SESSION['edit_teacher'] = $teacher;
    $_SESSION['edit_date'] = $date;
    $message = 'শিক্ষকের তথ্য সম্পাদনা করুন';
    $messageType = 'info';
}

if (isset($_POST['update_teacher'])) {
    $teacherId = $_POST['teacher_id'];
    $date = $_POST['date'];
    $roomNumber = $_POST['room_number'];
    
    try {
        $teacherManager->updateTeacherRoom($teacherId, $date, $roomNumber);
        $message = 'শিক্ষকের রুম আপডেট করা হয়েছে!';
        $messageType = 'success';
    } catch (Exception $e) {
        $message = 'আপডেট করতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

if (isset($_POST['remove_teacher'])) {
    $teacherId = $_POST['teacher_id'];
    $date = $_POST['date'];
    
    try {
        $teacherManager->removeTeacherFromDuty($teacherId, $date);
        $message = 'শিক্ষককে ডিউটি থেকে সরানো হয়েছে!';
        $messageType = 'success';
    } catch (Exception $e) {
        $message = 'সরাতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Fetch all duty assignments grouped by room for the latest duty date (or allow date selection)
$dutyDate = $_GET['date'] ?? $teacherManager->getAllDutyDates()[0] ?? date('Y-m-d');
$assignments = $teacherManager->getDutyAssignments($dutyDate);

// Group by room
$roomGroups = [];
foreach ($assignments as $a) {
    $room = $a['room_number'] ?: 'অনির্দিষ্ট';
    if (!isset($roomGroups[$room])) $roomGroups[$room] = [];
    $roomGroups[$room][] = $a;
}
ksort($roomGroups, SORT_NATURAL);

// Fetch all teachers for add modal
$allTeachers = $teacherManager->getAllTeachers();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম ওয়াইজ ডিউটি বন্টন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .assignment-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .room-container {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
            min-height: 120px;
            transition: all 0.3s ease;
        }
        .room-container.drag-over {
            border-color: #28a745;
            background: #d4edda;
        }
        .room-header {
            background: #667eea;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-weight: 600;
            text-align: center;
        }
        .teacher-pool {
            border: 2px dashed #6c757d;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9fa;
            min-height: 200px;
        }
        .teacher-pool.drag-over {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .teacher-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
            cursor: move;
            display: inline-block;
            min-width: 200px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .teacher-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .teacher-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg);
        }
        .teacher-name {
            font-weight: 600;
            color: #495057;
            margin-bottom: 3px;
        }
        .teacher-details {
            font-size: 12px;
            color: #6c757d;
        }
        .room-assignment-view {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
        }
        .date-selector {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .assignment-summary {
            background: #f0f8ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-top: 20px;
        }
        .room-config {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .assignment-card { box-shadow: none !important; border: 1px solid #ddd !important; }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="assignment-card">
                        <div class="text-center">
                            <h1><i class="fas fa-door-open text-primary"></i> রুম ওয়াইজ ডিউটি বন্টন</h1>
                            <p class="text-muted mb-0">তারিখ ভিত্তিক রুম অনুযায়ী শিক্ষক ডিউটি বন্টন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Step 1: Room Configuration -->
            <div class="assignment-card">
                <div class="section-header">
                    <h4><i class="fas fa-cogs me-2"></i> ধাপ ১: রুম কনফিগারেশন</h4>
                </div>

                <div class="room-config">
                    <form method="POST">
                        <div class="row">
                            <div class="col-12">
                                <label class="form-label">পরীক্ষার রুমসমূহ (প্রতি লাইনে একটি রুম)</label>
                                <textarea class="form-control" name="rooms" rows="6" placeholder="রুমের নাম লিখুন..."><?php echo implode("\n", $configuredRooms); ?></textarea>
                                <small class="text-muted">প্রতি লাইনে একটি রুমের নাম লিখুন</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button type="submit" name="save_rooms" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> রুম কনফিগারেশন সেভ করুন
                            </button>
                        </div>
                    </form>
                </div>

                <div class="mt-3">
                    <h6>কনফিগার করা রুমসমূহ:</h6>
                    <div class="row">
                        <?php foreach ($configuredRooms as $index => $room): ?>
                            <div class="col-md-4 col-lg-3 mb-2">
                                <div class="badge bg-secondary p-2 w-100">
                                    <i class="fas fa-door-closed me-1"></i> <?php echo htmlspecialchars($room); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Step 2: Date Selection and Assignment -->
            <?php if (!empty($availableDates)): ?>
                <div class="assignment-card">
                    <div class="section-header">
                        <h4><i class="fas fa-calendar-alt me-2"></i> ধাপ ২: তারিখ নির্বাচন ও রুম বন্টন</h4>
                    </div>

                    <div class="date-selector">
                        <form method="POST" id="dateSelectionForm">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <label class="form-label">ডিউটির তারিখ নির্বাচন করুন</label>
                                    <div class="input-group mb-3">
                                        <select class="form-select" name="selected_date" id="selectedDate" onchange="loadTeachersForDate()">
                                            <option value="">তারিখ নির্বাচন করুন</option>
                                            <?php foreach ($availableDates as $date): ?>
                                                <option value="<?php echo $date; ?>">
                                                    <?php echo date('d F Y', strtotime($date)); ?> 
                                                    (<?php echo count($dutyAssignments[$date]); ?> জন শিক্ষক)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <button class="btn btn-outline-secondary" type="button" data-bs-toggle="modal" data-bs-target="#customDateModal">
                                            <i class="fas fa-plus"></i> নতুন তারিখ
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>নির্দেশনা:</strong> শিক্ষকদের ড্রাগ করে রুমে নিয়ে যান
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Assignment Interface -->
                    <div id="assignmentInterface" style="display: none;">
                        <form method="POST" id="roomAssignmentForm">
                            <input type="hidden" name="assignment_date" id="assignmentDate">
                            
                            <div class="row">
                                <!-- Teacher Pool -->
                                <div class="col-md-4">
                                    <h5><i class="fas fa-users me-2"></i> শিক্ষক তালিকা</h5>
                                    <div class="teacher-pool" id="teacherPool" ondrop="dropTeacher(event)" ondragover="allowDrop(event)">
                                        <div class="text-center text-muted">
                                            <i class="fas fa-users fa-2x mb-2"></i>
                                            <p>শিক্ষকদের তালিকা এখানে দেখানো হবে</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Room Assignment Area -->
                                <div class="col-md-8">
                                    <h5><i class="fas fa-door-open me-2"></i> রুম বন্টন</h5>
                                    <div class="row" id="roomsContainer">
                                        <?php foreach ($configuredRooms as $index => $room): ?>
                                            <div class="col-md-6 mb-3">
                                                <div class="room-container" data-room="<?php echo $index; ?>" ondrop="dropTeacher(event)" ondragover="allowDrop(event)">
                                                    <div class="room-header">
                                                        <?php echo htmlspecialchars($room); ?>
                                                        <span class="badge bg-light text-dark ms-2" id="count_<?php echo $index; ?>">০</span>
                                                    </div>
                                                    <div class="room-teachers" id="room_<?php echo $index; ?>">
                                                        <!-- Teachers will be dropped here -->
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    
                                    <!-- Teacher List with Edit/Remove Options -->
                                    <div class="mt-4">
                                        <h5><i class="fas fa-user-edit me-2"></i> শিক্ষক সম্পাদনা</h5>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-hover" id="teacherEditTable" style="display: none;">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>ক্রমিক</th>
                                                        <th>নাম</th>
                                                        <th>বিষয়</th>
                                                        <th>রুম</th>
                                                        <th>অ্যাকশন</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="teacherEditTableBody">
                                                    <!-- Teachers will be loaded here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="assignment-summary">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>মোট শিক্ষক:</strong> <span id="totalTeachers">০</span> |
                                        <strong>বন্টনকৃত:</strong> <span id="assignedTeachers">০</span> |
                                        <strong>অবশিষ্ট:</strong> <span id="remainingTeachers">০</span>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-secondary me-2" onclick="clearAllAssignments()">
                                            <i class="fas fa-undo me-1"></i> সব মুছুন
                                        </button>
                                        <button type="submit" name="save_room_assignment" class="btn btn-success">
                                            <i class="fas fa-save me-2"></i> রুম বন্টন সেভ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            <?php else: ?>
                <div class="assignment-card">
                    <div class="alert alert-warning text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <h5>কোনো ডিউটি বন্টন পাওয়া যায়নি!</h5>
                        <p>রুম ওয়াইজ ডিউটি বন্টন করার জন্য প্রথমে নিচের ধাপগুলো অনুসরণ করুন:</p>
                        <div class="text-start mt-3">
                            <ol>
                                <li><a href="teacher_duty_management.php" class="alert-link">শিক্ষক ডিউটি ব্যবস্থাপনা</a> পেজে যান</li>
                                <li>CSV ফাইল আপলোড করে শিক্ষকদের তথ্য যুক্ত করুন</li>
                                <li><a href="date_wise_duty_assignment.php" class="alert-link">তারিখ ভিত্তিক ডিউটি বন্টন</a> পেজে যান</li>
                                <li>একটি তারিখ নির্বাচন করে শিক্ষকদের ডিউটি বন্টন করুন</li>
                                <li>তারপর এই পেজে ফিরে এসে রুম ওয়াইজ বন্টন করুন</li>
                            </ol>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Custom Date Modal -->
            <div class="modal fade" id="customDateModal" tabindex="-1" aria-labelledby="customDateModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="customDateModalLabel">নির্দিষ্ট তারিখ যোগ করুন</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form method="POST" action="date_wise_duty_assignment.php">
                                <div class="mb-3">
                                    <label for="custom_duty_date" class="form-label">তারিখ নির্বাচন করুন</label>
                                    <input type="date" class="form-control" id="custom_duty_date" name="duty_date" required>
                                </div>
                                <button type="submit" class="btn btn-primary">তারিখ যোগ করুন</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Edit Teacher Modal -->
            <div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editTeacherModalLabel">শিক্ষকের তথ্য সম্পাদনা</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form method="POST" id="editTeacherForm">
                                <input type="hidden" id="edit_teacher_id" name="teacher_id">
                                <input type="hidden" id="edit_date" name="date">
                                
                                <div class="mb-3">
                                    <label for="teacher_name" class="form-label">শিক্ষকের নাম</label>
                                    <input type="text" class="form-control" id="teacher_name" readonly>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="room_number" class="form-label">রুম নম্বর</label>
                                    <select class="form-select" id="room_number" name="room_number">
                                        <option value="">রুম নির্বাচন করুন</option>
                                        <?php foreach ($configuredRooms as $index => $room): ?>
                                            <option value="<?php echo htmlspecialchars($room); ?>"><?php echo htmlspecialchars($room); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="d-flex justify-content-between">
                                    <button type="submit" name="update_teacher" class="btn btn-primary">আপডেট করুন</button>
                                    <button type="submit" name="remove_teacher" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে এই শিক্ষককে ডিউটি থেকে সরাতে চান?');">ডিউটি থেকে সরান</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Step 3: Saved Room Assignments -->
            <?php if (!empty($roomAssignments)): ?>
                <div class="assignment-card">
                    <div class="section-header">
                        <h4><i class="fas fa-list-check me-2"></i> ধাপ ৩: সেভ করা রুম বন্টন</h4>
                    </div>

                    <?php
                    // Sort room assignments by date
                    ksort($roomAssignments);
                    ?>

                    <?php foreach ($roomAssignments as $date => $assignments): ?>
                        <div class="room-assignment-view">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    <?php echo date('d F Y', strtotime($date)); ?>
                                    <?php
                                    $totalAssigned = 0;
                                    foreach ($assignments as $roomAssignment) {
                                        $totalAssigned += count($roomAssignment);
                                    }
                                    ?>
                                    <span class="badge bg-primary ms-2"><?php echo $totalAssigned; ?> জন বন্টিত</span>
                                </h5>
                                <div>
                                    <a href="print_room_duty.php?date=<?php echo urlencode($date); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-print me-1"></i> প্রিন্ট
                                    </a>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="date_to_remove" value="<?php echo $date; ?>">
                                        <button type="submit" name="remove_room_assignment" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('এই তারিখের রুম বন্টন মুছে ফেলতে চান?')">
                                            <i class="fas fa-trash me-1"></i> মুছুন
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <div class="row">
                                <?php foreach ($configuredRooms as $roomIndex => $roomName): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card">
                                            <div class="card-header bg-primary text-white">
                                                <h6 class="mb-0">
                                                    <i class="fas fa-door-closed me-2"></i>
                                                    <?php echo htmlspecialchars($roomName); ?>
                                                    <?php
                                                    $roomTeachers = $assignments[$roomIndex] ?? [];
                                                    ?>
                                                    <span class="badge bg-light text-dark ms-2"><?php echo count($roomTeachers); ?> জন</span>
                                                </h6>
                                            </div>
                                            <div class="card-body p-2">
                                                <?php if (!empty($roomTeachers)): ?>
                                                    <?php foreach ($roomTeachers as $teacherIndex): ?>
                                                        <?php if (isset($uploadedTeachers[$teacherIndex])): ?>
                                                            <?php $teacher = $uploadedTeachers[$teacherIndex]; ?>
                                                            <div class="small mb-1 p-1 bg-light rounded">
                                                                <strong><?php echo htmlspecialchars($teacher['name']); ?></strong><br>
                                                                <small class="text-muted">
                                                                    <?php echo htmlspecialchars($teacher['subject']); ?> -
                                                                    <?php echo htmlspecialchars($teacher['designation']); ?>
                                                                </small>
                                                            </div>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <small class="text-muted">কোনো শিক্ষক নেই</small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let currentTeachers = [];
        let dutyAssignments = <?php echo json_encode($dutyAssignments); ?>;
        let uploadedTeachers = <?php echo json_encode($uploadedTeachers); ?>;
        let configuredRooms = <?php echo json_encode($configuredRooms); ?>;

        // Load teachers for selected date
        function loadTeachersForDate() {
            const selectedDate = document.getElementById('selectedDate').value;

            if (!selectedDate) {
                document.getElementById('assignmentInterface').style.display = 'none';
                return;
            }

            document.getElementById('assignmentDate').value = selectedDate;
            document.getElementById('assignmentInterface').style.display = 'block';

            // Get teachers for this date
            const teacherIndices = dutyAssignments[selectedDate] || [];
            currentTeachers = teacherIndices;

            // Clear previous assignments
            clearAllAssignments();

            // Populate teacher pool
            const teacherPool = document.getElementById('teacherPool');
            teacherPool.innerHTML = '';

            teacherIndices.forEach(teacherIndex => {
                const teacher = uploadedTeachers[teacherIndex];
                if (teacher) {
                    const teacherElement = createTeacherElement(teacher, teacherIndex);
                    teacherPool.appendChild(teacherElement);
                }
            });

            updateSummary();
            
            // Populate teacher edit table
            populateTeacherEditTable(teacherIndices, selectedDate);
        }

        // Create teacher element
        function createTeacherElement(teacher, teacherIndex) {
            const div = document.createElement('div');
            div.className = 'teacher-item';
            div.draggable = true;
            div.dataset.teacherIndex = teacherIndex;
            div.innerHTML = `
                <div class="teacher-name">${teacher.name}</div>
                <div class="teacher-details">${teacher.subject} - ${teacher.designation}</div>
            `;

            div.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', teacherIndex);
                this.classList.add('dragging');
            });

            div.addEventListener('dragend', function(e) {
                this.classList.remove('dragging');
            });

            return div;
        }

        // Drag and drop functions
        function allowDrop(ev) {
            ev.preventDefault();
            ev.currentTarget.classList.add('drag-over');
        }

        function dropTeacher(ev) {
            ev.preventDefault();
            ev.currentTarget.classList.remove('drag-over');

            const teacherIndex = ev.dataTransfer.getData('text/plain');
            const teacher = uploadedTeachers[teacherIndex];

            if (!teacher) return;

            // Remove teacher from current location
            const existingElement = document.querySelector(`[data-teacher-index="${teacherIndex}"]`);
            if (existingElement) {
                existingElement.remove();
            }

            // Add teacher to new location
            const teacherElement = createTeacherElement(teacher, teacherIndex);

            if (ev.currentTarget.classList.contains('teacher-pool')) {
                ev.currentTarget.appendChild(teacherElement);
            } else if (ev.currentTarget.classList.contains('room-container')) {
                const roomTeachers = ev.currentTarget.querySelector('.room-teachers');
                roomTeachers.appendChild(teacherElement);

                // Update room count
                const roomIndex = ev.currentTarget.dataset.room;
                updateRoomCount(roomIndex);
            }

            updateSummary();
            updateHiddenInputs();
        }

        // Update room count
        function updateRoomCount(roomIndex) {
            const roomContainer = document.querySelector(`[data-room="${roomIndex}"]`);
            const teacherCount = roomContainer.querySelectorAll('.teacher-item').length;
            document.getElementById(`count_${roomIndex}`).textContent = teacherCount;
        }

        // Update summary
        function updateSummary() {
            const totalTeachers = currentTeachers.length;
            let assignedTeachers = 0;

            configuredRooms.forEach((room, index) => {
                const roomContainer = document.querySelector(`[data-room="${index}"]`);
                const teacherCount = roomContainer.querySelectorAll('.teacher-item').length;
                assignedTeachers += teacherCount;
                updateRoomCount(index);
            });

            const remainingTeachers = totalTeachers - assignedTeachers;

            document.getElementById('totalTeachers').textContent = totalTeachers;
            document.getElementById('assignedTeachers').textContent = assignedTeachers;
            document.getElementById('remainingTeachers').textContent = remainingTeachers;
        }

        // Update hidden inputs for form submission
        function updateHiddenInputs() {
            // Remove existing hidden inputs
            const existingInputs = document.querySelectorAll('input[name^="room_assignments"]');
            existingInputs.forEach(input => input.remove());

            // Add new hidden inputs
            const form = document.getElementById('roomAssignmentForm');

            configuredRooms.forEach((room, roomIndex) => {
                const roomContainer = document.querySelector(`[data-room="${roomIndex}"]`);
                const teacherElements = roomContainer.querySelectorAll('.teacher-item');

                teacherElements.forEach(teacherElement => {
                    const teacherIndex = teacherElement.dataset.teacherIndex;
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = `room_assignments[${roomIndex}][]`;
                    input.value = teacherIndex;
                    form.appendChild(input);
                });
            });
        }

        // Clear all assignments
        function clearAllAssignments() {
            configuredRooms.forEach((room, index) => {
                const roomTeachers = document.getElementById(`room_${index}`);
                roomTeachers.innerHTML = '';
                updateRoomCount(index);
            });
            updateSummary();
            updateHiddenInputs();
        }
        
        // Populate teacher edit table
        function populateTeacherEditTable(teacherIndices, selectedDate) {
            const tableBody = document.getElementById('teacherEditTableBody');
            const table = document.getElementById('teacherEditTable');
            
            // Clear existing rows
            tableBody.innerHTML = '';
            
            if (teacherIndices.length === 0) {
                table.style.display = 'none';
                return;
            }
            
            // Show the table
            table.style.display = 'table';
            
            // Add rows for each teacher
            teacherIndices.forEach((teacherIndex, index) => {
                const teacher = uploadedTeachers[teacherIndex];
                if (!teacher) return;
                
                const row = document.createElement('tr');
                
                // Serial number
                const slCell = document.createElement('td');
                slCell.textContent = index + 1;
                row.appendChild(slCell);
                
                // Name
                const nameCell = document.createElement('td');
                nameCell.textContent = teacher.name;
                row.appendChild(nameCell);
                
                // Subject
                const subjectCell = document.createElement('td');
                subjectCell.textContent = teacher.subject;
                row.appendChild(subjectCell);
                
                // Room
                const roomCell = document.createElement('td');
                roomCell.textContent = 'বরাদ্দ করা হয়নি'; // Will be updated when assigned
                row.appendChild(roomCell);
                
                // Actions
                const actionCell = document.createElement('td');
                const editButton = document.createElement('button');
                editButton.className = 'btn btn-sm btn-primary me-2';
                editButton.innerHTML = '<i class="fas fa-edit"></i> সম্পাদনা';
                editButton.onclick = function() {
                    openEditModal(teacher, '', selectedDate);
                };
                
                const removeButton = document.createElement('button');
                removeButton.className = 'btn btn-sm btn-danger';
                removeButton.innerHTML = '<i class="fas fa-trash"></i> সরান';
                removeButton.onclick = function() {
                    if (confirm('আপনি কি নিশ্চিত যে এই শিক্ষককে ডিউটি থেকে সরাতে চান?')) {
                        removeTeacherFromDuty(teacher.id, selectedDate);
                    }
                };
                
                actionCell.appendChild(editButton);
                actionCell.appendChild(removeButton);
                row.appendChild(actionCell);
                
                tableBody.appendChild(row);
            });
        }

        // Open edit modal for teacher
        function openEditModal(teacher, roomNumber, date) {
            document.getElementById('edit_teacher_id').value = teacher.id;
            document.getElementById('edit_date').value = date;
            document.getElementById('teacher_name').value = teacher.name;
            
            // Set room number in select
            const roomSelect = document.getElementById('room_number');
            for (let i = 0; i < roomSelect.options.length; i++) {
                if (roomSelect.options[i].value === roomNumber) {
                    roomSelect.selectedIndex = i;
                    break;
                }
            }
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));
            modal.show();
        }

        // Remove teacher from duty
        function removeTeacherFromDuty(teacherId, date) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';
            
            const teacherIdInput = document.createElement('input');
            teacherIdInput.name = 'teacher_id';
            teacherIdInput.value = teacherId;
            
            const dateInput = document.createElement('input');
            dateInput.name = 'date';
            dateInput.value = date;
            
            const submitInput = document.createElement('input');
            submitInput.name = 'remove_teacher';
            submitInput.value = '1';
            
            form.appendChild(teacherIdInput);
            form.appendChild(dateInput);
            form.appendChild(submitInput);
            
            document.body.appendChild(form);
            form.submit();
        }

        // Add event listeners for drag over
        document.addEventListener('DOMContentLoaded', function() {
            // Add drag over event listeners
            document.querySelectorAll('.room-container, .teacher-pool').forEach(container => {
                container.addEventListener('dragleave', function(e) {
                    this.classList.remove('drag-over');
                });
            });
        });
    </script>
</body>
</html>
