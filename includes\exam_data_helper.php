<?php
/**
 * Exam Data Helper Functions
 * Functions to retrieve and format exam-related data for public display
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/teacher_db.php';

class ExamDataHelper {
    private $pdo;
    private $teacherManager;
    
    public function __construct($pdo = null) {
        if ($pdo === null) {
            $db = new Database();
            $this->pdo = $db->getConnection();
        } else {
            $this->pdo = $pdo;
        }
        $this->teacherManager = new TeacherManager($this->pdo);
    }
    
    /**
     * Get comprehensive exam overview for public display
     */
    public function getExamOverview() {
        $overview = [
            'total_students' => 0,
            'total_teachers' => 0,
            'upcoming_exams' => [],
            'today_exams' => [],
            'room_assignments' => [],
            'recent_exams' => [],
            'exam_statistics' => []
        ];
        
        try {
            // Get total students
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM students");
            $overview['total_students'] = $stmt->fetch()['total'] ?? 0;
            
            // Get total teachers
            $stmt = $this->pdo->query("SELECT COUNT(*) as total FROM teachers");
            $overview['total_teachers'] = $stmt->fetch()['total'] ?? 0;
            
            // Get upcoming exam dates (next 30 days)
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT duty_date, 
                       COUNT(DISTINCT teacher_id) as teacher_count,
                       COUNT(DISTINCT room_number) as room_count,
                       GROUP_CONCAT(DISTINCT room_number ORDER BY room_number) as rooms
                FROM duty_assignments 
                WHERE duty_date >= CURDATE() AND duty_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
                GROUP BY duty_date
                ORDER BY duty_date ASC 
                LIMIT 10
            ");
            $stmt->execute();
            $overview['upcoming_exams'] = $stmt->fetchAll();
            
            // Get today's exam details
            $today = date('Y-m-d');
            $todayAssignments = $this->teacherManager->getDutyAssignments($today);
            $overview['today_exams'] = $todayAssignments;
            
            // Group today's assignments by room
            $roomGroups = [];
            foreach ($todayAssignments as $assignment) {
                $room = $assignment['room_number'] ?: 'অনির্দিষ্ট';
                if (!isset($roomGroups[$room])) {
                    $roomGroups[$room] = [];
                }
                $roomGroups[$room][] = $assignment;
            }
            $overview['room_assignments'] = $roomGroups;
            
            // Get recent exam statistics (last 7 days)
            $stmt = $this->pdo->prepare("
                SELECT duty_date,
                       COUNT(DISTINCT teacher_id) as teacher_count,
                       COUNT(DISTINCT room_number) as room_count
                FROM duty_assignments 
                WHERE duty_date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) 
                      AND duty_date < CURDATE()
                GROUP BY duty_date
                ORDER BY duty_date DESC
            ");
            $stmt->execute();
            $overview['recent_exams'] = $stmt->fetchAll();
            
            // Calculate exam statistics
            $overview['exam_statistics'] = $this->calculateExamStatistics();
            
        } catch (Exception $e) {
            error_log("Error getting exam overview: " . $e->getMessage());
        }
        
        return $overview;
    }
    
    /**
     * Get detailed exam information for a specific date
     */
    public function getExamDetailsByDate($date) {
        $details = [
            'date' => $date,
            'assignments' => [],
            'room_summary' => [],
            'teacher_summary' => [],
            'statistics' => []
        ];
        
        try {
            // Get all assignments for the date
            $assignments = $this->teacherManager->getDutyAssignments($date);
            $details['assignments'] = $assignments;
            
            // Group by room
            $roomSummary = [];
            $teacherSummary = [];
            
            foreach ($assignments as $assignment) {
                $room = $assignment['room_number'] ?: 'অনির্দিষ্ট';
                
                // Room summary
                if (!isset($roomSummary[$room])) {
                    $roomSummary[$room] = [
                        'room' => $room,
                        'teacher_count' => 0,
                        'teachers' => []
                    ];
                }
                $roomSummary[$room]['teacher_count']++;
                $roomSummary[$room]['teachers'][] = $assignment;
                
                // Teacher summary by subject
                $subject = $assignment['subject'] ?: 'অন্যান্য';
                if (!isset($teacherSummary[$subject])) {
                    $teacherSummary[$subject] = 0;
                }
                $teacherSummary[$subject]++;
            }
            
            $details['room_summary'] = $roomSummary;
            $details['teacher_summary'] = $teacherSummary;
            
            // Statistics
            $details['statistics'] = [
                'total_teachers' => count($assignments),
                'total_rooms' => count($roomSummary),
                'subjects_involved' => count($teacherSummary)
            ];
            
        } catch (Exception $e) {
            error_log("Error getting exam details for date $date: " . $e->getMessage());
        }
        
        return $details;
    }
    
    /**
     * Get student seat information
     */
    public function getStudentSeatInfo($rollNumber = null, $registrationNumber = null) {
        $seatInfo = [];
        
        try {
            $sql = "SELECT * FROM students WHERE 1=1";
            $params = [];
            
            if ($rollNumber) {
                $sql .= " AND roll = ?";
                $params[] = $rollNumber;
            }
            
            if ($registrationNumber) {
                $sql .= " AND registration = ?";
                $params[] = $registrationNumber;
            }
            
            $sql .= " LIMIT 10";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $seatInfo = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting student seat info: " . $e->getMessage());
        }
        
        return $seatInfo;
    }
    
    /**
     * Calculate exam statistics
     */
    private function calculateExamStatistics() {
        $stats = [
            'total_exam_days' => 0,
            'avg_teachers_per_day' => 0,
            'avg_rooms_per_day' => 0,
            'most_active_teachers' => [],
            'busiest_rooms' => []
        ];
        
        try {
            // Total exam days
            $stmt = $this->pdo->query("SELECT COUNT(DISTINCT duty_date) as total FROM duty_assignments");
            $stats['total_exam_days'] = $stmt->fetch()['total'] ?? 0;
            
            // Average teachers per day
            $stmt = $this->pdo->query("
                SELECT AVG(teacher_count) as avg_teachers 
                FROM (
                    SELECT COUNT(DISTINCT teacher_id) as teacher_count 
                    FROM duty_assignments 
                    GROUP BY duty_date
                ) as daily_counts
            ");
            $stats['avg_teachers_per_day'] = round($stmt->fetch()['avg_teachers'] ?? 0, 1);
            
            // Average rooms per day
            $stmt = $this->pdo->query("
                SELECT AVG(room_count) as avg_rooms 
                FROM (
                    SELECT COUNT(DISTINCT room_number) as room_count 
                    FROM duty_assignments 
                    WHERE room_number IS NOT NULL AND room_number != ''
                    GROUP BY duty_date
                ) as daily_counts
            ");
            $stats['avg_rooms_per_day'] = round($stmt->fetch()['avg_rooms'] ?? 0, 1);
            
            // Most active teachers (top 5)
            $stmt = $this->pdo->query("
                SELECT t.name, t.designation, COUNT(*) as duty_count
                FROM duty_assignments da
                JOIN teachers t ON da.teacher_id = t.id
                GROUP BY da.teacher_id
                ORDER BY duty_count DESC
                LIMIT 5
            ");
            $stats['most_active_teachers'] = $stmt->fetchAll();
            
            // Busiest rooms (top 5)
            $stmt = $this->pdo->query("
                SELECT room_number, COUNT(*) as usage_count
                FROM duty_assignments
                WHERE room_number IS NOT NULL AND room_number != ''
                GROUP BY room_number
                ORDER BY usage_count DESC
                LIMIT 5
            ");
            $stats['busiest_rooms'] = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error calculating exam statistics: " . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * Get exam calendar data for display
     */
    public function getExamCalendar($month = null, $year = null) {
        if (!$month) $month = date('m');
        if (!$year) $year = date('Y');
        
        $calendar = [];
        
        try {
            $stmt = $this->pdo->prepare("
                SELECT duty_date,
                       COUNT(DISTINCT teacher_id) as teacher_count,
                       COUNT(DISTINCT room_number) as room_count
                FROM duty_assignments
                WHERE MONTH(duty_date) = ? AND YEAR(duty_date) = ?
                GROUP BY duty_date
                ORDER BY duty_date
            ");
            $stmt->execute([$month, $year]);
            $calendar = $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting exam calendar: " . $e->getMessage());
        }
        
        return $calendar;
    }
}
?>
