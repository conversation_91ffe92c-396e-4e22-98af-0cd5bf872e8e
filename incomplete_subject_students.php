<?php
require_once __DIR__ . '/models/Student.php';

// Get filter parameters if set
$filterSubjectCount = isset($_GET['subject_count']) ? (int)$_GET['subject_count'] : null;
$filterGroup = $_GET['group'] ?? '';
$filterType = $_GET['type'] ?? '';

// Get all students
$student = new Student();
$allStudents = $student->getAll();

// Filter students who don't have all 13 subjects
$incompleteStudents = [];
foreach ($allStudents as $s) {
    $subjectCount = 0;

    // Check new subjects field format first
    if (!empty($s['subjects'])) {
        $subjects = explode(',', $s['subjects']);
        $subjectCount = count(array_filter($subjects, function($subject) {
            return !empty(trim($subject));
        }));
    } else {
        // Fallback to old format for backward compatibility
        for ($i = 1; $i <= 13; $i++) {
            $subField = 'sub_' . $i;
            if (!empty($s[$subField])) {
                $subjectCount++;
            }
        }
    }

    // Store subject count for later use
    $s['subject_count'] = $subjectCount;

    // If student has less than 13 subjects, add to incomplete list
    if ($subjectCount < 13) {
        $incompleteStudents[] = $s;
    }
}

// Apply additional filters if set
$filteredStudents = $incompleteStudents;

// Filter by subject count if requested
if ($filterSubjectCount !== null) {
    $filteredStudents = array_filter($filteredStudents, function($s) use ($filterSubjectCount) {
        return $s['subject_count'] === $filterSubjectCount;
    });
    $filteredStudents = array_values($filteredStudents); // Reindex array
}

// Filter by group
if (!empty($filterGroup)) {
    $filteredStudents = array_filter($filteredStudents, function($s) use ($filterGroup) {
        $group = $s['department'] ?? $s['group_name'] ?? '';
        return strcasecmp($group, $filterGroup) === 0;
    });
    $filteredStudents = array_values($filteredStudents); // Reindex array
}

// Filter by type
if (!empty($filterType)) {
    $filteredStudents = array_filter($filteredStudents, function($s) use ($filterType) {
        $type = $s['student_type'] ?? $s['type'] ?? '';
        return strcasecmp($type, $filterType) === 0;
    });
    $filteredStudents = array_values($filteredStudents); // Reindex array
}

// Sort by roll number
usort($incompleteStudents, function($a, $b) {
    $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
    $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
    return $rollA - $rollB;
});

// Also sort filtered students
usort($filteredStudents, function($a, $b) {
    $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
    $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
    return $rollA - $rollB;
});

$totalIncomplete = count($incompleteStudents);

// Group by subject count for statistics
$subjectCountStats = [];
foreach ($incompleteStudents as $s) {
    $count = $s['subject_count'];
    if (!isset($subjectCountStats[$count])) {
        $subjectCountStats[$count] = 0;
    }
    $subjectCountStats[$count]++;
}

// Group by type
$regularIncomplete = array_filter($incompleteStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Regular';
});
$irregularIncomplete = array_filter($incompleteStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Irregular';
});
$improvementIncomplete = array_filter($incompleteStudents, function($s) {
    return ($s['student_type'] ?? $s['type'] ?? '') == 'Improvement';
});

// Group by group_name
$groupStats = [];
foreach ($incompleteStudents as $s) {
    $group = $s['department'] ?? $s['group_name'] ?? 'Unknown';
    if (!isset($groupStats[$group])) {
        $groupStats[$group] = [];
    }
    $groupStats[$group][] = $s;
}

// Sort groups
ksort($groupStats);


?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>অসম্পূর্ণ বিষয়ের শিক্ষার্থী - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border: none;
        }
        
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            border-bottom: none;
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            background: #f8f9fa;
        }
        
        .badge-subject-count {
            font-size: 0.9em;
            padding: 0.5em 0.8em;
        }
        
        .subject-list {
            font-size: 0.85em;
            max-width: 200px;
        }
        
        .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            font-weight: 500;
        }
        
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <h2 class="text-white">📚 অসম্পূর্ণ বিষয়ের শিক্ষার্থী</h2>
                    <p class="text-white-50">যাদের ১৩টি বিষয় সম্পূর্ণ নেই</p>
                    
                    <?php if ($filterSubjectCount !== null || !empty($filterGroup) || !empty($filterType)): ?>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-filter me-2"></i>ফিল্টার প্রয়োগ করা হয়েছে:</h5>
                            <ul class="mb-0">
                                <?php if ($filterSubjectCount !== null): ?>
                                    <li>বিষয় সংখ্যা: <strong><?php echo $filterSubjectCount; ?></strong></li>
                                <?php endif; ?>
                                
                                <?php if (!empty($filterGroup)): ?>
                                    <li>গ্রুপ: <strong><?php echo htmlspecialchars($filterGroup); ?></strong></li>
                                <?php endif; ?>
                                
                                <?php if (!empty($filterType)): ?>
                                    <li>ধরন: <strong><?php echo htmlspecialchars($filterType); ?></strong></li>
                                <?php endif; ?>
                            </ul>
                            <a href="incomplete_subject_students.php" class="btn btn-sm btn-light mt-2">
                                <i class="fas fa-times me-1"></i>ফিল্টার মুছুন
                            </a>
                            
                            <?php if ($filterSubjectCount !== null): ?>
                                <a href="export_incomplete_students.php?subject_count=<?php echo $filterSubjectCount; ?>" 
                                   class="btn btn-sm btn-warning mt-2" target="_blank">
                                    <i class="fas fa-file-export me-1"></i>CSV এক্সপোর্ট
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6 text-end">
                    <a href="export_incomplete_students.php" class="btn btn-warning me-2" target="_blank">
                        <i class="fas fa-file-export me-1"></i> সকল অসম্পূর্ণ শিক্ষার্থী CSV এক্সপোর্ট
                    </a>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            🎫 সীট কার্ড প্রিন্ট
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="working_seat_cards.php?incomplete_only=1" target="_blank">
                                <i class="fas fa-users me-2"></i>সকল অসম্পূর্ণ সীটকার্ড
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">গ্রুপ ভিত্তিক</h6></li>
                            <?php foreach ($groupStats as $group => $students): ?>
                                <li><a class="dropdown-item" href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&incomplete_only=1" target="_blank">
                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?> জন)
                                </a></li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">টাইপ ভিত্তিক</h6></li>
                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Regular&incomplete_only=1" target="_blank">
                                <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularIncomplete); ?> জন)
                            </a></li>
                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Irregular&incomplete_only=1" target="_blank">
                                <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularIncomplete); ?> জন)
                            </a></li>
                            <li><a class="dropdown-item" href="working_seat_cards.php?type=Improvement&incomplete_only=1" target="_blank">
                                <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementIncomplete); ?> জন)
                            </a></li>
                        </ul>
                    </div>
                    
                    <div class="btn-group ms-2" role="group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            📊 CSV এক্সপোর্ট
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="export_incomplete_students.php" target="_blank">
                                <i class="fas fa-users me-2"></i>সকল অসম্পূর্ণ শিক্ষার্থী
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">গ্রুপ ভিত্তিক</h6></li>
                            <?php foreach ($groupStats as $group => $students): ?>
                                <li><a class="dropdown-item" href="export_incomplete_students.php?group=<?php echo urlencode($group); ?>" target="_blank">
                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?> জন)
                                </a></li>
                            <?php endforeach; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">টাইপ ভিত্তিক</h6></li>
                            <li><a class="dropdown-item" href="export_incomplete_students.php?type=Regular" target="_blank">
                                <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularIncomplete); ?> জন)
                            </a></li>
                            <li><a class="dropdown-item" href="export_incomplete_students.php?type=Irregular" target="_blank">
                                <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularIncomplete); ?> জন)
                            </a></li>
                            <li><a class="dropdown-item" href="export_incomplete_students.php?type=Improvement" target="_blank">
                                <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementIncomplete); ?> জন)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="stats-number"><?php echo $totalIncomplete; ?></div>
                        <h6 class="text-white-50 mb-0">মোট অসম্পূর্ণ</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%);">
                        <div class="stats-number"><?php echo count($regularIncomplete); ?></div>
                        <h6 class="text-white-50 mb-0">নিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);">
                        <div class="stats-number"><?php echo count($irregularIncomplete); ?></div>
                        <h6 class="text-white-50 mb-0">অনিয়মিত</h6>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card" style="background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);">
                        <div class="stats-number"><?php echo count($improvementIncomplete); ?></div>
                        <h6 class="text-white-50 mb-0">উন্নতি</h6>
                    </div>
                </div>
            </div>
            
            <!-- Subject Count Filter -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-filter me-2"></i>বিষয় সংখ্যা অনুযায়ী ফিল্টার
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php
                                // Count students by subject count
                                $countBySubject = [];
                                foreach ($incompleteStudents as $student) {
                                    $count = $student['subject_count'];
                                    if (!isset($countBySubject[$count])) {
                                        $countBySubject[$count] = 0;
                                    }
                                    $countBySubject[$count]++;
                                }
                                
                                // Sort by subject count
                                ksort($countBySubject);
                                
                                // Display buttons for each subject count
                                foreach ($countBySubject as $count => $total) {
                                    ?>
                                    <div class="col-md-2 col-sm-4 mb-3">
                                        <div class="d-grid">
                                            <a href="export_incomplete_students.php?subject_count=<?php echo $count; ?>" 
                                               class="btn btn-outline-info" target="_blank">
                                                <strong><?php echo $count; ?></strong> বিষয়
                                                <span class="badge bg-secondary"><?php echo $total; ?></span>
                                            </a>
                                        </div>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                            
                            <!-- Specific Common Subject Buttons -->
                            <hr>
                            <div class="row">
                                <!-- Display buttons for common requirements -->
                                <div class="col-12 mb-2">
                                    <h6>বিশেষ ফিল্টার:</h6>
                                </div>
                                
                                <?php 
                                // Define common subject count filters
                                $commonFilters = [
                                    ['count' => 1, 'label' => '১ বিষয়', 'class' => 'danger'],
                                    ['count' => 2, 'label' => '২ বিষয়', 'class' => 'warning'],
                                    ['count' => 3, 'label' => '৩ বিষয়', 'class' => 'info'],
                                    ['count' => 4, 'label' => '৪ বিষয়', 'class' => 'primary'],
                                    ['count' => 5, 'label' => '৫ বিষয়', 'class' => 'success'],
                                ];
                                
                                foreach ($commonFilters as $filter) {
                                    $count = isset($countBySubject[$filter['count']]) ? $countBySubject[$filter['count']] : 0;
                                    ?>
                                    <div class="col-md-2 col-sm-4 mb-2">
                                        <div class="d-grid">
                                            <a href="incomplete_subject_students.php?subject_count=<?php echo $filter['count']; ?>" 
                                               class="btn btn-outline-<?php echo $filter['class']; ?>">
                                                <i class="fas fa-filter me-1"></i> <?php echo $filter['label']; ?>
                                                <span class="badge bg-secondary"><?php echo $count; ?></span>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="col-md-1 col-sm-2 mb-2">
                                        <div class="d-grid">
                                            <a href="export_incomplete_students.php?subject_count=<?php echo $filter['count']; ?>" 
                                               class="btn btn-<?php echo $filter['class']; ?>" target="_blank">
                                                <i class="fas fa-file-csv"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Group Statistics -->
            <?php if (!empty($groupStats)): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>গ্রুপ ভিত্তিক পরিসংখ্যান
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($groupStats as $group => $students): ?>
                                    <div class="col-md-3 col-sm-6 mb-3">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h4 class="text-primary"><?php echo count($students); ?></h4>
                                                <h6 class="card-title"><?php echo htmlspecialchars($group); ?></h6>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&incomplete_only=1"
                                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                                        <i class="fas fa-id-card"></i> অসম্পূর্ণ সীট কার্ড
                                                    </a>
                                                    <button class="btn btn-outline-info btn-sm" onclick="showGroupDetails('<?php echo htmlspecialchars($group); ?>')">
                                                        <i class="fas fa-eye"></i> বিস্তারিত
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-bolt me-2"></i>দ্রুত অ্যাকশন
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-2">
                                    <a href="group_seat_card_generator.php" class="btn btn-outline-primary w-100">
                                        <i class="fas fa-layer-group me-2"></i>গ্রুপ সীট কার্ড জেনারেটর
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="working_seat_cards.php?incomplete_only=1" class="btn btn-outline-success w-100" target="_blank">
                                        <i class="fas fa-users me-2"></i>সকল অসম্পূর্ণ সীট কার্ড
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="working_seat_cards.php?type=Regular&incomplete_only=1" class="btn btn-outline-info w-100" target="_blank">
                                        <i class="fas fa-user-check me-2"></i>নিয়মিত অসম্পূর্ণ
                                    </a>
                                </div>
                                <div class="col-md-3 mb-2">
                                    <a href="working_seat_cards.php?type=Irregular&incomplete_only=1" class="btn btn-outline-warning w-100" target="_blank">
                                        <i class="fas fa-user-times me-2"></i>অনিয়মিত অসম্পূর্ণ
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subject Count Distribution -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>বিষয় সংখ্যা অনুযায়ী বিতরণ
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($subjectCountStats as $count => $students): ?>
                                    <div class="col-md-2 col-sm-4 col-6 mb-3">
                                        <div class="text-center p-3 border rounded">
                                            <h4 class="text-primary"><?php echo $students; ?></h4>
                                            <small class="text-muted"><?php echo $count; ?>টি বিষয়</small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Tabs -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <ul class="nav nav-pills nav-fill" id="studentTypeTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                                        <i class="fas fa-users me-2"></i>সকল অসম্পূর্ণ (<?php echo $totalIncomplete; ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="regular-tab" data-bs-toggle="pill" data-bs-target="#regular" type="button" role="tab">
                                        <i class="fas fa-user-check me-2"></i>নিয়মিত (<?php echo count($regularIncomplete); ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="irregular-tab" data-bs-toggle="pill" data-bs-target="#irregular" type="button" role="tab">
                                        <i class="fas fa-user-times me-2"></i>অনিয়মিত (<?php echo count($irregularIncomplete); ?>)
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="improvement-tab" data-bs-toggle="pill" data-bs-target="#improvement" type="button" role="tab">
                                        <i class="fas fa-user-graduate me-2"></i>উন্নতি (<?php echo count($improvementIncomplete); ?>)
                                    </button>
                                </li>
                                <?php foreach ($groupStats as $group => $students): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="group-<?php echo strtolower(str_replace(' ', '-', $group)); ?>-tab"
                                            data-bs-toggle="pill" data-bs-target="#group-<?php echo strtolower(str_replace(' ', '-', $group)); ?>"
                                            type="button" role="tab">
                                        <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> (<?php echo count($students); ?>)
                                    </button>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students List -->
            <div class="row">
                <div class="col-12">
                    <div class="tab-content" id="studentTypeTabsContent">

                        <!-- All Students Tab -->
                        <div class="tab-pane fade show active" id="all" role="tabpanel">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-users me-2"></i>সকল অসম্পূর্ণ বিষয়ের শিক্ষার্থী
                                                <span class="badge bg-primary ms-2"><?php echo $totalIncomplete; ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?incomplete_only=1" class="btn btn-success" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>সকল অসম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php 
                                    // Decide which students to display based on filters
                                    $displayStudents = ($filterSubjectCount !== null || !empty($filterGroup) || !empty($filterType)) 
                                        ? $filteredStudents 
                                        : $incompleteStudents;
                                    
                                    if (empty($displayStudents)): ?>
                                        <div class="text-center py-5">
                                            <?php if ($filterSubjectCount !== null || !empty($filterGroup) || !empty($filterType)): ?>
                                                <i class="fas fa-filter fa-3x text-warning mb-3"></i>
                                                <h5 class="text-warning">ফিল্টার অনুযায়ী কোন শিক্ষার্থী পাওয়া যায়নি!</h5>
                                                <p class="text-muted">আপনার নির্বাচিত ফিল্টারের জন্য কোন রেকর্ড নেই।</p>
                                                <a href="incomplete_subject_students.php" class="btn btn-outline-secondary mt-2">
                                                    <i class="fas fa-times me-1"></i>ফিল্টার মুছুন
                                                </a>
                                            <?php else: ?>
                                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                                <h5 class="text-success">সবার বিষয় সম্পূর্ণ!</h5>
                                                <p class="text-muted">সকল শিক্ষার্থীর ১৩টি বিষয় রয়েছে।</p>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        <?php echo renderStudentTable($displayStudents, 'all'); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Regular Students Tab -->
                        <div class="tab-pane fade" id="regular" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-check me-2"></i>নিয়মিত - অসম্পূর্ণ বিষয়
                                                <span class="badge bg-light text-success ms-2"><?php echo count($regularIncomplete); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Regular&incomplete_only=1" class="btn btn-light" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>নিয়মিত অসম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php if (empty($regularIncomplete)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                            <h5 class="text-success">সব নিয়মিত শিক্ষার্থীর বিষয় সম্পূর্ণ!</h5>
                                        </div>
                                    <?php else: ?>
                                        <?php echo renderStudentTable($regularIncomplete, 'regular'); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Irregular Students Tab -->
                        <div class="tab-pane fade" id="irregular" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-times me-2"></i>অনিয়মিত - অসম্পূর্ণ বিষয়
                                                <span class="badge bg-light text-danger ms-2"><?php echo count($irregularIncomplete); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Irregular&incomplete_only=1" class="btn btn-light" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>অনিয়মিত অসম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php if (empty($irregularIncomplete)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                            <h5 class="text-success">সব অনিয়মিত শিক্ষার্থীর বিষয় সম্পূর্ণ!</h5>
                                        </div>
                                    <?php else: ?>
                                        <?php echo renderStudentTable($irregularIncomplete, 'irregular'); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Improvement Students Tab -->
                        <div class="tab-pane fade" id="improvement" role="tabpanel">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-graduate me-2"></i>উন্নতি - অসম্পূর্ণ বিষয়
                                                <span class="badge bg-dark ms-2"><?php echo count($improvementIncomplete); ?></span>
                                            </h5>
                                        </div>
                                        <div class="col-auto">
                                            <a href="working_seat_cards.php?type=Improvement&incomplete_only=1" class="btn btn-dark" target="_blank">
                                                <i class="fas fa-id-card me-2"></i>উন্নতি অসম্পূর্ণ সীটকার্ড
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <?php if (empty($improvementIncomplete)): ?>
                                        <div class="text-center py-5">
                                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                            <h5 class="text-success">সব উন্নতি শিক্ষার্থীর বিষয় সম্পূর্ণ!</h5>
                                        </div>
                                    <?php else: ?>
                                        <?php echo renderStudentTable($improvementIncomplete, 'improvement'); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Group-wise Tabs -->
                        <?php foreach ($groupStats as $group => $students): ?>
                            <?php $groupId = strtolower(str_replace(' ', '-', $group)); ?>
                            <div class="tab-pane fade" id="group-<?php echo $groupId; ?>" role="tabpanel">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <div class="row align-items-center">
                                            <div class="col">
                                                <h5 class="mb-0">
                                                    <i class="fas fa-layer-group me-2"></i><?php echo htmlspecialchars($group); ?> - অসম্পূর্ণ বিষয়
                                                    <span class="badge bg-dark ms-2"><?php echo count($students); ?></span>
                                                </h5>
                                            </div>
                                            <div class="col-auto">
                                                <div class="btn-group" role="group">
                                                    <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&incomplete_only=1"
                                                       class="btn btn-light" target="_blank">
                                                        <i class="fas fa-id-card me-2"></i><?php echo htmlspecialchars($group); ?> অসম্পূর্ণ সীটকার্ড
                                                    </a>
                                                    <button class="btn btn-outline-light" onclick="printGroupSeatCards('<?php echo htmlspecialchars($group); ?>')">
                                                        <i class="fas fa-print me-2"></i>প্রিন্ট
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body p-0">
                                        <?php if (empty($students)): ?>
                                            <div class="text-center py-5">
                                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                                <h5 class="text-success"><?php echo htmlspecialchars($group); ?> গ্রুপের সব শিক্ষার্থীর বিষয় সম্পূর্ণ!</h5>
                                            </div>
                                        <?php else: ?>
                                            <?php echo renderStudentTable($students, 'group-' . $groupId); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTables for all student tables
            var tables = {};
            $('.students-table').each(function() {
                var tableId = $(this).attr('id');
                tables[tableId] = $(this).DataTable({
                    "pageLength": 25,
                    "order": [[ 1, "asc" ]], // Sort by Roll No. column
                    "scrollX": true,
                    "responsive": true,
                    "language": {
                        "lengthMenu": "প্রতি পৃষ্ঠায় _MENU_ টি রেকর্ড দেখান",
                        "zeroRecords": "কোন রেকর্ড পাওয়া যায়নি",
                        "info": "_START_ থেকে _END_ পর্যন্ত _TOTAL_ টি রেকর্ডের মধ্যে",
                        "infoEmpty": "০ টি রেকর্ড",
                        "infoFiltered": "(মোট _MAX_ টি রেকর্ড থেকে ফিল্টার করা হয়েছে)",
                        "search": "খুঁজুন:",
                        "paginate": {
                            "first": "প্রথম",
                            "last": "শেষ",
                            "next": "পরবর্তী",
                            "previous": "পূর্ববর্তী"
                        }
                    },
                    // Add the following to make the search work better
                    "drawCallback": function() {
                        // When table is redrawn (like after search), uncheck the master checkbox
                        $('#' + tableId + ' thead input[type="checkbox"]').prop('checked', false);
                    }
                });
            });

            // Store tables in global variable for later use
            window.studentTables = tables;
        });
        
        // Add debug mode help
        $(document).ready(function() {
            $('body').append('<div style="position:fixed;bottom:10px;right:10px;opacity:0.5;"><a href="export_incomplete_students.php?debug=1" target="_blank" class="btn btn-sm btn-light"><i class="fas fa-bug"></i></a></div>');
        });

        // Show group details
        function showGroupDetails(groupName) {
            // Switch to the group tab
            const groupId = groupName.toLowerCase().replace(/\s+/g, '-');
            const tabButton = document.getElementById('group-' + groupId + '-tab');
            if (tabButton) {
                tabButton.click();
            }
        }

        // Print group seat cards
        function printGroupSeatCards(groupName) {
            const url = 'working_seat_cards.php?group=' + encodeURIComponent(groupName) + '&incomplete_only=1';
            window.open(url, '_blank');
        }

        // Bulk actions
        function selectAllStudents(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => cb.checked = true);
        }

        function deselectAllStudents(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => cb.checked = false);
        }

        function printSelectedSeatCards(tableId) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox:checked');
            const rollNumbers = [];

            checkboxes.forEach(cb => {
                rollNumbers.push(cb.value);
            });

            if (rollNumbers.length === 0) {
                alert('কোন শিক্ষার্থী নির্বাচন করা হয়নি!');
                return;
            }

            const url = 'working_seat_cards.php?rolls=' + rollNumbers.join(',');
            window.open(url, '_blank');
        }
        
        function exportSelectedStudents(tableId, event) {
            // Get all checked checkboxes
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox:checked');
            const rollNumbers = [];

            checkboxes.forEach(cb => {
                rollNumbers.push(cb.value);
            });

            // If no students are selected, export filtered students
            if (rollNumbers.length === 0) {
                // Check if the table has search applied
                const table = window.studentTables[tableId];
                const search = table ? table.search() : '';
                
                if (search) {
                    // Get all visible rows in the table after filtering
                    const visibleRows = table.rows({search: 'applied'}).nodes();
                    
                    // Extract roll numbers from visible rows
                    const filteredRolls = [];
                    visibleRows.forEach(row => {
                        const checkbox = row.querySelector('.student-checkbox');
                        if (checkbox) {
                            filteredRolls.push(checkbox.value);
                        }
                    });
                    
                    if (filteredRolls.length > 0) {
                        // Console log for debugging
                        console.log('Filtered rolls: ', filteredRolls);
                        
                        // Export filtered students
                        const url = 'export_incomplete_students.php?rolls=' + filteredRolls.join(',');
                        // Open in debug mode if shift key is pressed
                        if (event.shiftKey) {
                            window.open(url + '&debug=1', '_blank');
                        } else {
                            window.open(url, '_blank');
                        }
                        return;
                    }
                }
                
                // If no search or no results, show alert
                alert('কোন শিক্ষার্থী নির্বাচন করা হয়নি এবং কোন সার্চ ফিল্টার প্রয়োগ করা হয়নি!');
                return;
            }

            // Console log for debugging
            console.log('Selected rolls: ', rollNumbers, 'From table: ', tableId);
            
            // Export selected students
            const url = 'export_incomplete_students.php?rolls=' + rollNumbers.join(',');
            // Open in debug mode if shift key is pressed
            if (event.shiftKey) {
                window.open(url + '&debug=1', '_blank');
            } else {
                window.open(url, '_blank');
            }
        }

        // Toggle all checkboxes
        function toggleAllCheckboxes(tableId, masterCheckbox) {
            const checkboxes = document.querySelectorAll('#' + tableId + ' .student-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = masterCheckbox.checked;
            });
        }
    </script>
</body>
</html>

<?php
// Function to render student table
function renderStudentTable($students, $tableId) {
    if (empty($students)) {
        return '';
    }

    ob_start();
    ?>
    <div class="mb-3">
        <div class="btn-group" role="group">
            <button class="btn btn-sm btn-outline-primary" onclick="selectAllStudents('<?php echo $tableId; ?>-table')">
                <i class="fas fa-check-square me-1"></i>সব নির্বাচন
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="deselectAllStudents('<?php echo $tableId; ?>-table')">
                <i class="fas fa-square me-1"></i>সব বাতিল
            </button>
            <button class="btn btn-sm btn-success" onclick="printSelectedSeatCards('<?php echo $tableId; ?>-table')">
                <i class="fas fa-print me-1"></i>নির্বাচিত সীটকার্ড প্রিন্ট
            </button>
            <button class="btn btn-sm btn-primary" onclick="exportSelectedStudents('<?php echo $tableId; ?>-table', event)">
                <i class="fas fa-file-csv me-1"></i>নির্বাচিত CSV এক্সপোর্ট
            </button>
            <button class="btn btn-sm btn-outline-info" onclick="exportSelectedStudents('<?php echo $tableId; ?>-table', {shiftKey: true})">
                <i class="fas fa-bug me-1"></i>ডিবাগ মোড
            </button>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-striped table-hover mb-0 students-table" id="<?php echo $tableId; ?>-table">
            <thead class="table-dark">
                <tr>
                    <th>
                        <input type="checkbox" class="form-check-input" onchange="toggleAllCheckboxes('<?php echo $tableId; ?>-table', this)">
                    </th>
                    <th>Roll No.</th>
                    <th>Student Name</th>
                    <th>Type</th>
                    <th>Group</th>
                    <th>বিষয় সংখ্যা</th>
                    <th>বিষয়সমূহ</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($students as $student): ?>
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input student-checkbox" value="<?php echo htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? ''); ?>">
                        </td>
                        <td><strong class="text-primary"><?php echo htmlspecialchars($student['roll'] ?? $student['roll_no'] ?? ''); ?></strong></td>
                        <td><strong><?php echo htmlspecialchars($student['name'] ?? $student['student_name'] ?? ''); ?></strong></td>
                        <td>
                            <?php
                            $type = $student['student_type'] ?? $student['type'] ?? '';
                            $badgeClass = '';
                            switch($type) {
                                case 'Regular': $badgeClass = 'bg-success'; break;
                                case 'Irregular': $badgeClass = 'bg-danger'; break;
                                case 'Improvement': $badgeClass = 'bg-warning'; break;
                                default: $badgeClass = 'bg-secondary';
                            }
                            ?>
                            <span class="badge <?php echo $badgeClass; ?>"><?php echo htmlspecialchars($type); ?></span>
                        </td>
                        <td><?php echo htmlspecialchars($student['department'] ?? $student['group_name'] ?? ''); ?></td>
                        <td>
                            <span class="badge badge-subject-count bg-info"><?php echo $student['subject_count']; ?>/13</span>
                        </td>
                        <td>
                            <div class="subject-list">
                                <?php
                                $subjects = [];

                                // Check new subjects field format first
                                if (!empty($student['subjects'])) {
                                    $subjects = explode(',', $student['subjects']);
                                    $subjects = array_filter($subjects, function($s) {
                                        return !empty(trim($s));
                                    });
                                } else {
                                    // Fallback to old format
                                    for ($i = 1; $i <= 13; $i++) {
                                        $subField = 'sub_' . $i;
                                        if (!empty($student[$subField])) {
                                            $subjects[] = $student[$subField];
                                        }
                                    }
                                }

                                echo htmlspecialchars(implode(', ', array_slice($subjects, 0, 5)));
                                if (count($subjects) > 5) echo '...';
                                ?>
                            </div>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="working_seat_cards.php?search=<?php echo urlencode($student['name'] ?? $student['student_name'] ?? ''); ?>"
                                   class="btn btn-sm btn-primary" target="_blank">
                                    <i class="fas fa-id-card"></i> Seat Card
                                </a>
                                <a href="working_seat_cards.php?rolls=<?php echo urlencode($student['roll'] ?? $student['roll_no'] ?? ''); ?>&incomplete_only=1"
                                   class="btn btn-sm btn-success" target="_blank">
                                    <i class="fas fa-print"></i> Print
                                </a>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <?php
    return ob_get_clean();
}
?>
