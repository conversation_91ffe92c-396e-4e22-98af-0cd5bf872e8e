<?php
require_once 'includes/teacher_db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>নতুন ফিচার টেস্ট</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Hind Siliguri', sans-serif; }</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🎉 নতুন ফিচার টেস্ট</h1>";

// Test database tables
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-database'></i> ডাটাবেস টেবিল চেক</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Check teachers table
    $stmt = $pdo->query("SHOW TABLES LIKE 'teachers'");
    $teachersTable = $stmt->fetch();
    echo "<p><i class='fas fa-check text-success'></i> Teachers Table: " . ($teachersTable ? "✅ বিদ্যমান" : "❌ নেই") . "</p>";

    // Check duty_assignments table
    $stmt = $pdo->query("SHOW TABLES LIKE 'duty_assignments'");
    $dutyTable = $stmt->fetch();
    echo "<p><i class='fas fa-check text-success'></i> Duty Assignments Table: " . ($dutyTable ? "✅ বিদ্যমান" : "❌ নেই") . "</p>";

    // Check signatures table
    $stmt = $pdo->query("SHOW TABLES LIKE 'signatures'");
    $signaturesTable = $stmt->fetch();
    echo "<p><i class='fas fa-check text-success'></i> Signatures Table: " . ($signaturesTable ? "✅ বিদ্যমান" : "❌ নেই") . "</p>";

    // Count teachers
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM teachers");
    $teacherCount = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-users text-info'></i> মোট শিক্ষক: <strong>$teacherCount জন</strong></p>";

    // Count duty assignments
    $stmt = $pdo->query("SELECT COUNT(DISTINCT duty_date) as count FROM duty_assignments");
    $dutyCount = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-calendar text-warning'></i> ডিউটি তারিখ: <strong>$dutyCount টি</strong></p>";

    // Count signatures
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM signatures WHERE is_active = TRUE");
    $signatureCount = $stmt->fetch()['count'];
    echo "<p><i class='fas fa-signature text-danger'></i> সক্রিয় স্বাক্ষর: <strong>$signatureCount টি</strong></p>";

} catch (Exception $e) {
    echo "<p class='text-danger'>❌ ডাটাবেস এরর: " . $e->getMessage() . "</p>";
}

echo "</div></div></div>";

// Test new features
echo "<div class='col-md-6'>";
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-star'></i> নতুন ফিচার লিংক</h5>";
echo "</div>";
echo "<div class='card-body'>";

$features = [
    'duty_letter_generator.php' => 'ডিউটি লেটার জেনারেটর',
    'teacher_id_card_generator.php' => 'শিক্ষক আইডি কার্ড জেনারেটর',
    'signature_upload.php' => 'স্বাক্ষর আপলোড',
    'teacher_duty_management.php' => 'শিক্ষক ডিউটি ব্যবস্থাপনা (আপডেটেড)'
];

foreach ($features as $file => $name) {
    $exists = file_exists($file);
    echo "<p>";
    echo $exists ? "<i class='fas fa-check text-success'></i>" : "<i class='fas fa-times text-danger'></i>";
    echo " <a href='$file' class='text-decoration-none' target='_blank'>$name</a>";
    echo $exists ? " ✅" : " ❌";
    echo "</p>";
}

echo "</div></div></div>";
echo "</div>";

// Test TeacherManager methods
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'><i class='fas fa-cogs'></i> TeacherManager মেথড টেস্ট</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Test getAllTeachers
    $teachers = $teacherManager->getAllTeachers();
    echo "<p><i class='fas fa-check text-success'></i> getAllTeachers(): " . count($teachers) . " জন শিক্ষক পাওয়া গেছে</p>";

    // Test getAllDutyDates
    $dutyDates = $teacherManager->getAllDutyDates();
    echo "<p><i class='fas fa-check text-success'></i> getAllDutyDates(): " . count($dutyDates) . " টি তারিখ পাওয়া গেছে</p>";

    // Test getActiveSignature
    $principalSig = $teacherManager->getActiveSignature('principal');
    $convenerSig = $teacherManager->getActiveSignature('convener');
    echo "<p><i class='fas fa-check text-success'></i> Principal Signature: " . ($principalSig ? "✅ আছে" : "❌ নেই") . "</p>";
    echo "<p><i class='fas fa-check text-success'></i> Convener Signature: " . ($convenerSig ? "✅ আছে" : "❌ নেই") . "</p>";

    // Test teacher stats
    $stats = $teacherManager->getTeacherStats();
    echo "<div class='mt-3'>";
    echo "<h6>শিক্ষক পরিসংখ্যান:</h6>";
    echo "<ul>";
    echo "<li>মোট: " . $stats['total'] . " জন</li>";
    echo "<li>সাধারন: " . $stats['normal'] . " জন</li>";
    echo "<li>সম সময় অন্তর্ভুক্ত: " . $stats['always'] . " জন</li>";
    echo "<li>কখনো অন্তর্ভুক্ত নয়: " . $stats['never'] . " জন</li>";
    echo "</ul>";
    echo "</div>";

} catch (Exception $e) {
    echo "<p class='text-danger'>❌ মেথড টেস্ট এরর: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Directory check
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5 class='mb-0'><i class='fas fa-folder'></i> ডিরেক্টরি চেক</h5>";
echo "</div>";
echo "<div class='card-body'>";

$directories = [
    'uploads/signatures' => 'স্বাক্ষর আপলোড ডিরেক্টরি',
    'uploads/teachers' => 'শিক্ষক ছবি ডিরেক্টরি',
    'uploads' => 'মূল আপলোড ডিরেক্টরি'
];

foreach ($directories as $dir => $name) {
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    echo "<p>";
    echo $exists ? "<i class='fas fa-check text-success'></i>" : "<i class='fas fa-times text-danger'></i>";
    echo " $name: ";
    echo $exists ? "✅ বিদ্যমান" : "❌ নেই";
    if ($exists) {
        echo $writable ? " (লেখার অনুমতি আছে)" : " (লেখার অনুমতি নেই)";
    }
    echo "</p>";
}

echo "</div></div>";

// Action buttons
echo "<div class='text-center'>";
echo "<a href='index.php' class='btn btn-primary me-2'><i class='fas fa-home'></i> হোম পেজে যান</a>";
echo "<a href='teacher_duty_management.php' class='btn btn-success me-2'><i class='fas fa-users'></i> শিক্ষক ব্যবস্থাপনা</a>";
echo "<a href='duty_letter_generator.php' class='btn btn-info me-2'><i class='fas fa-file-alt'></i> ডিউটি লেটার</a>";
echo "<a href='signature_upload.php' class='btn btn-warning'><i class='fas fa-signature'></i> স্বাক্ষর আপলোড</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
