<?php
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

// Handle fix request
if (isset($_POST['fix_sl_numbers'])) {
    try {
        $pdo->beginTransaction();
        
        // Get all teachers without proper sl_number
        $stmt = $pdo->query("SELECT id, name FROM teachers WHERE sl_number IS NULL OR sl_number = '' OR sl_number = '0' ORDER BY id");
        $teachers = $stmt->fetchAll();
        
        if (!empty($teachers)) {
            // Get the highest existing sl_number
            $stmt = $pdo->query("SELECT MAX(CAST(sl_number AS UNSIGNED)) as max_sl FROM teachers WHERE sl_number IS NOT NULL AND sl_number != '' AND sl_number != '0'");
            $result = $stmt->fetch();
            $nextSl = ($result['max_sl'] ?? 0) + 1;
            
            // Update each teacher with a new sl_number
            $updateStmt = $pdo->prepare("UPDATE teachers SET sl_number = ? WHERE id = ?");
            $updatedCount = 0;
            
            foreach ($teachers as $teacher) {
                $updateStmt->execute([$nextSl, $teacher['id']]);
                $nextSl++;
                $updatedCount++;
            }
            
            $pdo->commit();
            $message = "সফলভাবে {$updatedCount} জন শিক্ষকের SL Number ঠিক করা হয়েছে!";
            $messageType = 'success';
        } else {
            $message = "সব শিক্ষকের SL Number ইতিমধ্যে সঠিক আছে!";
            $messageType = 'info';
        }
    } catch (Exception $e) {
        $pdo->rollback();
        $message = "SL Number ঠিক করতে সমস্যা হয়েছে: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle manual assignment
if (isset($_POST['assign_manual'])) {
    try {
        $teacherId = $_POST['teacher_id'];
        $newSlNumber = $_POST['new_sl_number'];
        
        // Check if sl_number already exists
        $stmt = $pdo->prepare("SELECT id FROM teachers WHERE sl_number = ? AND id != ?");
        $stmt->execute([$newSlNumber, $teacherId]);
        
        if ($stmt->fetch()) {
            $message = "এই SL Number ইতিমধ্যে অন্য শিক্ষকের জন্য ব্যবহৃত হয়েছে!";
            $messageType = 'warning';
        } else {
            // Update the teacher's sl_number
            $stmt = $pdo->prepare("UPDATE teachers SET sl_number = ? WHERE id = ?");
            $stmt->execute([$newSlNumber, $teacherId]);
            
            $message = "SL Number সফলভাবে আপডেট করা হয়েছে!";
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = "SL Number আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get problematic teachers
$stmt = $pdo->query("SELECT id, name, sl_number, subject, designation FROM teachers WHERE sl_number IS NULL OR sl_number = '' OR sl_number = '0' ORDER BY id");
$problematicTeachers = $stmt->fetchAll();

// Get all teachers for reference
$stmt = $pdo->query("SELECT id, name, sl_number, subject, designation FROM teachers ORDER BY CAST(sl_number AS UNSIGNED), id");
$allTeachers = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SL Number ঠিক করুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .teacher-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .sl-input {
            width: 80px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-wrench me-3"></i>SL Number ঠিক করুন
                    </h1>
                    <p class="text-white-50">শিক্ষকদের ক্রমিক নম্বর সংশোধন করুন</p>
                </div>

                <!-- Navigation -->
                <div class="text-center mb-4">
                    <a href="teacher_duty_management.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনা
                    </a>
                    <a href="check_teacher_data.php" class="btn btn-outline-light">
                        <i class="fas fa-search me-2"></i>ডেটা চেক করুন
                    </a>
                </div>

                <!-- Message Display -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <strong><?php echo $message; ?></strong>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Auto Fix Section -->
                <?php if (!empty($problematicTeachers)): ?>
                    <div class="main-card">
                        <h4 class="mb-4">
                            <i class="fas fa-magic text-warning me-2"></i>স্বয়ংক্রিয় সমাধান
                        </h4>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong><?php echo count($problematicTeachers); ?> জন শিক্ষকের SL Number সমস্যা আছে!</strong>
                        </div>
                        
                        <p>স্বয়ংক্রিয়ভাবে সব শিক্ষকের জন্য ক্রমিক SL Number বরাদ্দ করুন:</p>
                        
                        <form method="POST" onsubmit="return confirm('আপনি কি নিশ্চিত যে সব SL Number স্বয়ংক্রিয়ভাবে ঠিক করতে চান?')">
                            <button type="submit" name="fix_sl_numbers" class="btn btn-warning">
                                <i class="fas fa-magic me-2"></i>স্বয়ংক্রিয় ঠিক করুন
                            </button>
                        </form>
                    </div>

                    <!-- Manual Fix Section -->
                    <div class="main-card">
                        <h4 class="mb-4">
                            <i class="fas fa-edit text-primary me-2"></i>ম্যানুয়াল সংশোধন
                        </h4>
                        
                        <p>প্রতিটি শিক্ষকের জন্য আলাদাভাবে SL Number নির্ধারণ করুন:</p>
                        
                        <?php foreach ($problematicTeachers as $teacher): ?>
                            <div class="teacher-item">
                                <form method="POST" class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <strong><?php echo htmlspecialchars($teacher['name']); ?></strong><br>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($teacher['subject']); ?> - 
                                            <?php echo htmlspecialchars($teacher['designation']); ?>
                                        </small><br>
                                        <small class="text-danger">বর্তমান SL: <?php echo $teacher['sl_number'] ?: 'নেই'; ?></small>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <input type="hidden" name="teacher_id" value="<?php echo $teacher['id']; ?>">
                                        <label class="form-label mb-0">নতুন SL:</label>
                                        <input type="number" name="new_sl_number" class="form-control sl-input" min="1" required>
                                        <button type="submit" name="assign_manual" class="btn btn-sm btn-primary">
                                            <i class="fas fa-save"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="main-card">
                        <div class="alert alert-success text-center">
                            <i class="fas fa-check-circle fa-3x mb-3"></i>
                            <h4>সব ঠিক আছে!</h4>
                            <p>সব শিক্ষকের SL Number সঠিকভাবে বরাদ্দ করা আছে।</p>
                            <a href="teacher_duty_management.php" class="btn btn-success">
                                <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনায় ফিরে যান
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Current Teachers List -->
                <div class="main-card">
                    <h4 class="mb-4">
                        <i class="fas fa-list text-info me-2"></i>বর্তমান শিক্ষক তালিকা
                    </h4>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>নাম</th>
                                    <th>বিষয়</th>
                                    <th>পদবী</th>
                                    <th>স্ট্যাটাস</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($allTeachers as $teacher): ?>
                                    <tr class="<?php echo (empty($teacher['sl_number']) || $teacher['sl_number'] == '0') ? 'table-warning' : ''; ?>">
                                        <td>
                                            <strong><?php echo $teacher['sl_number'] ?: 'নেই'; ?></strong>
                                        </td>
                                        <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                        <td>
                                            <?php if (empty($teacher['sl_number']) || $teacher['sl_number'] == '0'): ?>
                                                <span class="badge bg-warning">সমস্যা</span>
                                            <?php else: ?>
                                                <span class="badge bg-success">ঠিক</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
