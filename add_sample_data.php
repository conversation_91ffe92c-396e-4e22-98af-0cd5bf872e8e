<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $student = new Student();
        
        // Sample data for testing
        $sampleStudents = [
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101001',
                'reg_no' => 'REG2024001',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Science',
                'student_name' => 'মোহাম্মদ রহিম উদ্দিন',
                'father_name' => 'আব্দুল করিম',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '174',
                'sub_5' => '176', 'sub_6' => '178', 'sub_7' => '265', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101002',
                'reg_no' => 'REG2024002',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Science',
                'student_name' => 'ফাতেমা খাতুন',
                'father_name' => 'মোহাম্মদ আলী',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '174',
                'sub_5' => '176', 'sub_6' => '178', 'sub_7' => '265', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101003',
                'reg_no' => 'REG2024003',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Humanities',
                'student_name' => 'আব্দুল হামিদ',
                'father_name' => 'নুরুল ইসলাম',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '109',
                'sub_5' => '121', 'sub_6' => '117', 'sub_7' => '304', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101004',
                'reg_no' => 'REG2024004',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Business Studies',
                'student_name' => 'সালমা বেগম',
                'father_name' => 'আব্দুর রহমান',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '253',
                'sub_5' => '292', 'sub_6' => '277', 'sub_7' => '286', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101005',
                'reg_no' => 'REG2024005',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Science',
                'student_name' => 'মোহাম্মদ কামাল',
                'father_name' => 'আব্দুল জলিল',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '174',
                'sub_5' => '176', 'sub_6' => '178', 'sub_7' => '265', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101006',
                'reg_no' => 'REG2024006',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Humanities',
                'student_name' => 'রাশিদা খাতুন',
                'father_name' => 'মোহাম্মদ ইউসুফ',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '109',
                'sub_5' => '121', 'sub_6' => '117', 'sub_7' => '304', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101007',
                'reg_no' => 'REG2024007',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Business Studies',
                'student_name' => 'আহমেদ হাসান',
                'father_name' => 'মোহাম্মদ নাসির',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '253',
                'sub_5' => '292', 'sub_6' => '277', 'sub_7' => '286', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101008',
                'reg_no' => 'REG2024008',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Science',
                'student_name' => 'নাসরিন আক্তার',
                'father_name' => 'আব্দুল মজিদ',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '174',
                'sub_5' => '176', 'sub_6' => '178', 'sub_7' => '265', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101009',
                'reg_no' => 'REG2024009',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Humanities',
                'student_name' => 'মোহাম্মদ জামাল',
                'father_name' => 'আব্দুল হক',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '109',
                'sub_5' => '121', 'sub_6' => '117', 'sub_7' => '304', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101010',
                'reg_no' => 'REG2024010',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Business Studies',
                'student_name' => 'সুমাইয়া খাতুন',
                'father_name' => 'মোহাম্মদ শফিক',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '253',
                'sub_5' => '292', 'sub_6' => '277', 'sub_7' => '286', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101011',
                'reg_no' => 'REG2024011',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Science',
                'student_name' => 'তানভীর আহমেদ',
                'father_name' => 'আব্দুল গফুর',
                'gender' => 'Male',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '174',
                'sub_5' => '176', 'sub_6' => '178', 'sub_7' => '265', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ],
            [
                'c_code' => '295',
                'eiin' => '123456',
                'roll_no' => '101012',
                'reg_no' => 'REG2024012',
                'session' => '2023-24',
                'type' => 'Regular',
                'group_name' => 'Humanities',
                'student_name' => 'রুমানা আক্তার',
                'father_name' => 'মোহাম্মদ বশির',
                'gender' => 'Female',
                'sub_1' => '101', 'sub_2' => '107', 'sub_3' => '275', 'sub_4' => '109',
                'sub_5' => '121', 'sub_6' => '117', 'sub_7' => '304', 'sub_8' => '',
                'sub_9' => '', 'sub_10' => '', 'sub_11' => '', 'sub_12' => '', 'sub_13' => ''
            ]
        ];
        
        if ($student->bulkInsert($sampleStudents)) {
            $count = count($sampleStudents);
            $message = "সফলভাবে {$count} জন নমুনা শিক্ষার্থী যোগ করা হয়েছে!";
            $messageType = 'success';
        } else {
            $message = 'নমুনা ডেটা যোগ করতে সমস্যা হয়েছে।';
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get current student count
$student = new Student();
$students = $student->getAll();
$totalStudents = count($students);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Sample Data - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <h2>🧪 Add Sample Data</h2>
                    <p class="text-muted">Add sample student data for testing the seat card system</p>
                </div>
            </div>

            <!-- Message Display -->
            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Current Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h4 class="text-info"><?php echo $totalStudents; ?></h4>
                            <p class="mb-0">বর্তমান শিক্ষার্থী সংখ্যা</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h4 class="text-success">12</h4>
                            <p class="mb-0">নমুনা ডেটা যোগ হবে</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Sample Data Form -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>🎯 Sample Data Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> নমুনা ডেটা সম্পর্কে:</h6>
                                <ul class="mb-0">
                                    <li>১২ জন শিক্ষার্থীর নমুনা ডেটা যোগ হবে</li>
                                    <li>বিভিন্ন বিভাগ: Science, Humanities, Business Studies</li>
                                    <li>রোল নম্বর: 101001 থেকে 101012</li>
                                    <li>সেশন: 2023-24</li>
                                    <li>বাংলা নাম সহ সম্পূর্ণ তথ্য</li>
                                </ul>
                            </div>
                            
                            <form method="POST" class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-plus-circle"></i> নমুনা ডেটা যোগ করুন
                                </button>
                            </form>
                            
                            <div class="mt-4 text-center">
                                <a href="seat_card_generator.php" class="btn btn-success">
                                    <i class="fas fa-id-card"></i> সীটকার্ড জেনারেটরে যান
                                </a>
                                <a href="all_students.php" class="btn btn-info">
                                    <i class="fas fa-users"></i> সকল শিক্ষার্থী দেখুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
