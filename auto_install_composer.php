<?php
/**
 * Automatic Composer Installer
 * This script automatically downloads and installs Composer and dependencies
 */

set_time_limit(300); // 5 minutes timeout
ini_set('memory_limit', '512M');

$status = [];
$errors = [];
$composerInstalled = false;
$dependenciesInstalled = false;

// Check current status
$composerExists = file_exists(__DIR__ . '/composer.phar') || isComposerInPath();
$vendorExists = file_exists(__DIR__ . '/vendor/autoload.php');

function isComposerInPath() {
    $output = [];
    $return_var = 0;
    exec('composer --version 2>&1', $output, $return_var);
    return $return_var === 0;
}

function downloadComposer() {
    global $status, $errors;
    
    $status[] = "Downloading Composer installer...";
    
    // Download composer-setup.php
    $setupUrl = 'https://getcomposer.org/installer';
    $setupContent = @file_get_contents($setupUrl);
    
    if ($setupContent === false) {
        $errors[] = "Failed to download Composer installer from $setupUrl";
        return false;
    }
    
    file_put_contents(__DIR__ . '/composer-setup.php', $setupContent);
    $status[] = "Composer installer downloaded successfully.";
    
    return true;
}

function installComposer() {
    global $status, $errors;
    
    $status[] = "Installing Composer...";
    
    // Run composer installer
    $output = [];
    $return_var = 0;
    
    $command = 'php composer-setup.php --install-dir=' . escapeshellarg(__DIR__) . ' --filename=composer.phar 2>&1';
    exec($command, $output, $return_var);
    
    if ($return_var !== 0) {
        $errors[] = "Composer installation failed: " . implode("\n", $output);
        return false;
    }
    
    $status[] = "Composer installed successfully.";
    
    // Clean up installer
    if (file_exists(__DIR__ . '/composer-setup.php')) {
        unlink(__DIR__ . '/composer-setup.php');
    }
    
    return true;
}

function installDependencies() {
    global $status, $errors;
    
    $status[] = "Installing project dependencies...";
    
    $composerPath = __DIR__ . '/composer.phar';
    if (!file_exists($composerPath)) {
        // Try global composer
        $composerPath = 'composer';
    } else {
        $composerPath = 'php ' . escapeshellarg($composerPath);
    }
    
    $output = [];
    $return_var = 0;
    
    // Change to project directory and run composer install
    $oldDir = getcwd();
    chdir(__DIR__);
    
    $command = $composerPath . ' install --no-dev --optimize-autoloader 2>&1';
    exec($command, $output, $return_var);
    
    chdir($oldDir);
    
    if ($return_var !== 0) {
        $errors[] = "Dependencies installation failed: " . implode("\n", $output);
        return false;
    }
    
    $status[] = "Dependencies installed successfully.";
    return true;
}

// Handle installation request
if (isset($_POST['install'])) {
    try {
        // Step 1: Download Composer if not exists
        if (!$composerExists && !isComposerInPath()) {
            if (downloadComposer()) {
                if (installComposer()) {
                    $composerInstalled = true;
                }
            }
        } else {
            $status[] = "Composer already available.";
            $composerInstalled = true;
        }
        
        // Step 2: Install dependencies
        if ($composerInstalled || $composerExists || isComposerInPath()) {
            if (installDependencies()) {
                $dependenciesInstalled = true;
            }
        }
        
        if ($dependenciesInstalled) {
            $status[] = "✅ Installation completed successfully!";
        }
        
    } catch (Exception $e) {
        $errors[] = "Installation error: " . $e->getMessage();
    }
}

// Re-check status after installation
$composerExists = file_exists(__DIR__ . '/composer.phar') || isComposerInPath();
$vendorExists = file_exists(__DIR__ . '/vendor/autoload.php');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Install Composer - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .status-log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .progress-step {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .step-completed {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .step-error {
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .step-pending {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white text-center">
                        <h3><i class="fas fa-magic"></i> Auto Install Composer & Dependencies</h3>
                        <p class="mb-0">One-click installation for Excel upload functionality</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- Current Status -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="alert <?php echo $composerExists ? 'alert-success' : 'alert-warning'; ?>">
                                    <i class="fas <?php echo $composerExists ? 'fa-check' : 'fa-clock'; ?>"></i>
                                    <strong>Composer:</strong> <?php echo $composerExists ? 'Available' : 'Not Found'; ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert <?php echo $vendorExists ? 'alert-success' : 'alert-warning'; ?>">
                                    <i class="fas <?php echo $vendorExists ? 'fa-check' : 'fa-clock'; ?>"></i>
                                    <strong>Dependencies:</strong> <?php echo $vendorExists ? 'Installed' : 'Not Installed'; ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="alert <?php echo ($composerExists && $vendorExists) ? 'alert-success' : 'alert-info'; ?>">
                                    <i class="fas <?php echo ($composerExists && $vendorExists) ? 'fa-check' : 'fa-info'; ?>"></i>
                                    <strong>Excel Upload:</strong> <?php echo ($composerExists && $vendorExists) ? 'Ready' : 'Pending'; ?>
                                </div>
                            </div>
                        </div>

                        <?php if ($vendorExists): ?>
                            <!-- Installation Complete -->
                            <div class="alert alert-success text-center">
                                <h4><i class="fas fa-check-circle"></i> Installation Complete!</h4>
                                <p>Excel upload functionality is now available.</p>
                                <div class="mt-3">
                                    <a href="upload.php" class="btn btn-success btn-lg me-2">
                                        <i class="fas fa-upload"></i> Upload Excel/CSV
                                    </a>
                                    <a href="view_students.php" class="btn btn-primary btn-lg">
                                        <i class="fas fa-users"></i> View Students
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Installation Options -->
                            <div class="text-center mb-4">
                                <h5><i class="fas fa-download"></i> Installation Options</h5>
                                <p class="text-muted">Choose your preferred installation method:</p>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-magic fa-2x text-primary mb-3"></i>
                                                <h6>Web Installer</h6>
                                                <p class="small text-muted">Install directly from browser</p>
                                                <form method="POST" id="installForm">
                                                    <button type="submit" name="install" class="btn btn-primary" id="installBtn">
                                                        <i class="fas fa-magic"></i> Auto Install
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-terminal fa-2x text-success mb-3"></i>
                                                <h6>Command Line</h6>
                                                <p class="small text-muted">Run batch script</p>
                                                <a href="install_composer.bat" download class="btn btn-success">
                                                    <i class="fas fa-download"></i> Download .bat
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-code fa-2x text-info mb-3"></i>
                                                <h6>PowerShell</h6>
                                                <p class="small text-muted">Advanced PowerShell script</p>
                                                <a href="install_composer.ps1" download class="btn btn-info">
                                                    <i class="fas fa-download"></i> Download .ps1
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Installation Instructions -->
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="alert alert-light">
                                        <h6><i class="fas fa-globe"></i> Web Installer</h6>
                                        <small>Click "Auto Install" button above. Works directly in browser.</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-light">
                                        <h6><i class="fas fa-terminal"></i> Batch Script</h6>
                                        <small>Download .bat file and double-click to run. Works on Windows.</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="alert alert-light">
                                        <h6><i class="fas fa-code"></i> PowerShell</h6>
                                        <small>Download .ps1 file. Right-click → "Run with PowerShell".</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Installation Steps Preview -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-list"></i> Installation Steps:</h6>
                                    <div class="progress-step <?php echo $composerExists ? 'step-completed' : 'step-pending'; ?>">
                                        <i class="fas fa-download"></i> Download Composer
                                    </div>
                                    <div class="progress-step step-pending">
                                        <i class="fas fa-cog"></i> Install Composer
                                    </div>
                                    <div class="progress-step step-pending">
                                        <i class="fas fa-box"></i> Install Dependencies
                                    </div>
                                    <div class="progress-step step-pending">
                                        <i class="fas fa-check"></i> Verify Installation
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle"></i> What will be installed:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> Composer (PHP package manager)</li>
                                        <li><i class="fas fa-check text-success"></i> PhpSpreadsheet (Excel processing)</li>
                                        <li><i class="fas fa-check text-success"></i> Required dependencies</li>
                                    </ul>

                                    <div class="alert alert-info">
                                        <small>
                                            <i class="fas fa-clock"></i> Installation may take 2-5 minutes depending on your internet connection.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Installation Log -->
                        <?php if (!empty($status) || !empty($errors)): ?>
                            <div class="mt-4">
                                <h6><i class="fas fa-terminal"></i> Installation Log:</h6>
                                <div class="status-log">
                                    <?php foreach ($status as $msg): ?>
                                        <div style="color: #68d391;">✓ <?php echo htmlspecialchars($msg); ?></div>
                                    <?php endforeach; ?>
                                    <?php foreach ($errors as $error): ?>
                                        <div style="color: #fc8181;">✗ <?php echo htmlspecialchars($error); ?></div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Manual Installation Fallback -->
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-warning mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> Manual Installation Required</h6>
                                <p>Automatic installation failed. Please try manual installation:</p>
                                <ol>
                                    <li>Download Composer from <a href="https://getcomposer.org/download/" target="_blank">getcomposer.org</a></li>
                                    <li>Open command prompt in project directory</li>
                                    <li>Run: <code>composer install</code></li>
                                </ol>
                                <a href="install_composer_dependencies.php" class="btn btn-outline-primary">
                                    <i class="fas fa-book"></i> View Manual Instructions
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="text-center mt-4">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-home"></i> Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('installForm')?.addEventListener('submit', function() {
            const btn = document.getElementById('installBtn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Installing...';
            btn.disabled = true;
        });
    </script>
</body>
</html>
