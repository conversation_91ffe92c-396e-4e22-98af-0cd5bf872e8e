<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Upload সমস্যার স্থায়ী সমাধান - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .step-card {
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
        }
        
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .alert-custom {
            border-left: 4px solid #28a745;
            background: #d4edda;
            border-color: #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2 class="mb-0">
                            <i class="fas fa-tools me-2"></i>
                            CSV Upload সমস্যার স্থায়ী সমাধান
                        </h2>
                        <small>PC restart এর পর CSV upload কাজ না করার সমাধান</small>
                    </div>
                    <div class="card-body">
                        
                        <div class="alert alert-custom">
                            <h5><i class="fas fa-info-circle me-2"></i>সমস্যার কারণসমূহ:</h5>
                            <ul class="mb-0">
                                <li>PC restart এর পর XAMPP services বন্ধ হয়ে যায়</li>
                                <li>Database connection হারিয়ে যায়</li>
                                <li>File permissions পরিবর্তন হয়</li>
                                <li>Uploads directory তৈরি হয় না</li>
                            </ul>
                        </div>
                        
                        <h4><i class="fas fa-cog me-2"></i>স্থায়ী সমাধানের পদক্ষেপসমূহ:</h4>
                        
                        <!-- Step 1 -->
                        <div class="card step-card">
                            <div class="card-body">
                                <h5><span class="step-number">1</span>XAMPP Auto Start Setup</h5>
                                <p>PC চালু হওয়ার সাথে সাথে XAMPP automatically start হওয়ার জন্য:</p>
                                
                                <h6>Method 1: Windows Startup Folder</h6>
                                <ol>
                                    <li><kbd>Win + R</kbd> চেপে <code>shell:startup</code> লিখুন</li>
                                    <li>Startup folder খুলবে</li>
                                    <li><code>xampp_auto_start.bat</code> file টি এই folder এ copy করুন</li>
                                </ol>
                                
                                <h6>Method 2: XAMPP Control Panel</h6>
                                <ol>
                                    <li>XAMPP Control Panel খুলুন</li>
                                    <li>Apache এবং MySQL এর পাশে <strong>Service</strong> button এ click করুন</li>
                                    <li>এতে Windows service হিসেবে install হবে</li>
                                </ol>
                                
                                <div class="code-block">
                                    <strong>Download Files:</strong><br>
                                    <a href="xampp_auto_start.bat" class="btn btn-sm btn-primary">📥 xampp_auto_start.bat</a>
                                    <a href="auto_startup_fix.bat" class="btn btn-sm btn-success">📥 auto_startup_fix.bat</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2 -->
                        <div class="card step-card">
                            <div class="card-body">
                                <h5><span class="step-number">2</span>Database Auto-Creation Setup</h5>
                                <p>Database এবং table automatically তৈরি হওয়ার জন্য আমরা ইতিমধ্যে code update করেছি।</p>
                                <ul>
                                    <li>✅ Database connection এ retry mechanism যোগ করা হয়েছে</li>
                                    <li>✅ Database না থাকলে automatically তৈরি হবে</li>
                                    <li>✅ Students table না থাকলে automatically তৈরি হবে</li>
                                    <li>✅ Better error messages যোগ করা হয়েছে</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- Step 3 -->
                        <div class="card step-card">
                            <div class="card-body">
                                <h5><span class="step-number">3</span>System Check Tools</h5>
                                <p>PC restart এর পর system check করার জন্য:</p>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <a href="system_startup_check.php" class="btn btn-success w-100 mb-2">
                                            <i class="fas fa-check-circle me-2"></i>System Startup Check
                                        </a>
                                        <small class="text-muted">সব কিছু ঠিক আছে কিনা check করুন</small>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="db_connection_test.php" class="btn btn-info w-100 mb-2">
                                            <i class="fas fa-database me-2"></i>Database Connection Test
                                        </a>
                                        <small class="text-muted">Database connection test করুন</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4 -->
                        <div class="card step-card">
                            <div class="card-body">
                                <h5><span class="step-number">4</span>Daily Usage Routine</h5>
                                <p>প্রতিদিন PC চালু করার পর:</p>
                                <ol>
                                    <li><strong>XAMPP Check:</strong> XAMPP Control Panel দেখুন Apache ও MySQL চালু আছে কিনা</li>
                                    <li><strong>Quick Test:</strong> <a href="system_startup_check.php">System Check</a> page এ যান</li>
                                    <li><strong>CSV Upload Test:</strong> <a href="csv_upload.php">CSV Upload</a> page test করুন</li>
                                </ol>
                            </div>
                        </div>
                        
                        <!-- Step 5 -->
                        <div class="card step-card">
                            <div class="card-body">
                                <h5><span class="step-number">5</span>Emergency Solutions</h5>
                                <p>যদি এখনও সমস্যা হয়:</p>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <button onclick="runAutoFix()" class="btn btn-warning w-100 mb-2">
                                            <i class="fas fa-magic me-2"></i>Auto Fix
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <a href="setup_database.php" class="btn btn-danger w-100 mb-2">
                                            <i class="fas fa-database me-2"></i>Reset Database
                                        </a>
                                    </div>
                                    <div class="col-md-4">
                                        <button onclick="restartXAMPP()" class="btn btn-secondary w-100 mb-2">
                                            <i class="fas fa-restart me-2"></i>Restart XAMPP
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>সফল হওয়ার লক্ষণ:</h5>
                            <ul class="mb-0">
                                <li>✅ PC restart এর পর XAMPP automatically চালু হবে</li>
                                <li>✅ CSV upload page কোন error ছাড়াই খুলবে</li>
                                <li>✅ Database connection automatically establish হবে</li>
                                <li>✅ File upload কাজ করবে</li>
                            </ul>
                        </div>
                        
                        <div class="text-center">
                            <a href="csv_upload.php" class="btn btn-primary btn-lg">
                                <i class="fas fa-upload me-2"></i>এখনই CSV Upload Test করুন
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runAutoFix() {
            if (confirm('Auto fix script চালাতে চান? এটি XAMPP restart করবে।')) {
                window.open('auto_startup_fix.bat', '_blank');
            }
        }
        
        function restartXAMPP() {
            alert('XAMPP Control Panel খুলে Apache ও MySQL stop করে আবার start করুন।');
        }
    </script>
</body>
</html>
