<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EXMM - Student Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4f46e5;
            --secondary-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-4: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .hero-section {
            background: var(--gradient-1);
            color: white;
            padding: 4rem 0;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 1rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .quick-action-btn {
            background: white;
            border: 2px solid transparent;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.3s ease;
            display: block;
            text-align: center;
            font-weight: 500;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            color: var(--dark-color);
        }

        .system-status {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .main-content {
            margin-left: 0;
            padding: 0;
        }

        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <!-- Hero Section -->
        <div class="hero-section">
            <div class="container">
                <div class="hero-content text-center">
                    <h1 class="display-4 fw-bold mb-3">
                        <i class="fas fa-graduation-cap me-3"></i>
                        EXMM Student Management System
                    </h1>
                    <p class="lead mb-4">Complete solution for student data management, seating arrangements, and academic administration</p>

                    <!-- System Status -->
                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="system-status">
                                <div class="row text-center">
                                    <div class="col-md-4">
                                        <i class="fas fa-server fa-2x mb-2"></i>
                                        <h6>PHP <?php echo PHP_VERSION; ?></h6>
                                        <small>Server Ready</small>
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-clock fa-2x mb-2"></i>
                                        <h6><?php echo date('H:i:s'); ?></h6>
                                        <small><?php echo date('M d, Y'); ?></small>
                                    </div>
                                    <div class="col-md-4">
                                        <i class="fas fa-database fa-2x mb-2"></i>
                                        <h6>Database</h6>
                                        <small>Connected</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- Main Features -->
        <div class="container mb-5">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold text-white mb-3">Core Features</h2>
                    <p class="text-white-50">Essential tools for student management</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- All Students -->
                <div class="col-lg-2 col-md-4 col-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: var(--gradient-1);">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="fw-bold mb-3">All Students</h5>
                        <p class="text-muted mb-4">View and manage all student records.</p>
                        <div class="d-grid">
                            <a href="view_students.php" class="btn btn-primary">
                                <i class="fas fa-list me-2"></i>View All
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filter by Subject -->
                <div class="col-lg-2 col-md-4 col-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: var(--gradient-2);">
                            <i class="fas fa-filter"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Filter by Subject</h5>
                        <p class="text-muted mb-4">Filter students by subject codes.</p>
                        <div class="d-grid">
                            <a href="subject_filter.php?code=101" class="btn btn-danger">
                                <i class="fas fa-search me-2"></i>Subject Filter
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Seat Card Generate -->
                <div class="col-lg-2 col-md-4 col-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Seat Card Generate</h5>
                        <p class="text-muted mb-4">HSC Exam-2025 এর জন্য প্রিন্টযোগ্য সীটকার্ড তৈরী করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="seat_card_generator.php" class="btn btn-warning">
                                <i class="fas fa-print me-2"></i>Generate Cards
                            </a>
                            <small class="text-muted">৪টি ভিন্ন ডিজাইন • ১২ কার্ড/পৃষ্ঠা</small>
                        </div>
                    </div>
                </div>

                <!-- Add Student -->
                <div class="col-lg-2 col-md-4 col-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: var(--gradient-4);">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Add Student</h5>
                        <p class="text-muted mb-4">Add new student records.</p>
                        <div class="d-grid">
                            <a href="add_student.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>Add Student
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 13 Subject Students -->
                <div class="col-lg-2 col-md-4 col-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <h5 class="fw-bold mb-3">১৩ বিষয়ের শিক্ষার্থী</h5>
                        <p class="text-muted mb-4">যাদের ১৩টি বিষয় আছে।</p>
                        <div class="d-grid">
                            <a href="thirteen_subject_students.php" class="btn btn-purple" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%); border: none; color: white;">
                                <i class="fas fa-star me-2"></i>১৩ বিষয়
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Features -->
        <div class="container mb-5">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold text-white mb-3">Advanced Features</h2>
                    <p class="text-white-50">Professional tools for exam management</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- Dynamic Seat Plan -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-th-large"></i>
                        </div>
                        <h5 class="fw-bold mb-3">ডাইনামিক সীট প্লান</h5>
                        <p class="text-muted mb-4">রুম ওয়াইজ কাস্টমাইজেবল সীটিং অ্যারেঞ্জমেন্ট। ঝিকঝাক, কোনাকোনি, diagonal এবং serial প্যাটার্ন সহ।</p>
                        <div class="d-grid gap-2">
                            <a href="dynamic_seat_plan.php" class="btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white;">
                                <i class="fas fa-cog me-2"></i>সীট প্লান কনফিগার করুন
                            </a>
                            <a href="demo_seat_plan_features.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-magic me-2"></i>ফিচার ডেমো
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>কাস্টম রুম কনফিগারেশন<br>
                                <i class="fas fa-check text-success me-1"></i>৪টি সীটিং প্যাটার্ন<br>
                                <i class="fas fa-check text-success me-1"></i>প্রিন্ট অপ্টিমাইজড
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Upload Tools -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fas fa-upload"></i>
                        </div>
                        <h5 class="fw-bold mb-3">Upload Tools</h5>
                        <p class="text-muted mb-4">Excel এবং CSV ফাইল আপলোড করে bulk data import করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="upload.php" class="btn" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; color: white;">
                                <i class="fas fa-file-excel me-2"></i>Excel Upload
                            </a>
                            <a href="simple_csv_upload.php" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-file-csv me-2"></i>CSV Upload
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Incomplete Subject Students -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h5 class="fw-bold mb-3">অসম্পূর্ণ বিষয়ের শিক্ষার্থী</h5>
                        <p class="text-muted mb-4">যাদের সব বিষয় কোড পূরণ হয়নি তাদের তালিকা।</p>
                        <div class="d-grid">
                            <a href="incomplete_subject_students.php" class="btn" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border: none; color: #333;">
                                <i class="fas fa-list me-2"></i>অসম্পূর্ণ তালিকা
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Teacher Management Section -->
        <div class="container mb-5">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold text-white mb-3">👨‍🏫 শিক্ষক ব্যবস্থাপনা</h2>
                    <p class="text-white-50">পরীক্ষার ডিউটি ও রুম বন্টন ব্যবস্থাপনা</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- Teacher Duty Management -->
                <div class="col-lg-6 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <h5 class="fw-bold mb-3">শিক্ষক ডিউটি ব্যবস্থাপনা</h5>
                        <p class="text-muted mb-4">CSV আপলোড, ডিউটি স্ট্যাটাস নির্ধারণ ও তারিখ ভিত্তিক ডিউটি বন্টন করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="teacher_duty_management.php" class="btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; color: white;">
                                <i class="fas fa-users-cog me-2"></i>ডিউটি ব্যবস্থাপনা
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>CSV ফাইল আপলোড<br>
                                <i class="fas fa-check text-success me-1"></i>স্ট্যাটাস ম্যানেজমেন্ট<br>
                                <i class="fas fa-check text-success me-1"></i>তারিখ ভিত্তিক বন্টন
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Room Wise Duty Assignment -->
                <div class="col-lg-6 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                            <i class="fas fa-door-open"></i>
                        </div>
                        <h5 class="fw-bold mb-3">রুম ওয়াইজ ডিউটি বন্টন</h5>
                        <p class="text-muted mb-4">ড্রাগ অ্যান্ড ড্রপ দিয়ে শিক্ষকদের রুম অনুযায়ী বন্টন করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="room_wise_duty_assignment.php" class="btn" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none; color: white;">
                                <i class="fas fa-door-closed me-2"></i>রুম বন্টন
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>ড্রাগ অ্যান্ড ড্রপ UI<br>
                                <i class="fas fa-check text-success me-1"></i>রুম কনফিগারেশন<br>
                                <i class="fas fa-check text-success me-1"></i>প্রিন্ট রেডি আউটপুট
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Document Generation Section -->
        <div class="container mb-5">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="fw-bold text-white mb-3">📄 ডকুমেন্ট জেনারেশন</h2>
                    <p class="text-white-50">ডিউটি লেটার ও আইডি কার্ড তৈরি করুন</p>
                </div>
            </div>

            <div class="row g-4 justify-content-center">
                <!-- Duty Letter Generator -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h5 class="fw-bold mb-3">ডিউটি লেটার জেনারেটর</h5>
                        <p class="text-muted mb-4">কাস্টমাইজেবল হেডার, স্মারক নম্বর ও স্বাক্ষর সহ ডিউটি লেটার তৈরি করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="duty_letter_generator.php" class="btn" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none; color: white;">
                                <i class="fas fa-file-alt me-2"></i>লেটার তৈরি করুন
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>কাস্টম হেডার<br>
                                <i class="fas fa-check text-success me-1"></i>স্বাক্ষর সাপোর্ট<br>
                                <i class="fas fa-check text-success me-1"></i>প্রিন্ট অপ্টিমাইজড
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Teacher ID Card Generator -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <h5 class="fw-bold mb-3">শিক্ষক আইডি কার্ড</h5>
                        <p class="text-muted mb-4">পরীক্ষার তত্ত্বাবধায়ক আইডি কার্ড ছবি ও লোগো সহ তৈরি করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="teacher_id_card_generator.php" class="btn" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border: none; color: white;">
                                <i class="fas fa-calendar-alt me-2"></i>তারিখ ভিত্তিক কার্ড
                            </a>
                            <a href="all_teachers_id_cards.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-users me-2"></i>সকল শিক্ষকের কার্ড
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>ছবি ও লোগো সহ<br>
                                <i class="fas fa-check text-success me-1"></i>২ কার্ড/পৃষ্ঠা<br>
                                <i class="fas fa-check text-success me-1"></i>প্রফেশনাল ডিজাইন
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Signature Upload -->
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                            <i class="fas fa-signature"></i>
                        </div>
                        <h5 class="fw-bold mb-3">স্বাক্ষর আপলোড</h5>
                        <p class="text-muted mb-4">প্রিন্সিপাল ও কনভেনার এর স্বাক্ষর আপলোড ও ব্যবস্থাপনা করুন।</p>
                        <div class="d-grid gap-2">
                            <a href="signature_upload.php" class="btn" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border: none; color: #333;">
                                <i class="fas fa-signature me-2"></i>স্বাক্ষর আপলোড
                            </a>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-check text-success me-1"></i>প্রিন্সিপাল স্বাক্ষর<br>
                                <i class="fas fa-check text-success me-1"></i>কনভেনার স্বাক্ষর<br>
                                <i class="fas fa-check text-success me-1"></i>অটো ইন্টিগ্রেশন
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

















        <!-- Footer -->
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="text-center text-white py-4">
                        <div class="row">
                            <div class="col-md-4">
                                <h6 class="fw-bold mb-3">EXMM System</h6>
                                <p class="small text-white-50">Complete student management solution with advanced features for academic institutions.</p>
                            </div>
                            <div class="col-md-4">
                                <h6 class="fw-bold mb-3">Quick Links</h6>
                                <div class="d-flex flex-column">
                                    <a href="view_students.php" class="text-white-50 text-decoration-none mb-1">All Students</a>
                                    <a href="subject_filter.php?code=101" class="text-white-50 text-decoration-none mb-1">Filter by Subject</a>
                                    <a href="seat_card_generator.php" class="text-white-50 text-decoration-none mb-1">Seat Card Generate</a>
                                    <a href="dynamic_seat_plan.php" class="text-white-50 text-decoration-none mb-1">ডাইনামিক সীট প্লান</a>
                                    <a href="add_student.php" class="text-white-50 text-decoration-none mb-1">Add Student</a>
                                    <a href="thirteen_subject_students.php" class="text-white-50 text-decoration-none mb-1">১৩ বিষয়ের শিক্ষার্থী</a>
                                    <a href="teacher_duty_management.php" class="text-white-50 text-decoration-none mb-1">শিক্ষক ডিউটি ব্যবস্থাপনা</a>
                                    <a href="duty_letter_generator.php" class="text-white-50 text-decoration-none mb-1">ডিউটি লেটার জেনারেটর</a>
                                    <a href="teacher_id_card_generator.php" class="text-white-50 text-decoration-none mb-1">শিক্ষক আইডি কার্ড</a>
                                    <a href="signature_upload.php" class="text-white-50 text-decoration-none mb-1">স্বাক্ষর আপলোড</a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="fw-bold mb-3">System Info</h6>
                                <p class="small text-white-50">
                                    Version 2.0<br>
                                    PHP <?php echo PHP_VERSION; ?><br>
                                    <?php echo date('Y'); ?> © EXMM
                                </p>
                            </div>
                        </div>
                        <hr class="my-4 border-white-50">
                        <p class="small text-white-50 mb-0">
                            <i class="fas fa-heart text-danger"></i>
                            Built with modern web technologies for educational excellence
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on scroll
            const statsCards = document.querySelectorAll('.stats-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                    }
                });
            });

            statsCards.forEach(card => {
                observer.observe(card);
            });

            // Add hover effects to feature cards
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });

        // Add CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
