<?php
// Adaptive Teacher Honorarium Calculator
// This version will work with different possible database structures
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Initialize variables
$start_date = $_GET['start_date'] ?? '';
$end_date = $_GET['end_date'] ?? '';
$rate_per_duty = $_GET['rate_per_duty'] ?? 500; // Default rate per duty
$show_results = false;

// Process form submission
if (!empty($start_date) && !empty($end_date)) {
    $show_results = true;
    
    // Ensure dates are in the correct format for MySQL (YYYY-MM-DD)
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date)) {
        $start_date = date('Y-m-d', strtotime($start_date));
    }
    
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
        $end_date = date('Y-m-d', strtotime($end_date));
    }
}

// Function to detect table structure and adapt queries
function detectTableStructure($conn) {
    $structure = [
        'teachers_table' => '',
        'duties_table' => '',
        'teacher_id_field' => '',
        'teacher_name_field' => '',
        'designation_field' => '',
        'subject_field' => '',
        'duty_date_field' => '',
        'room_field' => '',
        'shift_field' => ''
    ];
    
    // Find tables related to teachers
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }
    
    // Look for teacher-related table
    foreach ($tables as $table) {
        $lowercase = strtolower($table);
        if (strpos($lowercase, 'teacher') !== false) {
            $structure['teachers_table'] = $table;
            
            // Find fields in teachers table
            $fields_result = $conn->query("DESCRIBE $table");
            while ($field = $fields_result->fetch_assoc()) {
                $field_name = strtolower($field['Field']);
                if ($field_name === 'id') {
                    $structure['teacher_id_field'] = 'id';
                }
                if (strpos($field_name, 'name') !== false) {
                    $structure['teacher_name_field'] = $field['Field'];
                }
                if (strpos($field_name, 'designation') !== false || strpos($field_name, 'position') !== false) {
                    $structure['designation_field'] = $field['Field'];
                }
                if (strpos($field_name, 'subject') !== false) {
                    $structure['subject_field'] = $field['Field'];
                }
            }
            
            break;
        }
    }
    
    // Look for duty-related table
    foreach ($tables as $table) {
        $lowercase = strtolower($table);
        if (strpos($lowercase, 'duty') !== false || strpos($lowercase, 'assign') !== false) {
            $structure['duties_table'] = $table;
            
            // Find fields in duty table
            $fields_result = $conn->query("DESCRIBE $table");
            while ($field = $fields_result->fetch_assoc()) {
                $field_name = strtolower($field['Field']);
                if (strpos($field_name, 'date') !== false) {
                    $structure['duty_date_field'] = $field['Field'];
                }
                if (strpos($field_name, 'teacher') !== false && strpos($field_name, 'id') !== false) {
                    $structure['teacher_id_field'] = $field['Field'];
                }
                if (strpos($field_name, 'room') !== false) {
                    $structure['room_field'] = $field['Field'];
                }
                if (strpos($field_name, 'shift') !== false) {
                    $structure['shift_field'] = $field['Field'];
                }
            }
            
            break;
        }
    }
    
    return $structure;
}

// Get teacher duty counts within date range
function getTeacherDutyCounts($conn, $start_date, $end_date, $structure) {
    $teacherDutyCounts = [];
    
    // Build dynamic SQL based on detected structure
    $teacher_table = $structure['teachers_table'];
    $duties_table = $structure['duties_table'];
    $teacher_id_field = $structure['teacher_id_field'];
    $teacher_name_field = $structure['teacher_name_field'] ?: 'name';
    $designation_field = $structure['designation_field'] ?: 'designation';
    $subject_field = $structure['subject_field'] ?: 'subject';
    $duty_date_field = $structure['duty_date_field'] ?: 'duty_date';
    $room_field = $structure['room_field'] ?: 'room_number';
    $shift_field = $structure['shift_field'] ?: 'duty_shift';
    
    // If we can't find necessary structure, return empty array
    if (empty($teacher_table) || empty($duties_table)) {
        error_log("Required tables not found");
        return [];
    }
    
    // Dynamic SQL with handling for potential different date formats
    $sql = "SELECT d.*, 
               t.$teacher_name_field AS teacher_name,
               " . (!empty($designation_field) ? "t.$designation_field AS designation," : "'অজানা' AS designation,") . "
               " . (!empty($subject_field) ? "t.$subject_field AS subject" : "'অজানা' AS subject") . "
            FROM $duties_table d
            JOIN $teacher_table t ON d.$teacher_id_field = t.$teacher_id_field
            WHERE 
                ($duty_date_field BETWEEN '$start_date' AND '$end_date')
                OR (STR_TO_DATE($duty_date_field, '%Y-%m-%d') BETWEEN '$start_date' AND '$end_date')
                OR (STR_TO_DATE($duty_date_field, '%d-%m-%Y') BETWEEN '$start_date' AND '$end_date')
                OR (STR_TO_DATE($duty_date_field, '%d/%m/%Y') BETWEEN '$start_date' AND '$end_date')
            ORDER BY $duty_date_field";
    
    error_log("Generated SQL: $sql");
    $result = $conn->query($sql);
    
    if ($result === false) {
        error_log("Query error: " . $conn->error);
        return [];
    }
    
    if ($result->num_rows === 0) {
        error_log("No duties found in date range");
        return [];
    }
    
    error_log("Found " . $result->num_rows . " duties in date range");
    
    while ($duty = $result->fetch_assoc()) {
        $teacherId = $duty[$teacher_id_field];
        if (!isset($teacherDutyCounts[$teacherId])) {
            $teacherDutyCounts[$teacherId] = [
                'teacher_id' => $teacherId,
                'teacher_name' => $duty['teacher_name'] ?? 'অজানা',
                'designation' => $duty['designation'] ?? '',
                'subject' => $duty['subject'] ?? '',
                'count' => 0,
                'duties' => []
            ];
        }
        
        $teacherDutyCounts[$teacherId]['count']++;
        $teacherDutyCounts[$teacherId]['duties'][] = [
            'date' => $duty[$duty_date_field],
            'room' => $duty[$room_field] ?? 'অনির্দিষ্ট',
            'shift' => $duty[$shift_field] ?? '',
            'type' => $duty['duty_type'] ?? 'সাধারণ'
        ];
    }
    
    // Sort by duty count (descending)
    usort($teacherDutyCounts, function($a, $b) {
        return $b['count'] - $a['count'];
    });
    
    return $teacherDutyCounts;
}

// Format date in Bengali
function formatDateBengali($date) {
    if (empty($date)) return 'অজানা';
    
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    try {
        $day = date('d', strtotime($date));
        $month = $months[date('m', strtotime($date))];
        $year = date('Y', strtotime($date));
        
        return "$day $month $year";
    } catch (Exception $e) {
        return $date; // Return original if parsing fails
    }
}

// Calculate total honorarium
function calculateTotalHonorarium($teacherDutyCounts, $rate_per_duty) {
    $total = 0;
    foreach ($teacherDutyCounts as $teacher) {
        $total += ($teacher['count'] * $rate_per_duty);
    }
    return $total;
}

// Detect database structure
$db_structure = detectTableStructure($conn);

// Get teacher duty counts if dates are provided
$teacherDutyCounts = [];
if ($show_results) {
    $teacherDutyCounts = getTeacherDutyCounts($conn, $start_date, $end_date, $db_structure);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক সন্মানী হিসাব</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        
        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: #eaf4ff;
            border-bottom: 2px solid #007bff;
            padding: 15px 20px;
        }
        
        .form-card {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .duty-count-badge {
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 20px;
        }
        
        .teacher-row:hover {
            background-color: #f0f7ff;
        }
        
        .duty-details {
            display: none;
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        .summary-box {
            background: #e9f7ef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .card {
                border: none;
                box-shadow: none;
            }
            
            .container {
                max-width: 100%;
                width: 100%;
            }
            
            .duty-details {
                display: block !important;
                break-inside: avoid;
            }
            
            .table {
                width: 100%;
            }
            
            .collapse {
                display: block !important;
            }
        }
        
        .detected-table {
            font-size: 12px;
            margin-bottom: 15px;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card form-card">
            <div class="card-header">
                <h3 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    শিক্ষক সন্মানী হিসাব (অটো-ডিটেকশন)
                </h3>
            </div>
            <div class="card-body">
                <!-- Detected DB structure -->
                <div class="alert alert-info no-print mb-4">
                    <h5><i class="fas fa-database me-2"></i> সনাক্তকৃত ডাটাবেস স্ট্রাকচার:</h5>
                    <table class="detected-table">
                        <tr>
                            <td>শিক্ষক টেবিল:</td>
                            <td><code><?php echo $db_structure['teachers_table'] ?: 'পাওয়া যায়নি'; ?></code></td>
                        </tr>
                        <tr>
                            <td>ডিউটি টেবিল:</td>
                            <td><code><?php echo $db_structure['duties_table'] ?: 'পাওয়া যায়নি'; ?></code></td>
                        </tr>
                        <tr>
                            <td>শিক্ষক আইডি ফিল্ড:</td>
                            <td><code><?php echo $db_structure['teacher_id_field'] ?: 'পাওয়া যায়নি'; ?></code></td>
                        </tr>
                        <tr>
                            <td>শিক্ষকের নাম ফিল্ড:</td>
                            <td><code><?php echo $db_structure['teacher_name_field'] ?: 'পাওয়া যায়নি'; ?></code></td>
                        </tr>
                        <tr>
                            <td>তারিখ ফিল্ড:</td>
                            <td><code><?php echo $db_structure['duty_date_field'] ?: 'পাওয়া যায়নি'; ?></code></td>
                        </tr>
                    </table>
                    <p><a href="db_structure_check.php" class="btn btn-sm btn-outline-primary">ডাটাবেস স্ট্রাকচার দেখুন</a></p>
                </div>
                
                <form action="" method="get" class="row g-3 align-items-end mb-4 no-print">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">শুরুর তারিখ</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">শেষের তারিখ</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
                    </div>
                    <div class="col-md-2">
                        <label for="rate_per_duty" class="form-label">প্রতি ডিউটি হার (টাকা)</label>
                        <input type="number" class="form-control" id="rate_per_duty" name="rate_per_duty" value="<?php echo $rate_per_duty; ?>" min="1" required>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i> হিসাব করুন
                        </button>
                    </div>
                </form>
                
                <?php if ($show_results): ?>
                    <?php if (empty($db_structure['teachers_table']) || empty($db_structure['duties_table'])): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>সমস্যা:</strong> ডাটাবেসে আবশ্যক টেবিল পাওয়া যায়নি। শিক্ষক এবং ডিউটি সম্পর্কিত টেবিল থাকা দরকার।
                            <div class="mt-3">
                                <a href="db_structure_check.php" class="btn btn-outline-danger">
                                    ডাটাবেস স্ট্রাকচার দেখুন
                                </a>
                            </div>
                        </div>
                    <?php elseif (empty($teacherDutyCounts)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            নির্বাচিত তারিখ সীমার মধ্যে কোন ডিউটি অ্যাসাইনমেন্ট পাওয়া যায়নি।
                            <div class="mt-3">
                                <a href="direct_honorarium_debug.php?start_date=<?php echo $start_date; ?>&end_date=<?php echo $end_date; ?>" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-bug me-1"></i> সমস্যা সমাধান করুন
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="summary-box">
                            <div class="row">
                                <div class="col-md-4">
                                    <h5>সময়কাল: <?php echo formatDateBengali($start_date); ?> থেকে <?php echo formatDateBengali($end_date); ?></h5>
                                </div>
                                <div class="col-md-4">
                                    <h5>মোট শিক্ষক: <?php echo count($teacherDutyCounts); ?> জন</h5>
                                </div>
                                <div class="col-md-4">
                                    <h5>মোট সন্মানী: <?php echo calculateTotalHonorarium($teacherDutyCounts, $rate_per_duty); ?> টাকা</h5>
                                </div>
                            </div>
                        </div>
                        
                        <div class="no-print mb-3">
                            <button class="btn btn-success me-2" onclick="window.print()">
                                <i class="fas fa-print me-2"></i> প্রিন্ট করুন
                            </button>
                            <button class="btn btn-info me-2" onclick="toggleAllDetails()">
                                <i class="fas fa-eye me-2"></i> সব বিস্তারিত দেখুন
                            </button>
                            <button class="btn btn-secondary" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i> এক্সেল ফাইলে রপ্তানি করুন
                            </button>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="honorariumTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>শিক্ষকের নাম</th>
                                        <th>পদবি</th>
                                        <th>বিষয়</th>
                                        <th>ডিউটি সংখ্যা</th>
                                        <th>সন্মানী (টাকা)</th>
                                        <th class="no-print">বিস্তারিত</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($teacherDutyCounts as $index => $teacher): ?>
                                    <tr class="teacher-row">
                                        <td><?php echo $index + 1; ?></td>
                                        <td><?php echo htmlspecialchars($teacher['teacher_name']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                        <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                        <td>
                                            <span class="badge bg-primary duty-count-badge">
                                                <?php echo $teacher['count']; ?> টি ডিউটি
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($teacher['count'] * $rate_per_duty); ?> টাকা</strong>
                                        </td>
                                        <td class="no-print">
                                            <button class="btn btn-sm btn-outline-secondary" 
                                                    onclick="toggleDetails(<?php echo $index; ?>)">
                                                <i class="fas fa-chevron-down" id="icon-<?php echo $index; ?>"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="7" class="p-0">
                                            <div class="duty-details" id="details-<?php echo $index; ?>">
                                                <h6>ডিউটি বিবরণ - <?php echo htmlspecialchars($teacher['teacher_name']); ?></h6>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-bordered">
                                                        <thead class="table-light">
                                                            <tr>
                                                                <th>#</th>
                                                                <th>তারিখ</th>
                                                                <th>রুম</th>
                                                                <th>শিফট</th>
                                                                <th>ডিউটি ধরন</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($teacher['duties'] as $dutyIndex => $duty): ?>
                                                            <tr>
                                                                <td><?php echo $dutyIndex + 1; ?></td>
                                                                <td><?php echo formatDateBengali($duty['date']); ?></td>
                                                                <td><?php echo htmlspecialchars($duty['room']); ?></td>
                                                                <td><?php echo htmlspecialchars($duty['shift']); ?></td>
                                                                <td><?php echo htmlspecialchars($duty['type']); ?></td>
                                                            </tr>
                                                            <?php endforeach; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <td colspan="4" class="text-end"><strong>মোট</strong></td>
                                        <td>
                                            <?php 
                                                $totalDuties = array_sum(array_column($teacherDutyCounts, 'count'));
                                                echo "<strong>{$totalDuties} টি ডিউটি</strong>";
                                            ?>
                                        </td>
                                        <td colspan="2">
                                            <strong>
                                                <?php echo number_format($totalDuties * $rate_per_duty); ?> টাকা
                                            </strong>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="text-center mt-5 mb-3 no-print">
            <a href="db_structure_check.php" class="btn btn-info me-2">
                <i class="fas fa-database me-2"></i>
                ডাটাবেস স্ট্রাকচার দেখুন
            </a>
            <a href="direct_honorarium_debug.php" class="btn btn-warning me-2">
                <i class="fas fa-bug me-2"></i>
                ডিবাগিং টুল ব্যবহার করুন
            </a>
            <a href="duty_assignments_management.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                ডিউটি ম্যানেজমেন্টে ফিরে যান
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleDetails(index) {
            const details = document.getElementById('details-' + index);
            const icon = document.getElementById('icon-' + index);
            
            if (details.style.display === 'block') {
                details.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                details.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }
        
        function toggleAllDetails() {
            const details = document.querySelectorAll('.duty-details');
            const isAnyHidden = Array.from(details).some(el => el.style.display !== 'block');
            
            details.forEach((el, i) => {
                const icon = document.getElementById('icon-' + i);
                if (isAnyHidden) {
                    el.style.display = 'block';
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    el.style.display = 'none';
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            });
        }
        
        function exportToExcel() {
            // Create a workbook
            let csvContent = "data:text/csv;charset=utf-8,";
            
            // Add headers
            csvContent += "ক্রমিক,শিক্ষকের নাম,পদবি,বিষয়,ডিউটি সংখ্যা,সন্মানী (টাকা)\n";
            
            // Get table data
            const table = document.getElementById('honorariumTable');
            const rows = table.querySelectorAll('tbody tr.teacher-row');
            
            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    index + 1,
                    cells[1].textContent.trim(),
                    cells[2].textContent.trim(),
                    cells[3].textContent.trim(),
                    cells[4].textContent.trim().replace(' টি ডিউটি', ''),
                    cells[5].textContent.trim().replace(' টাকা', '')
                ];
                csvContent += rowData.join(',') + "\n";
            });
            
            // Create a download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "শিক্ষক_সন্মানী_হিসাব.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>