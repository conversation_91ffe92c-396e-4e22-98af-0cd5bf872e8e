# EXMM Composer Auto Installer (PowerShell)
# Run this script to automatically install Composer and dependencies

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    EXMM Composer Auto Installer" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if PHP is available
if (-not (Test-Command "php")) {
    Write-Host "ERROR: PHP is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install PHP first and add it to your system PATH" -ForegroundColor Yellow
    Write-Host "Download PHP from: https://www.php.net/downloads" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ PHP is available" -ForegroundColor Green

# Get PHP version
$phpVersion = php --version | Select-Object -First 1
Write-Host "PHP Version: $phpVersion" -ForegroundColor Gray
Write-Host ""

# Check if Composer is already installed globally
if (Test-Command "composer") {
    Write-Host "✓ Composer is already installed globally" -ForegroundColor Green
    $composerVersion = composer --version
    Write-Host "Composer Version: $composerVersion" -ForegroundColor Gray
} elseif (Test-Path "composer.phar") {
    Write-Host "✓ Local composer.phar found" -ForegroundColor Green
} else {
    Write-Host "Downloading Composer installer..." -ForegroundColor Yellow
    
    try {
        # Download Composer installer
        Invoke-WebRequest -Uri "https://getcomposer.org/installer" -OutFile "composer-setup.php"
        Write-Host "✓ Composer installer downloaded" -ForegroundColor Green
        
        Write-Host "Installing Composer locally..." -ForegroundColor Yellow
        
        # Install Composer
        $installResult = php composer-setup.php --install-dir=. --filename=composer.phar
        
        if (Test-Path "composer.phar") {
            Write-Host "✓ Composer installed successfully" -ForegroundColor Green
            
            # Clean up installer
            Remove-Item "composer-setup.php" -ErrorAction SilentlyContinue
            Write-Host "✓ Installer cleaned up" -ForegroundColor Green
        } else {
            Write-Host "ERROR: Composer installation failed" -ForegroundColor Red
            Remove-Item "composer-setup.php" -ErrorAction SilentlyContinue
            Read-Host "Press Enter to exit"
            exit 1
        }
    } catch {
        Write-Host "ERROR: Failed to download or install Composer" -ForegroundColor Red
        Write-Host $_.Exception.Message -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "Installing project dependencies..." -ForegroundColor Yellow

# Install dependencies
try {
    if (Test-Command "composer") {
        # Use global composer
        $result = composer install --no-dev --optimize-autoloader 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Dependencies installed successfully using global Composer" -ForegroundColor Green
        } else {
            throw "Global composer failed"
        }
    } elseif (Test-Path "composer.phar") {
        # Use local composer.phar
        $result = php composer.phar install --no-dev --optimize-autoloader 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Dependencies installed successfully using local Composer" -ForegroundColor Green
        } else {
            throw "Local composer failed: $result"
        }
    } else {
        throw "No Composer found"
    }
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Verify installation
if (Test-Path "vendor/autoload.php") {
    Write-Host "✓ Dependencies verified successfully" -ForegroundColor Green
} else {
    Write-Host "WARNING: vendor/autoload.php not found" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    Installation Completed Successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "Excel upload functionality is now available." -ForegroundColor Cyan
Write-Host "You can now upload Excel files (.xlsx, .xls) and CSV files." -ForegroundColor Cyan
Write-Host ""

Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Open your web browser" -ForegroundColor White
Write-Host "2. Go to: http://localhost/exmm/upload.php" -ForegroundColor White
Write-Host "3. Upload your Excel or CSV file" -ForegroundColor White
Write-Host ""

# Ask if user wants to open the upload page
$openBrowser = Read-Host "Would you like to open the upload page now? (y/n)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost/exmm/upload.php"
}

Read-Host "Press Enter to exit"
