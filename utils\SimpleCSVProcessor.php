<?php
require_once __DIR__ . '/../models/Student.php';

class SimpleCSVProcessor {
    private $student;
    private $allowedExtensions = ['csv'];

    public function __construct() {
        $this->student = new Student();
    }

    public function validateFile($file) {
        $errors = [];

        // Check if file was uploaded
        if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
            $errors[] = "File upload error occurred.";
            return $errors;
        }

        // Check file size (max 10MB)
        if ($file['size'] > 10 * 1024 * 1024) {
            $errors[] = "File size too large. Maximum 10MB allowed.";
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, $this->allowedExtensions)) {
            $errors[] = "Invalid file format. Only CSV files are allowed when Composer is not installed.";
        }

        return $errors;
    }

    public function processCSVFile($filePath) {
        try {
            $handle = fopen($filePath, 'r');
            if (!$handle) {
                throw new Exception("Cannot open CSV file");
            }

            $students = [];
            $errors = [];
            $rowNumber = 1;
            $header = null;

            while (($row = fgetcsv($handle)) !== FALSE) {
                if ($rowNumber === 1) {
                    // Skip header row
                    $header = $row;
                    $rowNumber++;
                    continue;
                }

                // Skip empty rows
                if (empty(array_filter($row))) {
                    $rowNumber++;
                    continue;
                }

                $studentData = $this->mapRowToStudent($row, $rowNumber);
                
                if ($studentData['valid']) {
                    $students[] = $studentData['data'];
                } else {
                    $errors = array_merge($errors, $studentData['errors']);
                }
                
                $rowNumber++;
            }

            fclose($handle);

            return [
                'success' => empty($errors),
                'students' => $students,
                'errors' => $errors,
                'total_rows' => $rowNumber - 2, // Exclude header
                'valid_rows' => count($students)
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'students' => [],
                'errors' => ["Error reading CSV file: " . $e->getMessage()],
                'total_rows' => 0,
                'valid_rows' => 0
            ];
        }
    }

    private function mapRowToStudent($row, $rowNumber) {
        $errors = [];
        
        // Map CSV columns to database fields
        $studentData = [
            'c_code' => trim($row[0] ?? '295'),
            'eiin' => trim($row[1] ?? '136257'),
            'roll_no' => trim($row[2] ?? ''),
            'reg_no' => trim($row[3] ?? ''),
            'session' => trim($row[4] ?? '2023'),
            'type' => trim($row[5] ?? 'Regular'),
            'group_name' => trim($row[6] ?? 'Science'),
            'student_name' => trim($row[7] ?? ''),
            'father_name' => trim($row[8] ?? ''),
            'gender' => trim($row[9] ?? 'Male'),
            'sub_1' => trim($row[10] ?? ''),
            'sub_2' => trim($row[11] ?? ''),
            'sub_3' => trim($row[12] ?? ''),
            'sub_4' => trim($row[13] ?? ''),
            'sub_5' => trim($row[14] ?? ''),
            'sub_6' => trim($row[15] ?? ''),
            'sub_7' => trim($row[16] ?? ''),
            'sub_8' => trim($row[17] ?? ''),
            'sub_9' => trim($row[18] ?? ''),
            'sub_10' => trim($row[19] ?? ''),
            'sub_11' => trim($row[20] ?? ''),
            'sub_12' => trim($row[21] ?? ''),
            'sub_13' => trim($row[22] ?? '')
        ];

        // Validate required fields
        if (empty($studentData['student_name'])) {
            $errors[] = "Row {$rowNumber}: Student name is required.";
        }

        if (empty($studentData['roll_no'])) {
            $errors[] = "Row {$rowNumber}: Roll number is required.";
        }

        // Validate gender
        if (!empty($studentData['gender']) && !in_array($studentData['gender'], ['Male', 'Female', 'Other'])) {
            $studentData['gender'] = 'Male'; // Default value
        }

        return [
            'valid' => empty($errors),
            'data' => $studentData,
            'errors' => $errors
        ];
    }

    public function saveStudents($students) {
        try {
            return $this->student->bulkInsert($students);
        } catch (Exception $e) {
            throw new Exception("Error saving students: " . $e->getMessage());
        }
    }

    public function generateSampleCSV() {
        $sampleData = [
            ['C.Code', 'EIIN', 'Roll No', 'Reg No', 'Session', 'Type', 'Group', 'Student Name', 'Father Name', 'Gender', 'Sub 1', 'Sub 2', 'Sub 3', 'Sub 4', 'Sub 5', 'Sub 6', 'Sub 7', 'Sub 8', 'Sub 9', 'Sub 10', 'Sub 11', 'Sub 12', 'Sub 13'],
            ['295', '136257', '100001', 'REG2023001', '2023', 'Regular', 'Science', 'আব্দুল করিম', 'মোঃ রহিম', 'Male', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '112', '113'],
            ['295', '136257', '100002', 'REG2023002', '2023', 'Regular', 'Science', 'ফাতেমা খাতুন', 'মোঃ আলী', 'Female', '101', '102', '103', '104', '105', '106', '107', '108', '109', '110', '111', '', ''],
            ['295', '136257', '100003', 'IMP2023001', '2023', 'Improvement', 'Science', 'মোহাম্মদ রহিম', 'মোঃ করিম', 'Male', '106', '107', '108', '109', '', '', '', '', '', '', '', '', '']
        ];

        $filename = 'sample_students.csv';
        $filepath = __DIR__ . '/../uploads/' . $filename;
        
        // Create uploads directory if not exists
        $uploadDir = dirname($filepath);
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $handle = fopen($filepath, 'w');
        foreach ($sampleData as $row) {
            fputcsv($handle, $row);
        }
        fclose($handle);

        return $filename;
    }
}
?>
