<?php
// AJAX handler for saving teacher order in honorarium report
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Database connection
require_once '../db_connect.php';

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['teacher_order']) || !isset($input['start_date']) || !isset($input['end_date'])) {
        throw new Exception('অবৈধ ডাটা প্রেরণ করা হয়েছে');
    }
    
    $teacherOrder = $input['teacher_order'];
    $startDate = $input['start_date'];
    $endDate = $input['end_date'];
    
    // Validate data
    if (!is_array($teacherOrder) || empty($teacherOrder)) {
        throw new Exception('শিক্ষকদের ক্রম ডাটা পাওয়া যায়নি');
    }
    
    // Create or update teacher order table
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS teacher_honorarium_order (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL,
        display_order INT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_teacher_date_range (teacher_id, start_date, end_date),
        INDEX idx_date_range (start_date, end_date),
        INDEX idx_display_order (display_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $conn->query($createTableSQL);
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Delete existing order for this date range
        $deleteSQL = "DELETE FROM teacher_honorarium_order WHERE start_date = ? AND end_date = ?";
        $deleteStmt = $conn->prepare($deleteSQL);
        $deleteStmt->bind_param('ss', $startDate, $endDate);
        $deleteStmt->execute();
        
        // Insert new order
        $insertSQL = "INSERT INTO teacher_honorarium_order (teacher_id, display_order, start_date, end_date) VALUES (?, ?, ?, ?)";
        $insertStmt = $conn->prepare($insertSQL);
        
        foreach ($teacherOrder as $order) {
            $teacherId = $order['teacher_id'];
            $displayOrder = $order['order'];
            
            $insertStmt->bind_param('iiss', $teacherId, $displayOrder, $startDate, $endDate);
            $insertStmt->execute();
        }
        
        // Commit transaction
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'শিক্ষকদের ক্রম সফলভাবে সংরক্ষিত হয়েছে',
            'saved_count' => count($teacherOrder)
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

$conn->close();
?> 