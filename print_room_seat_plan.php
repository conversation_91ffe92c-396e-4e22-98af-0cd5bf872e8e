<?php
// This is a simplified print-only version
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/Student.php';

// Get parameters from URL or POST
$roomData = $_GET['room_data'] ?? $_POST['room_data'] ?? '';
$seatingPattern = $_GET['pattern'] ?? $_POST['seating_pattern'] ?? 'serial';

if (empty($roomData)) {
    die("No room data provided");
}

// Decode room data
$rooms = json_decode(base64_decode($roomData), true);
if (!$rooms) {
    die("Invalid room data");
}

// Get students from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    foreach ($rooms as &$room) {
        $stmt = $db->prepare("SELECT * FROM students WHERE CAST(roll_no AS UNSIGNED) BETWEEN ? AND ? ORDER BY CAST(roll_no AS UNSIGNED) ASC");
        $stmt->execute([$room['start_roll'], $room['end_roll']]);
        $room['students'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Simple seating arrangement function
function arrangeSeats($students, $config, $pattern) {
    $seats = [];
    $studentIndex = 0;
    
    // Initialize empty seats
    for ($col = 0; $col < $config['columns']; $col++) {
        for ($bench = 0; $bench < $config['benches_per_column']; $bench++) {
            for ($seat = 0; $seat < $config['seats_per_bench']; $seat++) {
                $seats[$col][$bench][$seat] = null;
            }
        }
    }
    
    // Fill seats based on pattern
    switch ($pattern) {
        case 'zigzag':
            for ($col = 0; $col < $config['columns']; $col++) {
                $isEvenColumn = ($col % 2 == 0);
                $benchOrder = $isEvenColumn ? 
                    range(0, $config['benches_per_column'] - 1) : 
                    range($config['benches_per_column'] - 1, 0);
                
                foreach ($benchOrder as $bench) {
                    for ($seat = 0; $seat < $config['seats_per_bench']; $seat++) {
                        if ($studentIndex < count($students)) {
                            $seats[$col][$bench][$seat] = $students[$studentIndex];
                            $studentIndex++;
                        }
                    }
                }
            }
            break;
            
        case 'diagonal':
            for ($diag = 0; $diag < ($config['columns'] + $config['benches_per_column']); $diag++) {
                for ($col = 0; $col < $config['columns']; $col++) {
                    $bench = $diag - $col;
                    if ($bench >= 0 && $bench < $config['benches_per_column']) {
                        for ($seat = 0; $seat < $config['seats_per_bench']; $seat++) {
                            if ($studentIndex < count($students)) {
                                $seats[$col][$bench][$seat] = $students[$studentIndex];
                                $studentIndex++;
                            }
                        }
                    }
                }
            }
            break;
            
        case 'alternate':
            for ($col = 0; $col < $config['columns']; $col++) {
                for ($bench = 0; $bench < $config['benches_per_column']; $bench++) {
                    if ($col % 2 == 1 && $bench % 2 == 1) continue;
                    
                    for ($seat = 0; $seat < $config['seats_per_bench']; $seat++) {
                        if ($studentIndex < count($students)) {
                            $seats[$col][$bench][$seat] = $students[$studentIndex];
                            $studentIndex++;
                        }
                    }
                }
            }
            break;
            
        default: // serial
            for ($col = 0; $col < $config['columns']; $col++) {
                for ($bench = 0; $bench < $config['benches_per_column']; $bench++) {
                    for ($seat = 0; $seat < $config['seats_per_bench']; $seat++) {
                        if ($studentIndex < count($students)) {
                            $seats[$col][$bench][$seat] = $students[$studentIndex];
                            $studentIndex++;
                        }
                    }
                }
            }
            break;
    }
    
    return $seats;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম ওয়াইজ সীট প্লান - প্রিন্ট ভার্সন</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            padding: 15mm;
            margin: 0 auto;
            background: white;
            page-break-after: always;
            position: relative;
        }
        
        .page:last-child {
            page-break-after: avoid;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 15px;
        }
        
        .college-name {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .college-address {
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .exam-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .room-info {
            display: flex;
            justify-content: space-between;
            background: #f0f0f0;
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
        }
        
        .seating-area {
            display: grid;
            gap: 8px;
            justify-content: center;
            margin: 15px 0;
        }
        
        .column {
            display: flex;
            flex-direction: column;
            gap: 6px;
            align-items: center;
        }
        
        .column-label {
            background: #333;
            color: white;
            padding: 3px 8px;
            font-size: 10px;
            font-weight: 600;
            border-radius: 3px;
        }
        
        .bench {
            display: flex;
            gap: 3px;
            border: 1px solid #666;
            padding: 4px;
            background: #fafafa;
            border-radius: 4px;
        }
        
        .seat {
            width: 60px;
            height: 40px;
            border: 1px solid #999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            background: white;
            border-radius: 3px;
        }
        
        .seat.occupied {
            background: #e0e0e0;
            border-color: #666;
        }
        
        .seat.empty {
            background: #f8f8f8;
            border-style: dashed;
            border-color: #ccc;
        }
        
        .roll {
            font-weight: 700;
            font-size: 10px;
        }
        
        .name {
            font-size: 7px;
            line-height: 1;
            text-align: center;
            margin-top: 1px;
        }
        
        .footer {
            position: absolute;
            bottom: 15mm;
            left: 15mm;
            right: 15mm;
            text-align: center;
            font-size: 10px;
            border-top: 1px solid #ccc;
            padding-top: 10px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .page {
                margin: 0;
                box-shadow: none;
                page-break-after: always;
            }
            
            .page:last-child {
                page-break-after: avoid;
            }
        }
        
        @media screen {
            body {
                background: #f0f0f0;
                padding: 20px;
            }
            
            .page {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <?php foreach ($rooms as $roomIndex => $room): ?>
        <?php 
        $seatingPlan = arrangeSeats($room['students'], $room, $seatingPattern);
        ?>
        
        <div class="page">
            <div class="header">
                <div class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
                <div class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</div>
                <div class="exam-title">HSC পরীক্ষা-২০২৫ সীট প্লান</div>
            </div>
            
            <div class="room-info">
                <div><strong>রুম নং:</strong> <?php echo $room['room_number'] ?? ($roomIndex + 1); ?></div>
                <div><strong>রোল:</strong> <?php echo $room['start_roll']; ?> - <?php echo $room['end_roll']; ?></div>
                <div><strong>শিক্ষার্থী:</strong> <?php echo count($room['students']); ?></div>
                <div><strong>প্যাটার্ন:</strong> <?php 
                    $patterns = ['serial' => 'সিরিয়াল', 'zigzag' => 'ঝিকঝাক', 'diagonal' => 'কোনাকোনি', 'alternate' => 'বিকল্প'];
                    echo $patterns[$seatingPattern] ?? 'সিরিয়াল';
                ?></div>
            </div>
            
            <div class="seating-area" style="grid-template-columns: repeat(<?php echo $room['columns']; ?>, 1fr);">
                <?php for ($col = 0; $col < $room['columns']; $col++): ?>
                    <div class="column">
                        <div class="column-label">কলাম <?php echo $col + 1; ?></div>
                        
                        <?php for ($bench = 0; $bench < $room['benches_per_column']; $bench++): ?>
                            <div class="bench">
                                <?php for ($seat = 0; $seat < $room['seats_per_bench']; $seat++): ?>
                                    <?php 
                                    $student = $seatingPlan[$col][$bench][$seat] ?? null;
                                    ?>
                                    <div class="seat <?php echo $student ? 'occupied' : 'empty'; ?>">
                                        <?php if ($student): ?>
                                            <div class="roll"><?php echo htmlspecialchars($student['roll_no']); ?></div>
                                            <div class="name"><?php echo htmlspecialchars(substr($student['student_name'], 0, 12)); ?></div>
                                        <?php else: ?>
                                            <div class="roll">-</div>
                                        <?php endif; ?>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        <?php endfor; ?>
                    </div>
                <?php endfor; ?>
            </div>
            
            <div class="footer">
                <strong>পরিসংখ্যান:</strong>
                মোট সীট: <?php echo $room['columns'] * $room['benches_per_column'] * $room['seats_per_bench']; ?> |
                দখলকৃত: <?php echo count($room['students']); ?> |
                খালি: <?php echo ($room['columns'] * $room['benches_per_column'] * $room['seats_per_bench']) - count($room['students']); ?> |
                তারিখ: <?php echo date('d/m/Y'); ?>
            </div>
        </div>
    <?php endforeach; ?>
    
    <script>
        // Auto print when loaded
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
