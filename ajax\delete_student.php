<?php
require_once '../models/Student.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $studentId = (int)$_POST['id'];
    
    try {
        $student = new Student();
        
        // First check if student exists
        $studentData = $student->getById($studentId);
        if (!$studentData) {
            echo json_encode([
                'success' => false,
                'message' => 'শিক্ষার্থী পাওয়া যায়নি।'
            ]);
            exit;
        }
        
        // Delete the student
        $result = $student->delete($studentId);
        
        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'শিক্ষার্থী সফলভাবে মুছে ফেলা হয়েছে।'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'শিক্ষার্থী মুছে ফেলতে সমস্যা হয়েছে।'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'সার্ভার এরর: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'অবৈধ অনুরোধ।'
    ]);
}
?>
