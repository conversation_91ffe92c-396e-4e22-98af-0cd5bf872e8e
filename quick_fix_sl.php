<?php
require_once 'includes/teacher_db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>Quick SL Fix</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Hind Siliguri', sans-serif; }</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>⚡ Quick SL Number Fix</h1>";

try {
    $pdo->beginTransaction();
    
    // Get all teachers without proper sl_number
    $stmt = $pdo->query("SELECT id, name FROM teachers WHERE sl_number IS NULL OR sl_number = '' OR sl_number = '0' ORDER BY id");
    $teachers = $stmt->fetchAll();
    
    if (!empty($teachers)) {
        echo "<div class='alert alert-info'>";
        echo "<h5>🔧 Fixing SL Numbers...</h5>";
        echo "<p>Found " . count($teachers) . " teachers without proper SL numbers.</p>";
        echo "</div>";
        
        // Get the highest existing sl_number
        $stmt = $pdo->query("SELECT MAX(CAST(sl_number AS UNSIGNED)) as max_sl FROM teachers WHERE sl_number IS NOT NULL AND sl_number != '' AND sl_number != '0'");
        $result = $stmt->fetch();
        $nextSl = ($result['max_sl'] ?? 0) + 1;
        
        echo "<div class='card'>";
        echo "<div class='card-body'>";
        echo "<h6>Updating teachers:</h6>";
        echo "<ul>";
        
        // Update each teacher with a new sl_number
        $updateStmt = $pdo->prepare("UPDATE teachers SET sl_number = ? WHERE id = ?");
        $updatedCount = 0;
        
        foreach ($teachers as $teacher) {
            $updateStmt->execute([$nextSl, $teacher['id']]);
            echo "<li>ID: " . $teacher['id'] . " - " . htmlspecialchars($teacher['name']) . " → SL: $nextSl</li>";
            $nextSl++;
            $updatedCount++;
        }
        
        echo "</ul>";
        echo "</div></div>";
        
        $pdo->commit();
        
        echo "<div class='alert alert-success mt-4'>";
        echo "<h5>✅ Success!</h5>";
        echo "<p>Successfully updated {$updatedCount} teachers with proper SL numbers!</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ All Good!</h5>";
        echo "<p>All teachers already have proper SL numbers!</p>";
        echo "</div>";
    }
    
    // Show current status
    echo "<div class='card mt-4'>";
    echo "<div class='card-header bg-primary text-white'>";
    echo "<h5 class='mb-0'>📊 Current Status</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM teachers");
    $total = $stmt->fetch()['total'];
    echo "<p><strong>Total Teachers:</strong> $total</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as valid FROM teachers WHERE sl_number IS NOT NULL AND sl_number != '' AND sl_number != '0'");
    $valid = $stmt->fetch()['valid'];
    echo "<p><strong>Teachers with valid SL:</strong> $valid</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as invalid FROM teachers WHERE sl_number IS NULL OR sl_number = '' OR sl_number = '0'");
    $invalid = $stmt->fetch()['invalid'];
    echo "<p><strong>Teachers without SL:</strong> $invalid</p>";
    
    if ($invalid == 0) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>All teachers now have proper SL numbers!";
        echo "</div>";
    }
    
    echo "</div></div>";
    
} catch (Exception $e) {
    $pdo->rollback();
    echo "<div class='alert alert-danger'>";
    echo "<h5>❌ Error!</h5>";
    echo "<p>Failed to fix SL numbers: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div class='text-center mt-4'>";
echo "<a href='teacher_duty_management.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users me-2'></i>শিক্ষক ব্যবস্থাপনা";
echo "</a>";
echo "<a href='check_teacher_data.php' class='btn btn-info'>";
echo "<i class='fas fa-search me-2'></i>ডেটা চেক করুন";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
