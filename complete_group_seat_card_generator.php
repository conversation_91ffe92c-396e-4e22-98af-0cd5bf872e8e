<?php
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/Student.php';

// Get parameters
$groupName = $_GET['group'] ?? '';
$studentType = $_GET['type'] ?? '';
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Build query for complete students (all 13 subjects)
    $sql = "SELECT * FROM students WHERE 1=1";
    $params = [];
    
    // Add group filter
    if (!empty($groupName)) {
        $sql .= " AND department = :department";
        $params['department'] = $groupName;
    }

    // Add type filter
    if (!empty($studentType)) {
        $sql .= " AND student_type = :student_type";
        $params['student_type'] = $studentType;
    }

    // Filter complete students (all 13 subjects)
    // Check if subjects field has exactly 13 comma-separated values
    $sql .= " AND subjects IS NOT NULL AND subjects != '' AND
              (LENGTH(subjects) - LENGTH(REPLACE(subjects, ',', '')) + 1) = 13";

    $sql .= " ORDER BY CAST(roll AS UNSIGNED) ASC";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all groups for dropdown
    $groupStmt = $db->query("SELECT DISTINCT department FROM students WHERE department IS NOT NULL AND department != '' ORDER BY department");
    $allGroups = $groupStmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    $students = [];
    $allGroups = [];
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সম্পূর্ণ গ্রুপ সীট কার্ড জেনারেটর - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            min-height: 100vh;
        }
        
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        
        .generator-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header-custom {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white;
            padding: 20px;
            border: none;
        }
        
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .group-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #10b981;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .group-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .group-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .student-count {
            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .btn-generate {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-generate:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-icon {
            font-size: 2rem;
            color: #10b981;
            margin-bottom: 10px;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="generator-card">
                        <div class="card-header-custom">
                            <h2 class="mb-0">
                                <i class="fas fa-check-circle me-3"></i>
                                সম্পূর্ণ গ্রুপ সীট কার্ড জেনারেটর
                            </h2>
                            <p class="mb-0 mt-2">সম্পূর্ণ ১৩ বিষয়ের শিক্ষার্থীদের গ্রুপ অনুযায়ী সীট কার্ড তৈরি করুন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filter Section -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="filter-section">
                        <h5 class="mb-3">
                            <i class="fas fa-filter me-2"></i>ফিল্টার অপশন
                        </h5>
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">গ্রুপ নির্বাচন করুন</label>
                                <select name="group" class="form-select">
                                    <option value="">সব গ্রুপ</option>
                                    <?php foreach ($allGroups as $group): ?>
                                        <option value="<?php echo htmlspecialchars($group); ?>" 
                                                <?php echo ($groupName === $group) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($group); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">শিক্ষার্থীর ধরন</label>
                                <select name="type" class="form-select">
                                    <option value="">সব ধরন</option>
                                    <option value="Regular" <?php echo ($studentType === 'Regular') ? 'selected' : ''; ?>>নিয়মিত</option>
                                    <option value="Irregular" <?php echo ($studentType === 'Irregular') ? 'selected' : ''; ?>>অনিয়মিত</option>
                                    <option value="Improvement" <?php echo ($studentType === 'Improvement') ? 'selected' : ''; ?>>উন্নতি</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">প্রতি পৃষ্ঠায় কার্ড</label>
                                <select name="cards_per_page" class="form-select">
                                    <option value="12" <?php echo ($cardsPerPage === 12) ? 'selected' : ''; ?>>১২টি কার্ড</option>
                                    <option value="9" <?php echo ($cardsPerPage === 9) ? 'selected' : ''; ?>>৯টি কার্ড</option>
                                    <option value="6" <?php echo ($cardsPerPage === 6) ? 'selected' : ''; ?>>৬টি কার্ড</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-search me-2"></i>ফিল্টার প্রয়োগ করুন
                                </button>
                                <a href="?" class="btn btn-outline-secondary ms-2">
                                    <i class="fas fa-undo me-2"></i>রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?php echo count($students); ?></div>
                    <div class="stat-label">সম্পূর্ণ শিক্ষার্থী</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stat-number"><?php echo count($allGroups); ?></div>
                    <div class="stat-label">মোট গ্রুপ</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-id-card"></i>
                    </div>
                    <div class="stat-number"><?php echo ceil(count($students) / $cardsPerPage); ?></div>
                    <div class="stat-label">মোট পৃষ্ঠা</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-double"></i>
                    </div>
                    <div class="stat-number">১৩</div>
                    <div class="stat-label">সম্পূর্ণ বিষয়</div>
                </div>
            </div>

            <!-- Group Cards -->
            <?php if (!empty($students)): ?>
                <?php
                // Group students by department
                $groupedStudents = [];
                foreach ($students as $student) {
                    $group = $student['department'] ?? 'Unknown';
                    if (!isset($groupedStudents[$group])) {
                        $groupedStudents[$group] = [];
                    }
                    $groupedStudents[$group][] = $student;
                }
                ksort($groupedStudents);
                ?>

                <div class="row">
                    <div class="col-12">
                        <h4 class="text-white mb-3">
                            <i class="fas fa-check-circle me-2"></i>সম্পূর্ণ গ্রুপ ভিত্তিক সীট কার্ড জেনারেশন
                        </h4>
                    </div>
                </div>

                <?php foreach ($groupedStudents as $group => $groupStudents): ?>
                    <div class="group-card">
                        <div class="group-header">
                            <div class="group-name">
                                <i class="fas fa-check-circle me-2 text-success"></i><?php echo htmlspecialchars($group); ?> (সম্পূর্ণ ১৩ বিষয়)
                            </div>
                            <div class="student-count"><?php echo count($groupStudents); ?> জন</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <p class="text-muted mb-2">
                                    <strong>রোল রেঞ্জ:</strong> 
                                    <?php 
                                    $rolls = array_column($groupStudents, 'roll');
                                    $rolls = array_map('intval', $rolls);
                                    $rolls = array_filter($rolls, function($r) { return $r > 0; });
                                    if (!empty($rolls)) {
                                        sort($rolls);
                                        echo min($rolls) . ' - ' . max($rolls);
                                    } else {
                                        echo 'কোন রোল নেই';
                                    }
                                    ?>
                                </p>
                                <p class="text-muted mb-2">
                                    <strong>সম্পূর্ণ শিক্ষার্থী:</strong> <?php echo count($groupStudents); ?> জন
                                </p>
                                <p class="text-muted mb-0">
                                    <strong>প্রয়োজনীয় পৃষ্ঠা:</strong> <?php echo ceil(count($groupStudents) / $cardsPerPage); ?>টি
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="working_seat_cards.php?group=<?php echo urlencode($group); ?>&cards_per_page=<?php echo $cardsPerPage; ?>&complete_only=1" 
                                   class="btn btn-generate" target="_blank">
                                    <i class="fas fa-id-card me-2"></i>সম্পূর্ণ সীট কার্ড জেনারেট করুন
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Generate All Button -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <?php 
                        $allParams = $_GET;
                        $allParams['complete_only'] = '1';
                        ?>
                        <a href="working_seat_cards.php?<?php echo http_build_query($allParams); ?>" 
                           class="btn btn-lg" style="background: linear-gradient(135deg, #10b981 0%, #34d399 100%); color: white;" target="_blank">
                            <i class="fas fa-print me-2"></i>সব গ্রুপের সম্পূর্ণ সীট কার্ড একসাথে জেনারেট করুন
                        </a>
                    </div>
                </div>

            <?php else: ?>
                <div class="row">
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-white">কোন সম্পূর্ণ শিক্ষার্থী পাওয়া যায়নি</h5>
                            <p class="text-white-50">নির্বাচিত ফিল্টার অনুযায়ী কোন সম্পূর্ণ ১৩ বিষয়ের শিক্ষার্থী নেই।</p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
