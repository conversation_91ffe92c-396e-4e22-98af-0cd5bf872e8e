<?php
session_start();
require_once 'includes/teacher_db.php';

// Fetch all teachers
$teachers = $teacherManager->getAllTeachers();

// Handle status update
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['teacher_id'], $_POST['duty_status'])) {
    $teacherId = $_POST['teacher_id'];
    $newStatus = $_POST['duty_status'];
    if ($teacherManager->updateDutyStatus($teacherId, $newStatus)) {
        $message = 'স্ট্যাটাস সফলভাবে আপডেট হয়েছে!';
        // Refresh teachers list
        $teachers = $teacherManager->getAllTeachers();
    } else {
        $message = 'স্ট্যাটাস আপডেট করতে সমস্যা হয়েছে!';
    }
}

// Status options
$statusOptions = [
    'সাধারন' => 'সাধারন',
    'সম সময় অন্তর্ভুক্ত' => 'সম সময় অন্তর্ভুক্ত',
    'কখনো অন্তর্ভুক্ত নয়' => 'কখনো অন্তর্ভুক্ত নয়',
];
$statusColors = [
    'সাধারন' => 'success',
    'সম সময় অন্তর্ভুক্ত' => 'primary',
    'কখনো অন্তর্ভুক্ত নয়' => 'danger',
];
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <title>শিক্ষক স্ট্যাটাস পেজ</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { background: #f4f6fa; }
        .teacher-card { border: 1px solid #e3e6ed; border-radius: 12px; padding: 20px; background: #fff; box-shadow: 0 2px 8px #e9ecef44; margin-bottom: 24px; transition: box-shadow 0.2s; }
        .teacher-card:hover { box-shadow: 0 4px 16px #b6c1d944; }
        .teacher-photo { width: 70px; height: 70px; object-fit: cover; border-radius: 50%; border: 2px solid #dee2e6; }
        .teacher-placeholder { width: 70px; height: 70px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 32px; color: #bbb; border: 2px solid #dee2e6; }
        .status-badge { font-size: 14px; padding: 4px 12px; border-radius: 20px; font-weight: 500; }
        .view-switcher .btn { min-width: 120px; }
        .table-avatar { width: 45px; height: 45px; object-fit: cover; border-radius: 50%; border: 1.5px solid #dee2e6; }
        .table-placeholder { width: 45px; height: 45px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #bbb; border: 1.5px solid #dee2e6; }
        @media (max-width: 576px) { .teacher-card { padding: 12px; } }
        @media print {
            .seat-plan-container, .seat-plan-grid, .column-wrapper, .column-header, .bench-row, .bench-seats, .seat-box {
                display: block !important;
            }
        }
    </style>
</head>
<body>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0 text-center flex-grow-1"><i class="fas fa-chalkboard-teacher me-2"></i>শিক্ষক স্ট্যাটাস পেজ</h2>
        <a href="index.php" class="btn btn-outline-secondary ms-3" title="হোম"><i class="fas fa-home"></i> হোম</a>
    </div>
    <div class="d-flex justify-content-center mb-4 view-switcher">
        <button class="btn btn-outline-primary me-2 active" id="cardViewBtn" onclick="switchView('card')"><i class="fas fa-th-large me-1"></i> কার্ড ভিউ</button>
        <button class="btn btn-outline-primary" id="tableViewBtn" onclick="switchView('table')"><i class="fas fa-list me-1"></i> টেবিল ভিউ</button>
    </div>
    <button id="tablePrintBtn" class="btn btn-info btn-sm mb-2">
        <i class="fas fa-table me-1"></i> টেবিল মোডে প্রিন্ট
    </button>
    <?php if ($message): ?>
        <div class="alert alert-info text-center"> <?php echo htmlspecialchars($message); ?> </div>
    <?php endif; ?>
    <div id="cardView" class="row">
        <?php foreach ($teachers as $teacher): ?>
            <div class="col-md-6 col-lg-4">
                <div class="teacher-card d-flex align-items-center">
                    <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                        <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" class="teacher-photo me-3">
                    <?php else: ?>
                        <div class="teacher-placeholder me-3">
                            <i class="fas fa-user"></i>
                        </div>
                    <?php endif; ?>
                    <div style="flex:1;">
                        <div class="d-flex align-items-center mb-1">
                            <strong class="me-2" style="font-size: 18px;"> <?php echo htmlspecialchars($teacher['name']); ?> </strong>
                            <span class="badge status-badge bg-<?php echo $statusColors[$teacher['duty_status']] ?? 'secondary'; ?>"> <?php echo htmlspecialchars($teacher['duty_status']); ?> </span>
                        </div>
                        <div class="mb-2 text-muted" style="font-size: 14px;">SL: <?php echo htmlspecialchars($teacher['sl_number']); ?> | <?php echo htmlspecialchars($teacher['designation']); ?> | <?php echo htmlspecialchars($teacher['subject']); ?></div>
                        <select class="form-select form-select-sm w-auto d-inline-block" onchange="updateStatus(<?php echo $teacher['id']; ?>, this)">
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php if ($teacher['duty_status'] === $value) echo 'selected'; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <div id="tableView" class="table-responsive" style="display:none;">
        <table class="table table-bordered align-middle bg-white">
            <thead class="table-light">
                <tr>
                    <th>ছবি</th>
                    <th>নাম</th>
                    <th>পদবী</th>
                    <th>বিষয়</th>
                    <th>SL</th>
                    <th>স্ট্যাটাস</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($teachers as $teacher): ?>
                <tr>
                    <td>
                        <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                            <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" class="table-avatar">
                        <?php else: ?>
                            <div class="table-placeholder"><i class="fas fa-user"></i></div>
                        <?php endif; ?>
                    </td>
                    <td><strong><?php echo htmlspecialchars($teacher['name']); ?></strong></td>
                    <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                    <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                    <td><?php echo htmlspecialchars($teacher['sl_number']); ?></td>
                    <td>
                        <span class="badge status-badge bg-<?php echo $statusColors[$teacher['duty_status']] ?? 'secondary'; ?> mb-1"> <?php echo htmlspecialchars($teacher['duty_status']); ?> </span><br>
                        <select class="form-select form-select-sm w-auto d-inline-block mt-1" onchange="updateStatus(<?php echo $teacher['id']; ?>, this)">
                            <?php foreach ($statusOptions as $value => $label): ?>
                                <option value="<?php echo $value; ?>" <?php if ($teacher['duty_status'] === $value) echo 'selected'; ?>><?php echo $label; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    <div id="toast-container" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
function showToast(message, type = 'info') {
    const toastId = 'toast_' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0 show`;
    toast.id = toastId;
    toast.role = 'alert';
    toast.innerHTML = `<div class=\"d-flex\"><div class=\"toast-body\">${message}</div><button type=\"button\" class=\"btn-close btn-close-white me-2 m-auto\" data-bs-dismiss=\"toast\" aria-label=\"Close\"></button></div>`;
    document.getElementById('toast-container').appendChild(toast);
    setTimeout(() => { toast.remove(); }, 3000);
}
function updateStatus(teacherId, selectElem) {
    const newStatus = selectElem.value;
    const rowOrCard = selectElem.closest('.teacher-card') || selectElem.closest('tr');
    fetch('ajax/update_teacher_status.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `teacher_id=${teacherId}&duty_status=${encodeURIComponent(newStatus)}`
    })
    .then(res => res.json())
    .then(data => {
        if (data.success) {
            // Update badge text and color
            const badge = rowOrCard.querySelector('.status-badge');
            badge.textContent = newStatus;
            badge.className = 'badge status-badge bg-' + (newStatus === 'সাধারন' ? 'success' : (newStatus === 'সম সময় অন্তর্ভুক্ত' ? 'primary' : 'danger')) + (badge.classList.contains('mb-1') ? ' mb-1' : '');
            showToast(data.message, 'success');
        } else {
            showToast(data.message, 'danger');
        }
    })
    .catch(() => showToast('স্ট্যাটাস আপডেট ব্যর্থ হয়েছে!', 'danger'));
}
function switchView(view) {
    const cardView = document.getElementById('cardView');
    const tableView = document.getElementById('tableView');
    const cardBtn = document.getElementById('cardViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');
    if (view === 'card') {
        cardView.style.display = '';
        tableView.style.display = 'none';
        cardBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else {
        cardView.style.display = 'none';
        tableView.style.display = '';
        cardBtn.classList.remove('active');
        tableBtn.classList.add('active');
    }
}
document.getElementById('tablePrintBtn')?.addEventListener('click', function() {
    document.body.classList.add('print-table-only');
    window.print();
    setTimeout(() => { document.body.classList.remove('print-table-only'); }, 500);
});
</script>
</body>
</html> 