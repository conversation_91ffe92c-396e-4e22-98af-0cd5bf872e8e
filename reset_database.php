<?php
// Database Reset Script - Safely handles foreign key constraints
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

try {
    // Connect to MySQL server (without specifying database)
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🔄 Database Reset Process</h2>";
    
    // Check if database exists
    $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
    $dbExists = $stmt->fetch();
    
    if ($dbExists) {
        echo "📋 Database '$dbname' exists. Proceeding with reset...<br>";
        
        // Connect to the specific database
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        
        // Disable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
        echo "✅ Foreign key checks disabled.<br>";
        
        // Get all tables in the database
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($tables)) {
            echo "<br><h3>🗑️ Dropping existing tables:</h3>";
            echo "<ul>";
            foreach ($tables as $table) {
                $pdo->exec("DROP TABLE IF EXISTS `$table`");
                echo "<li>✅ Dropped table: $table</li>";
            }
            echo "</ul>";
        } else {
            echo "📝 No tables found in database.<br>";
        }
        
        // Re-enable foreign key checks
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        echo "✅ Foreign key checks re-enabled.<br>";
        
    } else {
        echo "📝 Database '$dbname' does not exist. Creating new database...<br>";
        
        // Create database
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "✅ Database '$dbname' created successfully.<br>";
    }
    
    echo "<br><h2>✅ Database reset completed successfully!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li><a href='setup_database.php'>Run Database Setup</a> - Create tables and sample data</li>";
    echo "<li><a href='check_db_structure.php'>Check Database Structure</a> - Verify database state</li>";
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "<h2>❌ Database Reset Error</h2>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Error Code:</strong> " . $e->getCode() . "</p>";
    
    // Provide troubleshooting information
    echo "<br><h3>🔧 Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP MySQL service is running</li>";
    echo "<li>Check if MySQL root user has proper permissions</li>";
    echo "<li>Verify database connection settings</li>";
    echo "<li>Try running this script again</li>";
    echo "</ul>";
    
    echo "<br><p><a href='check_db_structure.php'>Check Current Database State</a></p>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Reset - EXMM</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        h2 {
            color: #333;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        h3 {
            color: #555;
            margin-top: 20px;
        }
        .btn {
            font-family: 'Hind Siliguri', sans-serif;
            font-weight: 500;
        }
        ul li {
            margin-bottom: 5px;
        }
        a {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        a:hover {
            color: #0056b3;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- PHP output will be displayed here -->
    </div>
</body>
</html>
