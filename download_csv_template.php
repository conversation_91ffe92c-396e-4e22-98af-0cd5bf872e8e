<?php
// Set headers for CSV download
$filename = 'EXMM_Student_Template_' . date('Y-m-d') . '.csv';
header('Content-Type: text/csv');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Create CSV content
$output = fopen('php://output', 'w');

// CSV Headers
$headers = [
    'C.Code', 'EIIN', 'Roll No.', 'Reg. No.', 'Session', 'Type', 'Group',
    'Student Name', 'Father Name', 'Gender',
    'Sub 1', 'Sub 2', 'Sub 3', 'Sub 4', 'Sub 5', 'Sub 6', 'Sub 7',
    'Sub 8', 'Sub 9', 'Sub 10', 'Sub 11', 'Sub 12', 'Sub 13'
];

// Write header row
fputcsv($output, $headers);

// Sample data rows
$sampleData = [
    ['295', '123456', '101', 'REG001', '2023-24', 'Regular', 'Science', 
     'Sample Student', 'Sample Father', 'Male',
     '101', '102', '103', '104', '105', '106', '107',
     '108', '109', '110', '111', '112', '113'],
    ['295', '123456', '102', 'REG002', '2023-24', 'Regular', 'Arts',
     'Another Student', 'Another Father', 'Female',
     '101', '102', '103', '104', '105', '', '',
     '', '', '', '', '', '']
];

// Write sample data
foreach ($sampleData as $row) {
    fputcsv($output, $row);
}

fclose($output);
?>
