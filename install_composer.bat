@echo off
echo ========================================
echo    EXMM Composer Auto Installer
echo ========================================
echo.

:: Check if PHP is available
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP first and add it to your system PATH
    echo Download PHP from: https://www.php.net/downloads
    pause
    exit /b 1
)

echo PHP is available. Proceeding with Composer installation...
echo.

:: Check if Composer is already installed globally
composer --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Composer is already installed globally.
    goto install_dependencies
)

:: Check if composer.phar exists locally
if exist composer.phar (
    echo Local composer.phar found.
    goto install_dependencies
)

echo Downloading Composer installer...
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"

if not exist composer-setup.php (
    echo ERROR: Failed to download Composer installer
    pause
    exit /b 1
)

echo Installing Composer locally...
php composer-setup.php --install-dir=. --filename=composer.phar

if not exist composer.phar (
    echo ERROR: Composer installation failed
    del composer-setup.php >nul 2>&1
    pause
    exit /b 1
)

echo Cleaning up installer...
del composer-setup.php >nul 2>&1

:install_dependencies
echo.
echo Installing project dependencies...

:: Try global composer first, then local
composer install --no-dev --optimize-autoloader >nul 2>&1
if %errorlevel% equ 0 (
    echo Dependencies installed successfully using global Composer.
    goto success
)

:: Try local composer.phar
if exist composer.phar (
    php composer.phar install --no-dev --optimize-autoloader
    if %errorlevel% equ 0 (
        echo Dependencies installed successfully using local Composer.
        goto success
    ) else (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
) else (
    echo ERROR: No Composer found
    pause
    exit /b 1
)

:success
echo.
echo ========================================
echo    Installation Completed Successfully!
echo ========================================
echo.
echo Excel upload functionality is now available.
echo You can now upload Excel files (.xlsx, .xls) and CSV files.
echo.
echo Next steps:
echo 1. Open your web browser
echo 2. Go to: http://localhost/exmm/upload.php
echo 3. Upload your Excel or CSV file
echo.
pause
