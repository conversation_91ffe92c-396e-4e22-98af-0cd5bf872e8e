<?php
// Teacher Database Management
$host = 'localhost';
$dbname = 'exmm';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $pdo->exec("SET NAMES utf8mb4");
    $pdo->exec("SET CHARACTER SET utf8mb4");
    $pdo->exec("SET character_set_connection=utf8mb4");
} catch(PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Create teachers table if not exists
$createTableSQL = "
CREATE TABLE IF NOT EXISTS teachers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sl_number VARCHAR(10) NOT NULL,
    name VARCHAR(255) NOT NULL,
    mobile VARCHAR(20) NOT NULL,
    subject VARCHAR(100) NOT NULL,
    designation VARCHAR(100) NOT NULL,
    college VARCHAR(255) NOT NULL,
    duty_status ENUM('সাধারন', 'সম সময় অন্তর্ভুক্ত', 'কখনো অন্তর্ভুক্ত নয়') DEFAULT 'সাধারন',
    photo VARCHAR(500) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_sl (sl_number),
    INDEX idx_name (name),
    INDEX idx_subject (subject),
    INDEX idx_duty_status (duty_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create duty assignments table for permanent storage
$createDutyAssignmentsSQL = "
CREATE TABLE IF NOT EXISTS duty_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    duty_date DATE NOT NULL,
    teacher_id INT NOT NULL,
    room_number VARCHAR(50) NULL,
    duty_shift ENUM('Morning', 'Afternoon') NOT NULL DEFAULT 'Morning',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    INDEX idx_duty_date (duty_date),
    INDEX idx_teacher_id (teacher_id),
    UNIQUE KEY unique_teacher_date (teacher_id, duty_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create signatures table for storing uploaded signatures
$createSignaturesSQL = "
CREATE TABLE IF NOT EXISTS signatures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    signature_type ENUM('principal', 'convener') NOT NULL,
    signature_path VARCHAR(500) NOT NULL,
    signature_name VARCHAR(255) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_type (signature_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create duty_dates table for storing duty date metadata
$createDutyDatesSQL = "
CREATE TABLE IF NOT EXISTS duty_dates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    duty_date DATE NOT NULL,
    exam_type ENUM('theory', 'practical') NOT NULL DEFAULT 'theory',
    exam_shift ENUM('Morning', 'Afternoon', 'Both') NOT NULL DEFAULT 'Morning',
    exam_subject VARCHAR(255) NULL,
    exam_code VARCHAR(50) NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_duty_date (duty_date),
    INDEX idx_duty_date (duty_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Create exams table for storing exam information
$createExamsSQL = "
CREATE TABLE IF NOT EXISTS exams (
    id INT AUTO_INCREMENT PRIMARY KEY,
    exam_date DATE NOT NULL,
    exam_name VARCHAR(255) NOT NULL,
    exam_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_exam_date (exam_date),
    UNIQUE KEY unique_exam_date (exam_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

try {
    // Temporarily disable foreign key checks for safe table creation
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    $pdo->exec($createTableSQL);
    $pdo->exec($createDutyAssignmentsSQL);
    $pdo->exec($createSignaturesSQL);
    $pdo->exec($createDutyDatesSQL);
    $pdo->exec($createExamsSQL);
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
} catch(PDOException $e) {
    error_log("Error creating tables: " . $e->getMessage());
    // Re-enable foreign key checks even if there's an error
    try {
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    } catch(PDOException $e2) {
        error_log("Error re-enabling foreign key checks: " . $e2->getMessage());
    }
}

// Helper functions for teacher operations
class TeacherManager {
    public $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    // Add new teacher
    public function addTeacher($data) {
        $sql = "INSERT INTO teachers (sl_number, name, mobile, subject, designation, college, duty_status, photo) 
                VALUES (:sl_number, :name, :mobile, :subject, :designation, :college, :duty_status, :photo)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            ':sl_number' => $data['sl_number'],
            ':name' => $data['name'],
            ':mobile' => $data['mobile'],
            ':subject' => $data['subject'],
            ':designation' => $data['designation'],
            ':college' => $data['college'],
            ':duty_status' => $data['duty_status'] ?? 'সাধারন',
            ':photo' => $data['photo'] ?? null
        ]);
    }
    
    // Get all teachers
    public function getAllTeachers($orderBy = null) {
        try {
            // Custom sorting to show single digit numbers first, then double digits
            if ($orderBy === null) {
                $sql = "SELECT * FROM teachers ORDER BY 
                        CASE WHEN LENGTH(TRIM(sl_number)) = 1 THEN 0 ELSE 1 END, 
                        CAST(sl_number AS UNSIGNED)";
            } else {
                $sql = "SELECT * FROM teachers ORDER BY $orderBy";
            }
            $stmt = $this->pdo->query($sql);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            error_log("Error getting teachers: " . $e->getMessage());
            return [];
        }
    }

    // Get teacher by ID
    public function getTeacherById($id) {
        try {
            $sql = "SELECT * FROM teachers WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch(PDOException $e) {
            throw new Exception("Error fetching teacher: " . $e->getMessage());
        }
    }
    
    // Update teacher
    public function updateTeacher($id, $data) {
        $sql = "UPDATE teachers SET 
                sl_number = :sl_number,
                name = :name,
                mobile = :mobile,
                subject = :subject,
                designation = :designation,
                college = :college,
                duty_status = :duty_status,
                photo = :photo
                WHERE id = :id";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            ':id' => $id,
            ':sl_number' => $data['sl_number'],
            ':name' => $data['name'],
            ':mobile' => $data['mobile'],
            ':subject' => $data['subject'],
            ':designation' => $data['designation'],
            ':college' => $data['college'],
            ':duty_status' => $data['duty_status'],
            ':photo' => $data['photo']
        ]);
    }
    
    // Delete teacher
    public function deleteTeacher($id) {
        try {
            // Get teacher data first to delete photo file
            $teacher = $this->getTeacherById($id);

            if ($teacher && !empty($teacher['photo']) && file_exists($teacher['photo'])) {
                unlink($teacher['photo']);
            }

            // Temporarily disable foreign key checks for safe deletion
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

            $sql = "DELETE FROM teachers WHERE id = :id";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([':id' => $id]);

            // Re-enable foreign key checks
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

            return $result;
        } catch (PDOException $e) {
            // Re-enable foreign key checks even if there's an error
            try {
                $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            } catch(PDOException $e2) {
                error_log("Error re-enabling foreign key checks: " . $e2->getMessage());
            }
            throw $e;
        }
    }
    
    // Update duty status
    public function updateDutyStatus($id, $status) {
        $sql = "UPDATE teachers SET duty_status = :status WHERE id = :id";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([':id' => $id, ':status' => $status]);
    }
    
    // Search teachers
    public function searchTeachers($searchTerm, $statusFilter = '', $subjectFilter = '') {
        $sql = "SELECT * FROM teachers WHERE 1=1";
        $params = [];
        
        if (!empty($searchTerm)) {
            $sql .= " AND (name LIKE :search OR mobile LIKE :search OR subject LIKE :search OR designation LIKE :search)";
            $params[':search'] = "%$searchTerm%";
        }
        
        if (!empty($statusFilter)) {
            $sql .= " AND duty_status = :status";
            $params[':status'] = $statusFilter;
        }
        
        if (!empty($subjectFilter)) {
            $sql .= " AND subject LIKE :subject";
            $params[':subject'] = "%$subjectFilter%";
        }
        
        // Use the same sorting logic as getAllTeachers for proper numeric ordering
        $sql .= " ORDER BY 
                    CASE WHEN LENGTH(TRIM(sl_number)) = 1 THEN 0 ELSE 1 END, 
                    CAST(sl_number AS UNSIGNED)";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    // Get teachers by duty status
    public function getTeachersByStatus($status) {
        $sql = "SELECT * FROM teachers WHERE duty_status = :status ORDER BY 
                CASE WHEN LENGTH(TRIM(sl_number)) = 1 THEN 0 ELSE 1 END, 
                CAST(sl_number AS UNSIGNED)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([':status' => $status]);
        return $stmt->fetchAll();
    }
    
    // Get unique subjects
    public function getUniqueSubjects() {
        $sql = "SELECT DISTINCT subject FROM teachers ORDER BY subject ASC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    // Get teacher statistics
    public function getTeacherStats() {
        $sql = "SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN duty_status = 'সাধারন' THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN duty_status = 'সম সময় অন্তর্ভুক্ত' THEN 1 ELSE 0 END) as always,
                SUM(CASE WHEN duty_status = 'কখনো অন্তর্ভুক্ত নয়' THEN 1 ELSE 0 END) as never
                FROM teachers";
        
        $stmt = $this->pdo->query($sql);
        return $stmt->fetch();
    }
    
    // Get all duty assignments for a specific teacher
    public function getTeacherDutyAssignments($teacherId) {
        try {
            $sql = "SELECT da.id, da.duty_date, da.room_number, t.name, t.subject 
                    FROM duty_assignments da 
                    JOIN teachers t ON da.teacher_id = t.id 
                    WHERE da.teacher_id = ? 
                    ORDER BY da.duty_date ASC";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$teacherId]);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            throw new Exception("Error fetching teacher duties: " . $e->getMessage());
        }
    }

    // Check if teacher already has duty on a specific date and shift
    public function checkTeacherDutyExists($teacherId, $dutyDate, $dutyShift = 'Morning') {
        try {
            $sql = "SELECT COUNT(*) FROM duty_assignments WHERE teacher_id = ? AND duty_date = ? AND duty_shift = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$teacherId, $dutyDate, $dutyShift]);
            return $stmt->fetchColumn() > 0;
        } catch(PDOException $e) {
            throw new Exception("Error checking teacher duty: " . $e->getMessage());
        }
    }

    // Assign teacher to duty
    public function assignTeacherToDuty($teacherId, $dutyDate, $roomNumber = null, $dutyShift = 'Morning') {
        try {
            $sql = "INSERT INTO duty_assignments (teacher_id, duty_date, room_number, duty_shift) VALUES (?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$teacherId, $dutyDate, $roomNumber, $dutyShift]);
        } catch(PDOException $e) {
            throw new Exception("Error assigning teacher to duty: " . $e->getMessage());
        }
    }

    // Update teacher duty
    public function updateTeacherDuty($dutyId, $teacherId, $dutyDate, $roomNumber = null) {
        try {
            $sql = "UPDATE duty_assignments SET teacher_id = ?, duty_date = ?, room_number = ? WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$teacherId, $dutyDate, $roomNumber, $dutyId]);
        } catch(PDOException $e) {
            throw new Exception("Error updating teacher duty: " . $e->getMessage());
        }
    }

    // Delete teacher duty
    public function deleteTeacherDuty($dutyId) {
        try {
            $sql = "DELETE FROM duty_assignments WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$dutyId]);
        } catch(PDOException $e) {
            throw new Exception("Error deleting teacher duty: " . $e->getMessage());
        }
    }
    
    // Bulk insert teachers from CSV
    public function bulkInsertTeachers($teachersData) {
        $this->pdo->beginTransaction();

        try {
            $sql = "INSERT INTO teachers (sl_number, name, mobile, subject, designation, college, duty_status)
                    VALUES (:sl_number, :name, :mobile, :subject, :designation, :college, :duty_status)
                    ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    mobile = VALUES(mobile),
                    subject = VALUES(subject),
                    designation = VALUES(designation),
                    college = VALUES(college)";

            $stmt = $this->pdo->prepare($sql);

            foreach ($teachersData as $teacher) {
                $stmt->execute([
                    ':sl_number' => $teacher['sl'],
                    ':name' => $teacher['name'],
                    ':mobile' => $teacher['mobile'],
                    ':subject' => $teacher['subject'],
                    ':designation' => $teacher['designation'],
                    ':college' => $teacher['college'],
                    ':duty_status' => 'সাধারন'
                ]);
            }

            $this->pdo->commit();
            return true;
        } catch (Exception $e) {
            $this->pdo->rollback();
            throw $e;
        }
    }

    // Bulk delete teachers by IDs
    public function bulkDeleteTeachers($ids) {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        try {
            // Sanitize IDs to ensure they are integers
            $sanitizedIds = array_map('intval', $ids);
            $sanitizedIds = array_filter($sanitizedIds, function($id) {
                return $id > 0;
            });

            if (empty($sanitizedIds)) {
                return false;
            }

            // Get teacher data first to delete photo files
            $placeholders = str_repeat('?,', count($sanitizedIds) - 1) . '?';
            $selectSql = "SELECT id, photo FROM teachers WHERE id IN ($placeholders)";
            $selectStmt = $this->pdo->prepare($selectSql);
            $selectStmt->execute($sanitizedIds);
            $teachers = $selectStmt->fetchAll();

            // Delete photo files
            foreach ($teachers as $teacher) {
                if (!empty($teacher['photo']) && file_exists($teacher['photo'])) {
                    unlink($teacher['photo']);
                }
            }

            // Temporarily disable foreign key checks for safe deletion
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

            // Delete teachers from database
            $deleteSql = "DELETE FROM teachers WHERE id IN ($placeholders)";
            $deleteStmt = $this->pdo->prepare($deleteSql);
            $result = $deleteStmt->execute($sanitizedIds);

            // Re-enable foreign key checks
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

            return $result;
        } catch (PDOException $e) {
            // Re-enable foreign key checks even if there's an error
            try {
                $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            } catch(PDOException $e2) {
                error_log("Error re-enabling foreign key checks: " . $e2->getMessage());
            }
            error_log("Bulk delete error: " . $e->getMessage());
            throw $e;
        }
    }

    // Delete all teachers
    public function deleteAllTeachers() {
        try {
            // Get all teachers to delete their photos
            $teachers = $this->getAllTeachers();

            // Delete all photo files
            foreach ($teachers as $teacher) {
                if (!empty($teacher['photo']) && file_exists($teacher['photo'])) {
                    unlink($teacher['photo']);
                }
            }

            // Temporarily disable foreign key checks for safe deletion
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

            // Delete all teachers from database
            $sql = "DELETE FROM teachers";
            $result = $this->pdo->exec($sql);

            // Re-enable foreign key checks
            $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

            return $result !== false;
        } catch (PDOException $e) {
            // Re-enable foreign key checks even if there's an error
            try {
                $this->pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            } catch(PDOException $e2) {
                error_log("Error re-enabling foreign key checks: " . $e2->getMessage());
            }
            error_log("Delete all teachers error: " . $e->getMessage());
            throw $e;
        }
    }

    // === DUTY ASSIGNMENT METHODS ===

    // Assign duty to teachers for a specific date
    public function assignDuty($dutyDate, $teacherIds, $roomAssignments = [], $dutyShift = 'Morning') {
        try {
            $this->pdo->beginTransaction();

            // Validate teacher IDs first
            $validTeacherIds = [];
            foreach ($teacherIds as $teacherId) {
                // Skip empty or invalid IDs
                if (empty($teacherId) || !is_numeric($teacherId)) {
                    continue;
                }

                // Check if teacher exists
                $checkStmt = $this->pdo->prepare("SELECT id FROM teachers WHERE id = ?");
                $checkStmt->execute([$teacherId]);
                if ($checkStmt->fetch()) {
                    $validTeacherIds[] = $teacherId;
                }
            }

            if (empty($validTeacherIds)) {
                throw new Exception("কোন বৈধ শিক্ষক ID পাওয়া যায়নি!");
            }

            // First, remove existing assignments for this date and shift to avoid duplicates
            // This allows to have separate assignments for morning and afternoon
            $deleteSql = "DELETE FROM duty_assignments WHERE duty_date = :duty_date AND duty_shift = :duty_shift";
            $deleteStmt = $this->pdo->prepare($deleteSql);
            $deleteStmt->execute([
                ':duty_date' => $dutyDate,
                ':duty_shift' => $dutyShift
            ]);

            // Insert new assignments
            $insertSql = "INSERT INTO duty_assignments (duty_date, teacher_id, room_number, duty_shift) VALUES (:duty_date, :teacher_id, :room_number, :duty_shift)";
            $insertStmt = $this->pdo->prepare($insertSql);

            foreach ($validTeacherIds as $teacherId) {
                $roomNumber = $roomAssignments[$teacherId] ?? null;
                $insertStmt->execute([
                    ':duty_date' => $dutyDate,
                    ':teacher_id' => $teacherId,
                    ':room_number' => $roomNumber,
                    ':duty_shift' => $dutyShift
                ]);
            }

            $this->pdo->commit();
            return true;
        } catch (Exception $e) {
            $this->pdo->rollback();
            throw $e;
        }
    }

    // Get duty assignments for a specific date with optional search
    public function getDutyAssignments($dutyDate, $searchQuery = '', $dutyShift = null) {
        $sql = "SELECT da.*, t.name AS teacher_name, t.designation, t.subject, t.college, t.mobile, t.sl_number, t.photo, t.id as teacher_id
                FROM duty_assignments da
                JOIN teachers t ON da.teacher_id = t.id
                WHERE da.duty_date = :duty_date";
        
        $params = [':duty_date' => $dutyDate];

        // Add shift filter if provided
        if (!empty($dutyShift) && $dutyShift !== 'all') {
            $sql .= " AND da.duty_shift = :duty_shift";
            $params[':duty_shift'] = $dutyShift;
        }

        if (!empty($searchQuery)) {
            $sql .= " AND (t.name LIKE :searchQuery OR t.id LIKE :searchQuery OR t.subject LIKE :searchQuery OR t.designation LIKE :searchQuery OR t.mobile LIKE :searchQuery)";
            $params[':searchQuery'] = "%" . $searchQuery . "%";
        }

        $sql .= " ORDER BY CAST(t.sl_number AS UNSIGNED) ASC";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    // Get all duty dates
    public function getAllDutyDates() {
        try {
            // First try to get dates from duty_dates table
            $sql = "SELECT duty_date FROM duty_dates ORDER BY duty_date ASC";
            $stmt = $this->pdo->query($sql);
            $dates = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // If no dates found in duty_dates table, fall back to duty_assignments table
            if (empty($dates)) {
                $sql = "SELECT DISTINCT duty_date FROM duty_assignments ORDER BY duty_date ASC";
                $stmt = $this->pdo->query($sql);
                $dates = $stmt->fetchAll(PDO::FETCH_COLUMN);
            }
            
            return $dates;
        } catch(PDOException $e) {
            error_log("Error getting duty dates: " . $e->getMessage());
            return [];
        }
    }

    // Get duty date details
    public function getDutyDateDetails($date) {
        try {
            $sql = "SELECT * FROM duty_dates WHERE duty_date = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$date]);
            return $stmt->fetch();
        } catch(PDOException $e) {
            error_log("Error getting duty date details: " . $e->getMessage());
            return null;
        }
    }
    
    // Check if duty date exists
    public function dutyDateExists($date) {
        try {
            $sql = "SELECT COUNT(*) FROM duty_dates WHERE duty_date = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$date]);
            return $stmt->fetchColumn() > 0;
        } catch(PDOException $e) {
            error_log("Error checking duty date: " . $e->getMessage());
            return false;
        }
    }

    // Get all duty dates for a specific teacher
    public function getAllDutyDatesForTeacher($teacherId) {
        try {
            $sql = "SELECT duty_date, room_number, duty_shift FROM duty_assignments WHERE teacher_id = :teacher_id ORDER BY duty_date ASC";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([':teacher_id' => $teacherId]);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            throw new Exception("Error fetching teacher duties: " . $e->getMessage());
        }
    }

    // Remove duty assignment for a specific date
    public function removeDutyAssignment($dutyDate) {
        $sql = "DELETE FROM duty_assignments WHERE duty_date = :duty_date";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([':duty_date' => $dutyDate]);
    }

    // === SIGNATURE MANAGEMENT METHODS ===

    // Upload signature
    public function uploadSignature($signatureType, $filePath, $fileName) {
        try {
            // First, deactivate existing signatures of this type
            $deactivateSql = "UPDATE signatures SET is_active = FALSE WHERE signature_type = :type";
            $deactivateStmt = $this->pdo->prepare($deactivateSql);
            $deactivateStmt->execute([':type' => $signatureType]);

            // Insert new signature
            $insertSql = "INSERT INTO signatures (signature_type, signature_path, signature_name, is_active)
                         VALUES (:type, :path, :name, TRUE)";
            $insertStmt = $this->pdo->prepare($insertSql);
            return $insertStmt->execute([
                ':type' => $signatureType,
                ':path' => $filePath,
                ':name' => $fileName
            ]);
        } catch (Exception $e) {
            throw $e;
        }
    }

    // Get active signature
    public function getActiveSignature($signatureType) {
        $sql = "SELECT * FROM signatures WHERE signature_type = :type AND is_active = TRUE ORDER BY uploaded_at DESC LIMIT 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([':type' => $signatureType]);
        return $stmt->fetch();
    }

    // Get all signatures
    public function getAllSignatures() {
        $sql = "SELECT * FROM signatures ORDER BY signature_type, uploaded_at DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll();
    }

    // New method to get all assignments with details efficiently
    public function getAllDutyAssignmentsWithDetails() {
        try {
            $sql = "SELECT 
                        da.id,
                        da.duty_date,
                        da.room_number,
                        da.teacher_id,
                        t.name AS teacher_name,
                        t.designation,
                        t.college
                    FROM 
                        duty_assignments da
                    JOIN 
                        teachers t ON da.teacher_id = t.id
                    ORDER BY 
                        da.duty_date DESC, t.name ASC";
            
            $stmt = $this->pdo->query($sql);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception("Error fetching duty assignments with details: " . $e->getMessage());
        }
    }
    
    // Delete all duty assignments
    public function deleteAllDutyAssignments() {
        try {
            $sql = "DELETE FROM duty_assignments";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute();
        } catch(PDOException $e) {
            throw new Exception("Error deleting duty assignments: " . $e->getMessage());
        }
    }
    
    // Update teacher's room for a specific date
    public function updateTeacherRoom($teacherId, $date, $roomNumber) {
        try {
            $sql = "UPDATE duty_assignments SET room_number = :room_number 
                    WHERE teacher_id = :teacher_id AND duty_date = :duty_date";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                ':room_number' => $roomNumber,
                ':teacher_id' => $teacherId,
                ':duty_date' => $date
            ]);
        } catch(PDOException $e) {
            throw new Exception("Error updating teacher room: " . $e->getMessage());
        }
    }
    
    // Remove teacher from duty for a specific date
    public function removeTeacherFromDuty($teacherId, $date) {
        try {
            $sql = "DELETE FROM duty_assignments 
                    WHERE teacher_id = :teacher_id AND duty_date = :duty_date";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                ':teacher_id' => $teacherId,
                ':duty_date' => $date
            ]);
        } catch(PDOException $e) {
            throw new Exception("Error removing teacher from duty: " . $e->getMessage());
        }
    }
    
    // Save duty date metadata
    public function saveDutyDateMetadata($date, $examType, $examShift, $examSubject, $examCode, $notes) {
        try {
            // Check if date already exists
            if ($this->dutyDateExists($date)) {
                // Update existing record
                $sql = "UPDATE duty_dates SET 
                        exam_type = :exam_type,
                        exam_shift = :exam_shift,
                        exam_subject = :exam_subject,
                        exam_code = :exam_code,
                        notes = :notes
                        WHERE duty_date = :duty_date";
            } else {
                // Insert new record
                $sql = "INSERT INTO duty_dates 
                        (duty_date, exam_type, exam_shift, exam_subject, exam_code, notes)
                        VALUES 
                        (:duty_date, :exam_type, :exam_shift, :exam_subject, :exam_code, :notes)";
            }
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([
                ':duty_date' => $date,
                ':exam_type' => $examType,
                ':exam_shift' => $examShift,
                ':exam_subject' => $examSubject,
                ':exam_code' => $examCode,
                ':notes' => $notes
            ]);
        } catch(PDOException $e) {
            throw new Exception("Error saving duty date metadata: " . $e->getMessage());
        }
    }

    // Get all duty assignments within a date range
    public function getAllDutyAssignmentsInDateRange($start_date, $end_date) {
        try {
            // Make sure we have the correct date format for MySQL
            $formatted_start = date('Y-m-d', strtotime($start_date));
            $formatted_end = date('Y-m-d', strtotime($end_date));
            
            error_log("Querying duties from $formatted_start to $formatted_end");
            
            // First try - Direct query using PDO with standard date format
            $stmt = $this->pdo->prepare("
                SELECT a.*, t.teacher_name, t.designation, t.subject, t.phone, t.photo
                FROM duty_assignments a
                JOIN teachers t ON a.teacher_id = t.id
                WHERE a.duty_date BETWEEN :start_date AND :end_date
                ORDER BY a.duty_date
            ");
            
            $stmt->bindParam(':start_date', $formatted_start);
            $stmt->bindParam(':end_date', $formatted_end);
            $stmt->execute();
            
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $found = count($results);
            error_log("Found $found duty assignments with standard date format");
            
            // If no results, try with different date format or direct SQL
            if ($found === 0) {
                // Check if table has any data
                $check_stmt = $this->pdo->query("SELECT COUNT(*) FROM duty_assignments");
                $total = $check_stmt->fetchColumn();
                error_log("Total records in duty_assignments: $total");
                
                if ($total > 0) {
                    // Table has data but date format might be different
                    // Try direct MySQL query with STR_TO_DATE conversion
                    $sql = "
                        SELECT a.*, t.teacher_name, t.designation, t.subject, t.phone, t.photo
                        FROM duty_assignments a
                        JOIN teachers t ON a.teacher_id = t.id
                        WHERE 
                            (STR_TO_DATE(a.duty_date, '%Y-%m-%d') BETWEEN :start_date AND :end_date)
                            OR (STR_TO_DATE(a.duty_date, '%d-%m-%Y') BETWEEN :start_date2 AND :end_date2)
                            OR (STR_TO_DATE(a.duty_date, '%d/%m/%Y') BETWEEN :start_date3 AND :end_date3)
                        ORDER BY a.duty_date
                    ";
                    
                    $stmt = $this->pdo->prepare($sql);
                    $stmt->bindParam(':start_date', $formatted_start);
                    $stmt->bindParam(':end_date', $formatted_end);
                    $stmt->bindParam(':start_date2', $formatted_start);
                    $stmt->bindParam(':end_date2', $formatted_end);
                    $stmt->bindParam(':start_date3', $formatted_start);
                    $stmt->bindParam(':end_date3', $formatted_end);
                    $stmt->execute();
                    
                    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    error_log("After format conversion, found " . count($results) . " duty assignments");
                    
                    if (count($results) === 0) {
                        // Last attempt: get all data and filter in PHP
                        $all_stmt = $this->pdo->query("
                            SELECT a.*, t.teacher_name, t.designation, t.subject, t.phone, t.photo
                            FROM duty_assignments a
                            JOIN teachers t ON a.teacher_id = t.id
                        ");
                        
                        $all_results = $all_stmt->fetchAll(PDO::FETCH_ASSOC);
                        error_log("Retrieved all " . count($all_results) . " duty assignments for PHP filtering");
                        
                        // Sample a date to understand format
                        if (!empty($all_results)) {
                            $sample_date = $all_results[0]['duty_date'];
                            error_log("Sample date format: $sample_date");
                            
                            // Try to detect format
                            $detected_format = null;
                            $formats = ['Y-m-d', 'd-m-Y', 'm/d/Y', 'd/m/Y', 'Y/m/d'];
                            foreach ($formats as $format) {
                                $d = \DateTime::createFromFormat($format, $sample_date);
                                if ($d && $d->format($format) === $sample_date) {
                                    $detected_format = $format;
                                    break;
                                }
                            }
                            
                            if ($detected_format) {
                                error_log("Detected date format: $detected_format");
                                
                                // Convert start/end to DateTime for comparison
                                $start_dt = new \DateTime($formatted_start);
                                $end_dt = new \DateTime($formatted_end);
                                
                                // Filter results in PHP
                                $results = array_filter($all_results, function($duty) use ($start_dt, $end_dt, $detected_format) {
                                    $duty_dt = \DateTime::createFromFormat($detected_format, $duty['duty_date']);
                                    if (!$duty_dt) return false;
                                    
                                    return $duty_dt >= $start_dt && $duty_dt <= $end_dt;
                                });
                                
                                error_log("After PHP filtering, found " . count($results) . " duty assignments");
                            }
                        }
                    }
                }
            }
            
            return array_values($results); // Reset array keys
        } catch (PDOException $e) {
            error_log("Database error in getAllDutyAssignmentsInDateRange: " . $e->getMessage());
            return [];
        } catch (\Exception $e) {
            error_log("General error in getAllDutyAssignmentsInDateRange: " . $e->getMessage());
            return [];
        }
    }

    // Update unique constraint in duty_assignments table for shift support
    public function updateDutyAssignmentsTableStructure() {
        try {
            // Check if we have the right unique key already
            $checkSql = "SHOW CREATE TABLE duty_assignments";
            $stmt = $this->pdo->query($checkSql);
            $tableInfo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // If the table creation SQL doesn't contain the unique key with shift, update it
            if (!strpos($tableInfo['Create Table'], 'unique_teacher_date_shift')) {
                // Drop old unique constraint
                $dropSql = "ALTER TABLE duty_assignments DROP INDEX unique_teacher_date";
                $this->pdo->exec($dropSql);
                
                // Add new unique constraint including shift
                $addSql = "ALTER TABLE duty_assignments ADD CONSTRAINT unique_teacher_date_shift UNIQUE (teacher_id, duty_date, duty_shift)";
                $this->pdo->exec($addSql);
                return true;
            }
            return false;
        } catch(PDOException $e) {
            // If error occurs, it might be that the constraint doesn't exist yet or already has the right format
            error_log("Info: " . $e->getMessage());
            return false;
        }
    }
}

// Initialize TeacherManager
$teacherManager = new TeacherManager($pdo);
?>
