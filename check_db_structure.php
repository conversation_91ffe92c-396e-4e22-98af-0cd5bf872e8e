<?php
// Database Structure Check Script
$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

try {
    // Connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "✅ Database '$dbname' connected successfully.<br><br>";
    
    // Show all tables
    $stmt = $pdo->query('SHOW TABLES');
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>📋 Tables in database:</h3>";
    echo "<ul>";
    foreach($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul><br>";
    
    // Check for foreign keys
    echo "<h3>🔗 Foreign Key Constraints:</h3>";
    $foundForeignKeys = false;
    
    foreach($tables as $table) {
        $stmt = $pdo->query("SELECT 
            CONSTRAINT_NAME, 
            TABLE_NAME, 
            COLUMN_NAME, 
            REFERENCED_TABLE_NAME, 
            REFERENCED_COLUMN_NAME 
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = '$dbname' 
        AND TABLE_NAME = '$table' 
        AND REFERENCED_TABLE_NAME IS NOT NULL");
        
        $fks = $stmt->fetchAll();
        if($fks) {
            $foundForeignKeys = true;
            echo "<h4>Table: $table</h4>";
            echo "<ul>";
            foreach($fks as $fk) {
                echo "<li>FK: {$fk['COLUMN_NAME']} -> {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</li>";
            }
            echo "</ul>";
        }
    }
    
    if (!$foundForeignKeys) {
        echo "<p>No foreign key constraints found.</p>";
    }
    
    // Check for any existing duty assignment tables
    echo "<br><h3>🔍 Checking for duty-related tables:</h3>";
    $dutyTables = [];
    foreach($tables as $table) {
        if (strpos(strtolower($table), 'duty') !== false || 
            strpos(strtolower($table), 'assignment') !== false ||
            strpos(strtolower($table), 'room') !== false) {
            $dutyTables[] = $table;
        }
    }
    
    if (empty($dutyTables)) {
        echo "<p>No duty-related tables found.</p>";
    } else {
        echo "<ul>";
        foreach($dutyTables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
} catch(PDOException $e) {
    echo "❌ Database Error: " . $e->getMessage() . "<br>";
    echo "Please check your database connection settings.";
}
?>
