<?php
// Teacher Honorarium Calculator (Final Fixed Version)
// Robust version with database structure auto-detection
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
require_once 'db_connect.php';

// Database structure auto-detection
function detectDatabaseStructure($conn) {
    $db_structure = [];
    
    // Check if we have a stored configuration
    if (file_exists('config/honorarium_structure.json')) {
        $config = json_decode(file_get_contents('config/honorarium_structure.json'), true);
        if (is_array($config) && !empty($config)) {
            // Validate that the tables and fields exist
            $tables_result = $conn->query("SHOW TABLES");
            $tables = [];
            while ($tables_result && $row = $tables_result->fetch_array()) {
                $tables[] = $row[0];
            }
            
            // If the configured tables exist, use the configuration
            if (in_array($config['teachers_table'], $tables) && 
                in_array($config['duties_table'], $tables)) {
                return $config;
            }
        }
    }
    
    // Find teacher table
    $result = $conn->query("SHOW TABLES");
    $teachers_table = '';
    $duties_table = '';
    
    if ($result) {
        while ($row = $result->fetch_array()) {
            $table_name = $row[0];
            if (empty($teachers_table) && stripos($table_name, 'teacher') !== false) {
                $teachers_table = $table_name;
            } elseif (empty($duties_table) && (
                stripos($table_name, 'duty') !== false || 
                stripos($table_name, 'assign') !== false
            )) {
                $duties_table = $table_name;
            }
        }
    }
    
    if (empty($teachers_table) || empty($duties_table)) {
        return false; // Couldn't detect required tables
    }
    
    $db_structure['teachers_table'] = $teachers_table;
    $db_structure['duties_table'] = $duties_table;
    
    // Find teacher ID field (primary key)
    $result = $conn->query("SHOW KEYS FROM `$teachers_table` WHERE Key_name = 'PRIMARY'");
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $db_structure['teacher_id_field'] = $row['Column_name'];
    } else {
        // Try to find ID field based on name
        $result = $conn->query("DESCRIBE `$teachers_table`");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                if (stripos($row['Field'], 'id') !== false && $row['Key'] == 'PRI') {
                    $db_structure['teacher_id_field'] = $row['Field'];
                    break;
                }
            }
        }
        
        // If still not found, use first field with "id" in name
        if (empty($db_structure['teacher_id_field'])) {
            $result = $conn->query("DESCRIBE `$teachers_table`");
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    if (stripos($row['Field'], 'id') !== false) {
                        $db_structure['teacher_id_field'] = $row['Field'];
                        break;
                    }
                }
            }
        }
    }
    
    // Find teacher name field
    $result = $conn->query("DESCRIBE `$teachers_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'name') !== false) {
                $db_structure['teacher_name_field'] = $row['Field'];
                break;
            }
        }
    }
    
    // Find duty date field
    $result = $conn->query("DESCRIBE `$duties_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'date') !== false) {
                $db_structure['duty_date_field'] = $row['Field'];
                break;
            }
        }
    }
    
    // Find duty foreign key (referencing teacher ID)
    $result = $conn->query("DESCRIBE `$duties_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (
                stripos($row['Field'], 'teacher') !== false && 
                stripos($row['Field'], 'id') !== false
            ) {
                $db_structure['duty_fk_field'] = $row['Field'];
                break;
            }
        }
    }
    
    // If duty FK not found, look for any ID field
    if (empty($db_structure['duty_fk_field'])) {
        $result = $conn->query("DESCRIBE `$duties_table`");
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                if (stripos($row['Field'], 'id') !== false && $row['Field'] != 'id') {
                    $db_structure['duty_fk_field'] = $row['Field'];
                    break;
                }
            }
        }
    }
    
    // Find room field
    $result = $conn->query("DESCRIBE `$duties_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'room') !== false) {
                $db_structure['room_field'] = $row['Field'];
                break;
            }
        }
    }
    
    // Find shift field
    $result = $conn->query("DESCRIBE `$duties_table`");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            if (stripos($row['Field'], 'shift') !== false) {
                $db_structure['shift_field'] = $row['Field'];
                break;
            }
        }
    }
    
    // Set defaults if not found
    if (empty($db_structure['room_field'])) $db_structure['room_field'] = 'room_number';
    if (empty($db_structure['shift_field'])) $db_structure['shift_field'] = 'duty_shift';
    
    return $db_structure;
}

// Detect the database structure
$db_structure = detectDatabaseStructure($conn);

// Handle form submission
$start_date = $_POST['start_date'] ?? '';
$end_date = $_POST['end_date'] ?? '';
$honorarium_rate = $_POST['honorarium_rate'] ?? 500;
$sort_by = $_POST['sort_by'] ?? 'name'; // Default sort by name
$submitted = !empty($_POST['submitted']);

// Function to get teacher duties with robust date handling
function getTeacherDutyCounts($conn, $start_date, $end_date, $db_structure) {
    if (empty($db_structure) || empty($start_date) || empty($end_date)) {
        return [
            'success' => false,
            'error' => 'ডাটাবেস স্ট্রাকচার ডিটেকট করা যায়নি বা তারিখ নির্বাচন করা হয়নি'
        ];
    }
    
    // Extract the structure values
    $teachers_table = $db_structure['teachers_table'];
    $duties_table = $db_structure['duties_table'];
    $teacher_id_field = $db_structure['teacher_id_field'];
    $teacher_name_field = $db_structure['teacher_name_field'];
    $duty_date_field = $db_structure['duty_date_field'];
    $duty_fk_field = $db_structure['duty_fk_field'];
    
    // Build query with flexible date format handling
    $sql = "SELECT 
                d.*, 
                t.$teacher_name_field AS teacher_name,
                t.designation AS designation,
                t.subject AS subject
            FROM $duties_table d
            JOIN $teachers_table t ON d.$duty_fk_field = t.$teacher_id_field
            WHERE 
                ($duty_date_field BETWEEN ? AND ?)
                OR (STR_TO_DATE($duty_date_field, '%Y-%m-%d') BETWEEN ? AND ?)
                OR (STR_TO_DATE($duty_date_field, '%d-%m-%Y') BETWEEN ? AND ?)
            ORDER BY $duty_date_field";
    
    try {
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            // If prepare fails, try a different approach with direct query
            // This is a fallback for older MySQL versions
            $direct_sql = "SELECT 
                d.*, 
                t.$teacher_name_field AS teacher_name,
                t.designation,
                t.subject
            FROM $duties_table d
            JOIN $teachers_table t ON d.$duty_fk_field = t.$teacher_id_field
            WHERE 
                ($duty_date_field BETWEEN '$start_date' AND '$end_date')
                OR (STR_TO_DATE($duty_date_field, '%Y-%m-%d') BETWEEN '$start_date' AND '$end_date')
                OR (STR_TO_DATE($duty_date_field, '%d-%m-%Y') BETWEEN '$start_date' AND '$end_date')
            ORDER BY $duty_date_field";
            
            $result = $conn->query($direct_sql);
            
            if (!$result) {
                return [
                    'success' => false,
                    'error' => 'ডাটাবেস কোয়েরী এরর: ' . $conn->error . ' SQL: ' . $direct_sql
                ];
            }
        } else {
            $stmt->bind_param("ssssss", $start_date, $end_date, $start_date, $end_date, $start_date, $end_date);
            if (!$stmt->execute()) {
                return [
                    'success' => false,
                    'error' => 'স্টেটমেন্ট এক্সিকিউট এরর: ' . $stmt->error
                ];
            }
            $result = $stmt->get_result();
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'কোয়েরী এক্সিকিউশন এরর: ' . $e->getMessage()
        ];
    }
    
    // Process results
    $duties = [];
    $teacher_counts = [];
    $dates = [];
    
    if ($result && $result->num_rows > 0) {
        while ($duty = $result->fetch_assoc()) {
            $duties[] = $duty;
            
            $teacher_id = isset($duty[$duty_fk_field]) ? $duty[$duty_fk_field] : 'unknown';
            $teacher_name = isset($duty['teacher_name']) ? $duty['teacher_name'] : 'Unknown Teacher';
            $duty_date = isset($duty[$duty_date_field]) ? $duty[$duty_date_field] : 'Unknown Date';
            
            if (!isset($teacher_counts[$teacher_id])) {
                $teacher_counts[$teacher_id] = [
                    'name' => $teacher_name,
                    'designation' => $duty['designation'] ?? '',
                    'subject' => $duty['subject'] ?? '',
                    'count' => 0,
                    'dates' => [],
                    'id' => $teacher_id,
                ];
            }
            
            $teacher_counts[$teacher_id]['count']++;
            $teacher_counts[$teacher_id]['dates'][$duty_date] = true;
            $dates[$duty_date] = true;
        }
    }
    
    return [
        'success' => true,
        'duties' => $duties,
        'teacher_counts' => $teacher_counts,
        'dates' => array_keys($dates),
        'total_duties' => count($duties),
        'total_teachers' => count($teacher_counts)
    ];
}

// Function to get custom teacher order from database
function getCustomTeacherOrder($conn, $start_date, $end_date) {
    try {
        // Check if teacher_honorarium_order table exists
        $tableExists = $conn->query("SHOW TABLES LIKE 'teacher_honorarium_order'");
        if (!$tableExists || $tableExists->num_rows === 0) {
            return [];
        }
        
        $sql = "SELECT teacher_id, display_order FROM teacher_honorarium_order 
                WHERE start_date = ? AND end_date = ? 
                ORDER BY display_order ASC";
        
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            return [];
        }
        
        $stmt->bind_param('ss', $start_date, $end_date);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $customOrder = [];
        while ($row = $result->fetch_assoc()) {
            $customOrder[$row['teacher_id']] = $row['display_order'];
        }
        
        return $customOrder;
        
    } catch (Exception $e) {
        return [];
    }
}

// Initialize variables
$result = null;

// Process form when submitted
if ($submitted) {
    $result = getTeacherDutyCounts($conn, $start_date, $end_date, $db_structure);
    
    // Sort teachers based on selected option
    if ($result && $result['success'] && !empty($result['teacher_counts'])) {
        if ($sort_by === 'name') {
            // Sort by name
            uasort($result['teacher_counts'], function($a, $b) {
                return strcasecmp($a['name'], $b['name']);
            });
        } elseif ($sort_by === 'designation') {
            // Sort by designation
            uasort($result['teacher_counts'], function($a, $b) {
                $desig_compare = strcasecmp($a['designation'] ?? '', $b['designation'] ?? '');
                return $desig_compare === 0 ? strcasecmp($a['name'], $b['name']) : $desig_compare;
            });
        } elseif ($sort_by === 'subject') {
            // Sort by subject
            uasort($result['teacher_counts'], function($a, $b) {
                $subject_compare = strcasecmp($a['subject'] ?? '', $b['subject'] ?? '');
                return $subject_compare === 0 ? strcasecmp($a['name'], $b['name']) : $subject_compare;
            });
        } elseif ($sort_by === 'duty_count') {
            // Sort by duty count (highest first)
            uasort($result['teacher_counts'], function($a, $b) {
                $count_compare = $b['count'] - $a['count'];
                return $count_compare === 0 ? strcasecmp($a['name'], $b['name']) : $count_compare;
            });
        } elseif ($sort_by === 'serial') {
            // Keep original order based on ID
            // No need to sort
        } elseif ($sort_by === 'custom') {
            // Sort by custom saved order
            $customOrder = getCustomTeacherOrder($conn, $start_date, $end_date);
            if (!empty($customOrder)) {
                uasort($result['teacher_counts'], function($a, $b) use ($customOrder) {
                    $orderA = $customOrder[$a['id']] ?? 999;
                    $orderB = $customOrder[$b['id']] ?? 999;
                    return $orderA - $orderB;
                });
            }
        }
    }
}

// Function to format Bengali date
function formatBanglaDate($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $parts = explode('-', $date);
    if (count($parts) == 3) {
        $year = $parts[0];
        $month = $parts[1];
        $day = $parts[2];
        
        return "$day " . ($months[$month] ?? $month) . " $year";
    }
    return $date;
}

// Format currency in Bengali
function formatMoney($amount) {
    return number_format($amount, 2);
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক সন্মানী হিসাব</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/themes/material_blue.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #eaf4ff;
            border-bottom: 2px solid #007bff;
            font-weight: 600;
        }
        .table {
            margin-bottom: 0;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .status-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        .date-range-picker {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .debug-info {
            font-family: monospace;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .honorarium-settings {
            background-color: #fff8e1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        @media print {
            .no-print {
                display: none;
            }
            .table {
                width: 100%;
                border: 1px solid #dee2e6;
            }
            .container {
                width: 100%;
                max-width: 100%;
            }
            body {
                font-size: 12px;
            }
            .print-header {
                text-align: center;
                margin-bottom: 20px;
            }
            .print-footer {
                margin-top: 50px;
                text-align: center;
            }
            .signature-cell {
                border-bottom: 1px dotted #000 !important;
                height: 40px;
                min-width: 150px;
            }
            .teacher-serial {
                font-size: 11px !important;
                color: #495057 !important;
                background: #e9ecef !important;
                padding: 1px 4px !important;
                border-radius: 3px !important;
                border: 1px solid #dee2e6 !important;
            }
        }
        
        /* Drag and Drop Styles */
        .draggable-table tbody tr {
            cursor: move;
            transition: all 0.3s ease;
        }
        
        .draggable-table tbody tr:hover {
            background-color: #f8f9fa !important;
        }
        
        .draggable-table tbody tr.dragging {
            opacity: 0.5;
            background: #e3f2fd !important;
            transform: rotate(2deg);
        }
        
        .draggable-table tbody tr.drag-over {
            outline: 2px dashed #007bff;
            background: #f0f8ff !important;
        }
        
        .drag-handle {
            cursor: grab;
            color: #6c757d;
            padding: 5px;
            font-size: 14px;
        }
        
        .drag-handle:hover {
            color: #007bff;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .save-order-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: none;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            border: none;
        }
        
        .save-order-btn:hover {
            transform: translateY(-5px);
            background: #218838;
            color: white;
        }
        
        .save-order-btn.show {
            display: flex;
        }
        
        .alert-success {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: none;
        }
        
        .teacher-serial {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            display: inline-block;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="header print-header">
            <div class="row">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-calculator me-2 text-primary"></i>
                        শিক্ষক সন্মানী হিসাব
                    </h1>
                    <p class="text-muted">নির্বাচিত তারিখের মধ্যে শিক্ষকদের ডিউটি এবং সন্মানী হিসাব করুন</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <?php if ($db_structure): ?>
                        <span class="badge bg-success">
                            <i class="fas fa-check-circle me-1"></i>
                            ডাটাবেস স্ট্রাকচার সনাক্ত করা হয়েছে
                        </span>
                    <?php else: ?>
                        <span class="badge bg-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            ডাটাবেস স্ট্রাকচার সনাক্ত করা যায়নি
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Date Range Selection Form -->
        <div class="date-range-picker no-print">
            <form method="post" action="">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">শুরুর তারিখ</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="date" class="form-control date-picker" id="start_date" name="start_date" value="<?php echo $start_date; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">শেষ তারিখ</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            <input type="date" class="form-control date-picker" id="end_date" name="end_date" value="<?php echo $end_date; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="honorarium_rate" class="form-label">সন্মানীর হার (টাকা)</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-money-bill-alt"></i></span>
                            <input type="number" class="form-control" id="honorarium_rate" name="honorarium_rate" value="<?php echo $honorarium_rate; ?>" min="1" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="sort_by" class="form-label">শিক্ষক সাজানোর ক্রম</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-sort"></i></span>
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>>নাম অনুযায়ী</option>
                                <option value="designation" <?php echo $sort_by === 'designation' ? 'selected' : ''; ?>>পদবি অনুযায়ী</option>
                                <option value="subject" <?php echo $sort_by === 'subject' ? 'selected' : ''; ?>>বিষয় অনুযায়ী</option>
                                <option value="duty_count" <?php echo $sort_by === 'duty_count' ? 'selected' : ''; ?>>ডিউটি সংখ্যা অনুযায়ী</option>
                                <option value="serial" <?php echo $sort_by === 'serial' ? 'selected' : ''; ?>>ক্রমিক অনুযায়ী</option>
                                <option value="custom" <?php echo $sort_by === 'custom' ? 'selected' : ''; ?>>কাস্টম ক্রম (ড্রাগ এন্ড ড্রপ)</option>
                            </select>
                        </div>
                        <small class="form-text text-muted">কাস্টম ক্রম নির্বাচন করলে শিক্ষকদের ড্রাগ এন্ড ড্রপ করে সাজানো যাবে</small>
                    </div>
                </div>
                <input type="hidden" name="submitted" value="1">
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        হিসাব করুন
                    </button>
                    <?php if ($result && $result['success']): ?>
                    <button type="button" class="btn btn-success ms-2" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        প্রিন্ট করুন
                    </button>
                    <?php endif; ?>
                    <a href="database_field_explorer.php" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-database me-2"></i>
                        ডাটাবেস স্ট্রাকচার চেক করুন
                    </a>
                </div>
            </form>
        </div>
        
        <?php if (!$db_structure): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle me-2"></i> ডাটাবেস স্ট্রাকচার সনাক্ত করা যায়নি</h4>
                <p>অনুগ্রহ করে নিশ্চিত করুন যে শিক্ষক এবং ডিউটি টেবিল আছে এবং সঠিকভাবে সেটআপ করা আছে।</p>
                <a href="database_field_explorer.php" class="btn btn-primary">
                    <i class="fas fa-tools me-2"></i>
                    ডাটাবেস ফিল্ড এক্সপ্লোরার ব্যবহার করুন
                </a>
            </div>
        <?php elseif ($submitted && !$result['success']): ?>
            <div class="alert alert-danger">
                <h4><i class="fas fa-exclamation-triangle me-2"></i> ডাটা লোড করতে সমস্যা হয়েছে</h4>
                <p><?php echo $result['error'] ?? 'অজানা সমস্যা'; ?></p>
                
                <div class="debug-info no-print">
                    <h5>ডিবাগ তথ্য:</h5>
                    <ul>
                        <li>শিক্ষক টেবিল: <?php echo $db_structure['teachers_table'] ?? 'অজানা'; ?></li>
                        <li>ডিউটি টেবিল: <?php echo $db_structure['duties_table'] ?? 'অজানা'; ?></li>
                        <li>শিক্ষক ID ফিল্ড: <?php echo $db_structure['teacher_id_field'] ?? 'অজানা'; ?></li>
                        <li>শিক্ষক নাম ফিল্ড: <?php echo $db_structure['teacher_name_field'] ?? 'অজানা'; ?></li>
                        <li>ডিউটি FK ফিল্ড: <?php echo $db_structure['duty_fk_field'] ?? 'অজানা'; ?></li>
                        <li>ডিউটি তারিখ ফিল্ড: <?php echo $db_structure['duty_date_field'] ?? 'অজানা'; ?></li>
                    </ul>
                    <p><a href="database_field_explorer.php" class="btn btn-sm btn-outline-secondary">ডাটাবেস স্ট্রাকচার পরীক্ষা করুন</a></p>
                </div>
            </div>
        <?php elseif ($submitted && $result['success'] && empty($result['duties'])): ?>
            <div class="alert alert-warning">
                <h4><i class="fas fa-info-circle me-2"></i> কোন ডাটা পাওয়া যায়নি</h4>
                <p>নির্বাচিত তারিখের মধ্যে (<?php echo formatBanglaDate($start_date); ?> থেকে <?php echo formatBanglaDate($end_date); ?>) কোন ডিউটি বরাদ্দ করা হয়নি।</p>
                <p>অন্য তারিখ নির্বাচন করুন বা সঠিক ডিউটি ডাটা আছে কিনা নিশ্চিত করুন।</p>
            </div>
        <?php elseif ($submitted && $result['success']): ?>
            <!-- Print Header -->
            <div class="d-none d-print-block print-header mb-4">
                <h2 class="text-center mb-1">শিক্ষক সন্মানী হিসাব রিপোর্ট</h2>
                <h4 class="text-center mb-3">
                    <?php echo formatBanglaDate($start_date); ?> থেকে <?php echo formatBanglaDate($end_date); ?>
                </h4>
                <p class="text-center">সন্মানীর হার: <?php echo formatMoney($honorarium_rate); ?> টাকা</p>
                <p class="text-center mb-4">মোট শিক্ষক: <?php echo count($result['teacher_counts']); ?> জন | মোট ডিউটি: <?php echo count($result['duties']); ?> টি</p>
                <hr>
            </div>
            
            <!-- Results Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">
                        <i class="fas fa-list-alt me-2"></i>
                        শিক্ষক সন্মানীর তালিকা
                    </h3>
                    <div class="no-print">
                        <span class="badge bg-primary">
                            <?php echo count($result['teacher_counts']); ?> জন শিক্ষক
                        </span>
                        <span class="badge bg-info ms-2">
                            <?php echo count($result['duties']); ?> টি ডিউটি
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover draggable-table" id="teacherTable">
                            <thead>
                                <tr>
                                    <th style="width: 50px;">
                                        <i class="fas fa-grip-vertical drag-handle"></i>
                                    </th>
                                    <th>#</th>
                                    <th>নাম</th>
                                    <th>পদবি</th>
                                    <th>বিষয়</th>
                                    <th class="text-center">ডিউটি সংখ্যা</th>
                                    <th class="text-end">মোট সন্মানী (টাকা)</th>
                                    <th class="text-center">শিক্ষকের স্বাক্ষর</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php 
                                $i = 1;
                                $total_honorarium = 0;
                                foreach($result['teacher_counts'] as $teacher_id => $teacher): 
                                    $teacher_honorarium = $teacher['count'] * $honorarium_rate;
                                    $total_honorarium += $teacher_honorarium;
                                ?>
                                <tr draggable="true" data-teacher-id="<?php echo $teacher_id; ?>">
                                    <td>
                                        <i class="fas fa-grip-vertical drag-handle"></i>
                                    </td>
                                    <td class="serial-cell"><?php echo $i++; ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($teacher['name']); ?>
                                        <?php if (!empty($teacher['sl_number'])): ?>
                                            <br><span class="teacher-serial">ক্রমিক: <?php echo htmlspecialchars($teacher['sl_number']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($teacher['designation'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($teacher['subject'] ?? ''); ?></td>
                                    <td class="text-center"><?php echo $teacher['count']; ?></td>
                                    <td class="text-end"><?php echo formatMoney($teacher_honorarium); ?></td>
                                    <td class="text-center" style="width: 150px;">
                                        <div style="border-bottom: 1px dotted #999; height: 30px;" class="signature-cell">&nbsp;</div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="table-dark">
                                    <th></th>
                                    <th colspan="3">সর্বমোট</th>
                                    <th class="text-center"><?php echo count($result['duties']); ?></th>
                                    <th class="text-end"><?php echo formatMoney($total_honorarium); ?></th>
                                    <th></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Date wise duty summary -->
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        তারিখ অনুযায়ী ডিউটি সংখ্যা
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach($result['dates'] as $date): 
                            $date_duties = array_filter($result['duties'], function($duty) use ($date, $db_structure) {
                                $duty_date_field = $db_structure['duty_date_field'];
                                return isset($duty[$duty_date_field]) && $duty[$duty_date_field] == $date;
                            });
                        ?>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title"><?php echo formatBanglaDate($date); ?></h5>
                                    <p class="card-text fs-4"><?php echo count($date_duties); ?> টি ডিউটি</p>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Print Footer -->
            <div class="d-none d-print-block print-footer">
                <div class="row mt-5 pt-5">
                    <div class="col-4 text-center">
                        <p>_______________________</p>
                        <p>প্রস্তুতকারী</p>
                    </div>
                    <div class="col-4 text-center">
                        <p>_______________________</p>
                        <p>পরীক্ষা কমিটির আহবায়ক</p>
                    </div>
                    <div class="col-4 text-center">
                        <p>_______________________</p>
                        <p>অধ্যক্ষ</p>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <p><strong>নোট:</strong> শিক্ষকগণ সন্মানী গ্রহণের সময় স্বাক্ষর করবেন।</p>
                    <small class="text-muted">প্রিন্ট করার তারিখ: <?php echo date('d/m/Y h:i A'); ?></small>
                </div>
            </div>
            
            <!-- Database Structure Info (Debug) -->
            <div class="debug-info no-print">
                <h5>ডাটাবেস স্ট্রাকচার তথ্য:</h5>
                <ul>
                    <li>শিক্ষক টেবিল: <?php echo $db_structure['teachers_table'] ?? 'অজানা'; ?></li>
                    <li>ডিউটি টেবিল: <?php echo $db_structure['duties_table'] ?? 'অজানা'; ?></li>
                    <li>শিক্ষক ID ফিল্ড: <?php echo $db_structure['teacher_id_field'] ?? 'অজানা'; ?></li>
                    <li>শিক্ষক নাম ফিল্ড: <?php echo $db_structure['teacher_name_field'] ?? 'অজানা'; ?></li>
                    <li>ডিউটি FK ফিল্ড: <?php echo $db_structure['duty_fk_field'] ?? 'অজানা'; ?></li>
                    <li>ডিউটি তারিখ ফিল্ড: <?php echo $db_structure['duty_date_field'] ?? 'অজানা'; ?></li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Save Order Button -->
    <button class="save-order-btn no-print" id="saveOrderBtn" onclick="saveTeacherOrder()">
        <i class="fas fa-save fa-lg"></i>
    </button>
    
    <!-- Success Alert -->
    <div class="alert alert-success alert-dismissible fade" id="successAlert" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <span id="successMessage">শিক্ষকদের ক্রম সফলভাবে সংরক্ষিত হয়েছে!</span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize date pickers
            flatpickr(".date-picker", {
                dateFormat: "Y-m-d",
                allowInput: true
            });
            
            // Initialize drag and drop for teacher table
            initializeDragAndDrop();
            
            // Add event listener to sort dropdown
            const sortSelect = document.getElementById('sort_by');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    // Reinitialize drag and drop based on new selection
                    initializeDragAndDrop();
                    
                    // Hide save button if not custom sort
                    const saveBtn = document.getElementById('saveOrderBtn');
                    if (this.value !== 'custom') {
                        saveBtn.classList.remove('show');
                    }
                });
            }
        });
        
        // Drag and Drop functionality
        function initializeDragAndDrop() {
            const tbody = document.querySelector('#teacherTable tbody');
            if (!tbody) return;
            
            // Only enable drag and drop if custom sort is selected
            const sortSelect = document.getElementById('sort_by');
            if (sortSelect && sortSelect.value !== 'custom') {
                return;
            }
            
            let draggingRow = null;
            let originalOrder = [];
            
            // Store original order
            tbody.querySelectorAll('tr').forEach((row, index) => {
                originalOrder.push({
                    element: row,
                    teacherId: row.dataset.teacherId,
                    originalIndex: index
                });
            });
            
            tbody.addEventListener('dragstart', function(e) {
                draggingRow = e.target.closest('tr');
                if (!draggingRow) return;
                
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggingRow.outerHTML);
                draggingRow.classList.add('dragging');
                
                // Show save button
                document.getElementById('saveOrderBtn').classList.add('show');
            });
            
            tbody.addEventListener('dragover', function(e) {
                e.preventDefault();
                let target = e.target.closest('tr');
                if (target && target !== draggingRow && target.parentNode === tbody) {
                    target.classList.add('drag-over');
                }
            });
            
            tbody.addEventListener('dragleave', function(e) {
                let target = e.target.closest('tr');
                if (target) {
                    target.classList.remove('drag-over');
                }
            });
            
            tbody.addEventListener('drop', function(e) {
                e.preventDefault();
                let target = e.target.closest('tr');
                if (draggingRow && target && draggingRow !== target && target.parentNode === tbody) {
                    target.classList.remove('drag-over');
                    
                    if (target.rowIndex > draggingRow.rowIndex) {
                        target.after(draggingRow);
                    } else {
                        target.before(draggingRow);
                    }
                    
                    // Update serial numbers
                    updateSerialNumbers();
                }
                draggingRow.classList.remove('dragging');
                draggingRow = null;
            });
            
            tbody.addEventListener('dragend', function(e) {
                if (draggingRow) draggingRow.classList.remove('dragging');
                tbody.querySelectorAll('tr').forEach(function(row) {
                    row.classList.remove('drag-over');
                });
                draggingRow = null;
            });
        }
        
        // Update serial numbers after drag and drop
        function updateSerialNumbers() {
            const rows = document.querySelectorAll('#teacherTable tbody tr');
            rows.forEach((row, index) => {
                const serialCell = row.querySelector('.serial-cell');
                if (serialCell) {
                    serialCell.textContent = index + 1;
                }
            });
        }
        
        // Save teacher order
        function saveTeacherOrder() {
            const rows = document.querySelectorAll('#teacherTable tbody tr');
            const teacherOrder = [];
            
            rows.forEach((row, index) => {
                const teacherId = row.dataset.teacherId;
                if (teacherId) {
                    teacherOrder.push({
                        teacher_id: teacherId,
                        order: index + 1
                    });
                }
            });
            
            // Send AJAX request to save order
            fetch('ajax/save_teacher_order.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    teacher_order: teacherOrder,
                    start_date: document.getElementById('start_date').value,
                    end_date: document.getElementById('end_date').value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccessMessage('শিক্ষকদের ক্রম সফলভাবে সংরক্ষিত হয়েছে!');
                    document.getElementById('saveOrderBtn').classList.remove('show');
                } else {
                    showSuccessMessage('শিক্ষকদের ক্রম সংরক্ষণে সমস্যা হয়েছে: ' + (data.message || 'অজানা সমস্যা'), 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showSuccessMessage('শিক্ষকদের ক্রম সংরক্ষণে সমস্যা হয়েছে!', 'danger');
            });
        }
        
        // Show success/error message
        function showSuccessMessage(message, type = 'success') {
            const alert = document.getElementById('successAlert');
            const messageSpan = document.getElementById('successMessage');
            
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            messageSpan.textContent = message;
            
            alert.style.display = 'block';
            
            // Auto hide after 3 seconds
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
