<?php
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/Student.php';

// Get form data
$seatingPattern = $_POST['seating_pattern'] ?? 'serial';
$totalRooms = (int)($_POST['total_rooms'] ?? 3);
$subjectCode = $_POST['subject_code'] ?? '';
$roomNumbers = $_POST['room_numbers'] ?? [];
$startIndexes = $_POST['start_indexes'] ?? [];
$endIndexes = $_POST['end_indexes'] ?? [];
$totalStudents = $_POST['total_students'] ?? [];

// Get room-specific configurations
$roomConfigs = [];
for ($i = 1; $i <= $totalRooms; $i++) {
    $columns = (int)($_POST["room_columns_$i"] ?? $_POST['room_columns'] ?? 8);
    $seatsPerBench = (int)($_POST["seats_per_bench_$i"] ?? $_POST['seats_per_bench'] ?? 2);

    // Get column-wise bench configuration
    $columnBenches = [];
    $columnSingleSeat = [];
    for ($col = 1; $col <= $columns; $col++) {
        $columnBenches[$col] = (int)($_POST["column_{$col}_benches_$i"] ?? 5);
        $columnSingleSeat[$col] = (bool)($_POST["column_{$col}_single_seat_$i"] ?? false);
    }

    $roomConfigs[$i] = [
        'columns' => $columns,
        'column_benches' => $columnBenches,
        'column_single_seat' => $columnSingleSeat,
        'seats_per_bench' => $seatsPerBench
    ];
}

// Get students from database
try {
    $database = new Database();
    $db = $database->getConnection();

    // Get students based on subject code or all students
    if (!empty($subjectCode)) {
        // Check if old columns exist
        try {
            $checkStmt = $db->query("SHOW COLUMNS FROM students LIKE 'sub_1'");
            $hasOldColumns = $checkStmt->rowCount() > 0;
        } catch (Exception $e) {
            $hasOldColumns = false;
        }

        if ($hasOldColumns) {
            $stmt = $db->prepare("SELECT * FROM students WHERE
                subjects LIKE ? OR
                sub_1 = ? OR sub_2 = ? OR sub_3 = ? OR sub_4 = ? OR sub_5 = ? OR
                sub_6 = ? OR sub_7 = ? OR sub_8 = ? OR sub_9 = ? OR sub_10 = ? OR
                sub_11 = ? OR sub_12 = ? OR sub_13 = ?
                ORDER BY CAST(roll AS UNSIGNED) ASC");
            $params = ['%' . $subjectCode . '%'];
            $params = array_merge($params, array_fill(0, 13, $subjectCode));
        } else {
            $stmt = $db->prepare("SELECT * FROM students WHERE subjects LIKE ? ORDER BY CAST(roll AS UNSIGNED) ASC");
            $params = ['%' . $subjectCode . '%'];
        }

        $stmt->execute($params);
        $filteredStudents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $stmt = $db->query("SELECT * FROM students ORDER BY CAST(roll AS UNSIGNED) ASC");
        $filteredStudents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Distribute students among rooms based on indexes
    $allStudents = [];
    for ($i = 0; $i < count($startIndexes); $i++) {
        $startIndex = (int)$startIndexes[$i] - 1; // Convert to 0-based index
        $endIndex = (int)$endIndexes[$i] - 1;     // Convert to 0-based index

        $roomStudents = [];
        for ($j = $startIndex; $j <= $endIndex && $j < count($filteredStudents); $j++) {
            $roomStudents[] = $filteredStudents[$j];
        }

        $allStudents[$i + 1] = $roomStudents;
    }
} catch (Exception $e) {
    die("Database error: " . $e->getMessage());
}

// Seating arrangement functions
function generateSerialSeating($students, $config) {
    $seats = [];
    $studentIndex = 0;

    for ($col = 0; $col < $config['columns']; $col++) {
        $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
        $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
        $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];

        for ($bench = 0; $bench < $benchesInThisColumn; $bench++) {
            for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                if ($studentIndex < count($students)) {
                    $seats[$col][$bench][$seat] = $students[$studentIndex];
                    $studentIndex++;
                } else {
                    $seats[$col][$bench][$seat] = null;
                }
            }
        }
    }

    return $seats;
}

function generateZigzagSeating($students, $config) {
    $seats = [];
    $studentIndex = 0;

    for ($col = 0; $col < $config['columns']; $col++) {
        $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
        $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
        $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];
        $isEvenColumn = ($col % 2 == 0);

        if ($isEvenColumn) {
            // Top to bottom
            for ($bench = 0; $bench < $benchesInThisColumn; $bench++) {
                for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                    if ($studentIndex < count($students)) {
                        $seats[$col][$bench][$seat] = $students[$studentIndex];
                        $studentIndex++;
                    } else {
                        $seats[$col][$bench][$seat] = null;
                    }
                }
            }
        } else {
            // Bottom to top
            for ($bench = $benchesInThisColumn - 1; $bench >= 0; $bench--) {
                for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                    if ($studentIndex < count($students)) {
                        $seats[$col][$bench][$seat] = $students[$studentIndex];
                        $studentIndex++;
                    } else {
                        $seats[$col][$bench][$seat] = null;
                    }
                }
            }
        }
    }

    return $seats;
}

function generateDiagonalSeating($students, $config) {
    $seats = [];
    $studentIndex = 0;

    // Initialize empty seats
    for ($col = 0; $col < $config['columns']; $col++) {
        $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
        $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
        $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];

        for ($bench = 0; $bench < $benchesInThisColumn; $bench++) {
            for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                $seats[$col][$bench][$seat] = null;
            }
        }
    }

    // Get maximum benches in any column
    $maxBenches = max($config['column_benches']);

    // Fill diagonally
    for ($diag = 0; $diag < ($config['columns'] + $maxBenches); $diag++) {
        for ($col = 0; $col < $config['columns']; $col++) {
            $bench = $diag - $col;
            $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
            $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
            $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];

            if ($bench >= 0 && $bench < $benchesInThisColumn) {
                for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                    if ($studentIndex < count($students)) {
                        $seats[$col][$bench][$seat] = $students[$studentIndex];
                        $studentIndex++;
                    }
                }
            }
        }
    }

    return $seats;
}

function generateAlternateSeating($students, $config) {
    $seats = [];
    $studentIndex = 0;

    // Initialize empty seats
    for ($col = 0; $col < $config['columns']; $col++) {
        $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
        $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
        $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];

        for ($bench = 0; $bench < $benchesInThisColumn; $bench++) {
            for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                $seats[$col][$bench][$seat] = null;
            }
        }
    }

    // Fill alternate positions
    for ($col = 0; $col < $config['columns']; $col++) {
        $benchesInThisColumn = $config['column_benches'][$col + 1] ?? 5;
        $isSingleSeatColumn = $config['column_single_seat'][$col + 1] ?? false;
        $seatsInThisBench = $isSingleSeatColumn ? 1 : $config['seats_per_bench'];

        for ($bench = 0; $bench < $benchesInThisColumn; $bench++) {
            // Skip every other bench in odd columns
            if ($col % 2 == 1 && $bench % 2 == 1) continue;

            for ($seat = 0; $seat < $seatsInThisBench; $seat++) {
                if ($studentIndex < count($students)) {
                    $seats[$col][$bench][$seat] = $students[$studentIndex];
                    $studentIndex++;
                }
            }
        }
    }

    return $seats;
}

// Generate seating arrangements for all rooms
$roomSeatingPlans = [];
foreach ($allStudents as $roomNum => $students) {
    $config = $roomConfigs[$roomNum];
    
    switch ($seatingPattern) {
        case 'zigzag':
            $roomSeatingPlans[$roomNum] = generateZigzagSeating($students, $config);
            break;
        case 'diagonal':
            $roomSeatingPlans[$roomNum] = generateDiagonalSeating($students, $config);
            break;
        case 'alternate':
            $roomSeatingPlans[$roomNum] = generateAlternateSeating($students, $config);
            break;
        default:
            $roomSeatingPlans[$roomNum] = generateSerialSeating($students, $config);
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>রুম ওয়াইজ সীট প্লান - EXMM</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .room-page {
            page-break-after: always;
            background: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .room-page:last-child {
            page-break-after: avoid;
        }
        
        .room-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        
        .college-name {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }
        
        .college-address {
            font-size: 16px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .exam-title {
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .room-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            margin-bottom: 25px;
        }
        
        .room-number {
            font-size: 18px;
            font-weight: 600;
        }
        
        .pattern-info {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .seating-grid {
            display: grid;
            gap: 15px;
            margin: 20px 0;
            justify-content: center;
        }
        
        .column {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
        }
        
        .column-header {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            font-weight: 600;
        }

        .column-header.single-seat-column {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            border: 2px solid #d35400;
        }
        
        .bench {
            display: flex;
            gap: 5px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 8px;
            background: white;
            min-height: 60px;
            align-items: center;
        }

        .bench.single-seat-bench {
            border-color: #f39c12;
            background: linear-gradient(135deg, #fef9e7 0%, #fcf3cf 100%);
            justify-content: center;
        }
        
        .seat {
            width: 80px;
            height: 50px;
            border: 1px solid #ccc;
            border-radius: 5px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            background: #f8f9fa;
            text-align: center;
            padding: 2px;
        }
        
        .seat.occupied {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .seat.empty {
            background: #e9ecef;
            border-style: dashed;
        }

        .seat.single-seat {
            width: 100px;
            border: 2px solid #f39c12;
            background: linear-gradient(135deg, #fef9e7 0%, #fcf3cf 100%);
        }

        .seat.single-seat.occupied {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
            border-color: #d35400;
        }
        
        .roll-number {
            font-weight: 700;
            font-size: 12px;
        }
        
        .student-name {
            font-size: 9px;
            line-height: 1.1;
            margin-top: 2px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .room-page {
                box-shadow: none;
                margin-bottom: 0;
                padding: 20px;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <!-- Controls -->
    <div class="controls no-print">
        <button onclick="window.print()" class="btn btn-primary">
            🖨️ প্রিন্ট করুন
        </button>
        <a href="dynamic_seat_plan.php" class="btn btn-success">
            ⬅️ ফিরে যান
        </a>
    </div>

    <?php foreach ($roomSeatingPlans as $roomNum => $seatingPlan): ?>
        <div class="room-page">
            <!-- Room Header -->
            <div class="room-header">
                <div class="college-name">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</div>
                <div class="college-address">দামুড়হুদা, চুয়াডাঙ্গা</div>
                <div class="exam-title">HSC পরীক্ষা-২০২৫ সীট প্লান</div>
            </div>

            <!-- Room Info -->
            <div class="room-info">
                <div class="room-number">
                    রুম নং: <?php echo $roomNumbers[$roomNum - 1] ?? $roomNum; ?>
                </div>
                <div class="pattern-info">
                    প্যাটার্ন: <?php
                        $patterns = [
                            'serial' => 'সিরিয়াল',
                            'zigzag' => 'ঝিকঝাক',
                            'diagonal' => 'কোনাকোনি',
                            'alternate' => 'বিকল্প'
                        ];
                        echo $patterns[$seatingPattern] ?? 'সিরিয়াল';
                    ?> |
                    <?php if (!empty($subjectCode)): ?>
                        বিষয় কোড: <?php echo $subjectCode; ?> |
                    <?php endif; ?>
                    ইনডেক্স: <?php echo $startIndexes[$roomNum - 1] ?? 1; ?> - <?php echo $endIndexes[$roomNum - 1] ?? count($allStudents[$roomNum]); ?> |
                    শিক্ষার্থী: <?php echo count($allStudents[$roomNum]); ?>
                </div>
            </div>

            <!-- Seating Grid -->
            <div class="seating-grid" style="grid-template-columns: repeat(<?php echo $roomConfigs[$roomNum]['columns']; ?>, 1fr);">
                <?php for ($col = 0; $col < $roomConfigs[$roomNum]['columns']; $col++): ?>
                    <?php
                    $benchesInThisColumn = $roomConfigs[$roomNum]['column_benches'][$col + 1] ?? 5;
                    $isSingleSeatColumn = $roomConfigs[$roomNum]['column_single_seat'][$col + 1] ?? false;
                    $seatsInThisBench = $isSingleSeatColumn ? 1 : $roomConfigs[$roomNum]['seats_per_bench'];
                    ?>
                    <div class="column">
                        <div class="column-header <?php echo $isSingleSeatColumn ? 'single-seat-column' : ''; ?>">
                            <?php if ($isSingleSeatColumn): ?>
                                <i class="fas fa-user me-1"></i>
                            <?php endif; ?>
                            কলাম <?php echo $col + 1; ?> (<?php echo $benchesInThisColumn; ?> বেঞ্চ)
                            <?php if ($isSingleSeatColumn): ?>
                                <small class="d-block">১ সীট/বেঞ্চ</small>
                            <?php endif; ?>
                        </div>

                        <?php for ($bench = 0; $bench < $benchesInThisColumn; $bench++): ?>
                            <div class="bench <?php echo $isSingleSeatColumn ? 'single-seat-bench' : ''; ?>">
                                <?php for ($seat = 0; $seat < $seatsInThisBench; $seat++): ?>
                                    <?php
                                    $student = $seatingPlan[$col][$bench][$seat] ?? null;
                                    $seatClass = $student ? 'seat occupied' : 'seat empty';
                                    if ($isSingleSeatColumn) {
                                        $seatClass .= ' single-seat';
                                    }
                                    ?>
                                    <div class="<?php echo $seatClass; ?>">
                                        <?php if ($student): ?>
                                            <div class="roll-number"><?php echo htmlspecialchars($student['roll_no']); ?></div>
                                            <div class="student-name"><?php echo htmlspecialchars(substr($student['student_name'], 0, 15)); ?></div>
                                        <?php else: ?>
                                            <div class="roll-number">-</div>
                                        <?php endif; ?>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        <?php endfor; ?>
                    </div>
                <?php endfor; ?>
            </div>

            <!-- Room Statistics -->
            <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                <strong>রুম পরিসংখ্যান:</strong>
                <?php
                $totalSeats = 0;
                $singleSeatColumns = 0;
                $regularSeatColumns = 0;

                for ($col = 1; $col <= $roomConfigs[$roomNum]['columns']; $col++) {
                    $benches = $roomConfigs[$roomNum]['column_benches'][$col] ?? 5;
                    $isSingleSeat = $roomConfigs[$roomNum]['column_single_seat'][$col] ?? false;

                    if ($isSingleSeat) {
                        $totalSeats += $benches * 1; // 1 seat per bench
                        $singleSeatColumns++;
                    } else {
                        $totalSeats += $benches * $roomConfigs[$roomNum]['seats_per_bench'];
                        $regularSeatColumns++;
                    }
                }
                ?>
                মোট সীট: <?php echo $totalSeats; ?> |
                দখলকৃত: <?php echo count($allStudents[$roomNum]); ?> |
                খালি: <?php echo $totalSeats - count($allStudents[$roomNum]); ?> |
                কলাম: <?php echo $roomConfigs[$roomNum]['columns']; ?>টি
                <?php if ($singleSeatColumns > 0): ?>
                    | একক সীট কলাম: <?php echo $singleSeatColumns; ?>টি
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>

    <script>
        // Auto-adjust grid layout for better printing
        window.addEventListener('beforeprint', function() {
            document.querySelectorAll('.seating-grid').forEach(grid => {
                const columns = grid.style.gridTemplateColumns.match(/\d+/)[0];
                if (columns > 8) {
                    grid.style.fontSize = '10px';
                }
                if (columns > 10) {
                    grid.style.fontSize = '9px';
                }
            });
        });

        // Restore normal size after printing
        window.addEventListener('afterprint', function() {
            document.querySelectorAll('.seating-grid').forEach(grid => {
                grid.style.fontSize = '';
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                window.print();
            }
            if (e.key === 'Escape') {
                window.location.href = 'dynamic_seat_plan.php';
            }
        });
    </script>
</body>
</html>
