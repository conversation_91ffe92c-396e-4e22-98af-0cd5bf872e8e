<?php
// Test Teacher Data Display
require_once 'includes/teacher_db.php';

echo "<h2>🧪 Teacher Data Test</h2>";

try {
    // Get teachers from database
    $teachers = $teacherManager->getAllTeachers();
    
    echo "<h3>📊 Database Teachers Data:</h3>";
    echo "<p>Total teachers: " . count($teachers) . "</p>";
    
    if (!empty($teachers)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>SL Number</th><th>Name</th><th>Mobile</th><th>Subject</th><th>Designation</th><th>Status</th>";
        echo "</tr>";
        
        foreach ($teachers as $teacher) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($teacher['id'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['sl_number'] ?? $teacher['sl'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['name'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['mobile'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['subject'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['designation'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['duty_status'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test the specific field access that was causing the error
        echo "<h3>🔍 Field Access Test:</h3>";
        $firstTeacher = $teachers[0];
        echo "<ul>";
        echo "<li><strong>Using 'sl_number':</strong> " . htmlspecialchars($firstTeacher['sl_number'] ?? 'NOT FOUND') . "</li>";
        echo "<li><strong>Using 'sl':</strong> " . htmlspecialchars($firstTeacher['sl'] ?? 'NOT FOUND') . "</li>";
        echo "<li><strong>Using null coalescing:</strong> " . htmlspecialchars($firstTeacher['sl_number'] ?? $firstTeacher['sl'] ?? 'BOTH NOT FOUND') . "</li>";
        echo "</ul>";
        
        // Show all available keys
        echo "<h3>🔑 Available Keys in Teacher Array:</h3>";
        echo "<ul>";
        foreach (array_keys($firstTeacher) as $key) {
            echo "<li><code>$key</code> = " . htmlspecialchars($firstTeacher[$key]) . "</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p>No teachers found in database.</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; background: #ffebee; padding: 10px; border-radius: 5px;'>";
    echo "<strong>Error:</strong> " . $e->getMessage();
    echo "</div>";
}

echo "<br><p><a href='teacher_duty_management.php'>← Back to Teacher Management</a></p>";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Data Test - EXMM</title>
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        table {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background: #007bff;
            color: white;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        ul {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- PHP output will be displayed here -->
</body>
</html>
