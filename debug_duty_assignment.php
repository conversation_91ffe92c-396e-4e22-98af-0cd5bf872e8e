<?php
require_once 'includes/teacher_db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>ডিউটি অ্যাসাইনমেন্ট ডিবাগ</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Hind Siliguri', sans-serif; }</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔍 ডিউটি অ্যাসাইনমেন্ট ডিবাগ</h1>";

// Test duty assignment
if (isset($_POST['test_assignment'])) {
    $dutyDate = $_POST['duty_date'];
    $selectedTeachers = $_POST['selected_teachers'] ?? [];
    
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-info text-white'>";
    echo "<h5 class='mb-0'>🧪 টেস্ট রেজাল্ট</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<h6>পাঠানো ডেটা:</h6>";
    echo "<ul>";
    echo "<li><strong>তারিখ:</strong> $dutyDate</li>";
    echo "<li><strong>নির্বাচিত শিক্ষক IDs:</strong> " . implode(', ', $selectedTeachers) . "</li>";
    echo "<li><strong>মোট শিক্ষক:</strong> " . count($selectedTeachers) . " জন</li>";
    echo "</ul>";
    
    try {
        $teacherManager->assignDuty($dutyDate, $selectedTeachers);
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>ডিউটি সফলভাবে বন্টন করা হয়েছে!";
        echo "</div>";
        
        // Show assigned teachers
        $assignments = $teacherManager->getDutyAssignments($dutyDate);
        if (!empty($assignments)) {
            echo "<h6>বন্টনকৃত শিক্ষকগণ:</h6>";
            echo "<table class='table table-striped'>";
            echo "<thead><tr><th>ID</th><th>নাম</th><th>বিষয়</th><th>পদবী</th></tr></thead>";
            echo "<tbody>";
            foreach ($assignments as $assignment) {
                echo "<tr>";
                echo "<td>" . $assignment['teacher_id'] . "</td>";
                echo "<td>" . htmlspecialchars($assignment['name']) . "</td>";
                echo "<td>" . htmlspecialchars($assignment['subject']) . "</td>";
                echo "<td>" . htmlspecialchars($assignment['designation']) . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
        }
        
    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>ডিউটি বন্টনে সমস্যা: " . $e->getMessage();
        echo "</div>";
    }
    
    echo "</div></div>";
}

// Show all teachers
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'>👥 সব শিক্ষক</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $teachers = $teacherManager->getAllTeachers();
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>কোন শিক্ষক পাওয়া যায়নি!";
        echo "</div>";
    } else {
        echo "<p><strong>মোট শিক্ষক:</strong> " . count($teachers) . " জন</p>";
        
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>SL</th><th>নাম</th><th>বিষয়</th><th>পদবী</th><th>ডিউটি স্ট্যাটাস</th></tr></thead>";
        echo "<tbody>";
        foreach ($teachers as $teacher) {
            echo "<tr>";
            echo "<td><strong>" . ($teacher['id'] ?? 'N/A') . "</strong></td>";
            echo "<td>" . ($teacher['sl_number'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($teacher['name']) . "</td>";
            echo "<td>" . htmlspecialchars($teacher['subject']) . "</td>";
            echo "<td>" . htmlspecialchars($teacher['designation']) . "</td>";
            echo "<td>";
            $status = $teacher['duty_status'];
            $badgeClass = $status === 'সম সময় অন্তর্ভুক্ত' ? 'bg-success' : ($status === 'কখনো অন্তর্ভুক্ত নয়' ? 'bg-danger' : 'bg-primary');
            echo "<span class='badge $badgeClass'>$status</span>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>শিক্ষক তালিকা লোড করতে সমস্যা: " . $e->getMessage();
    echo "</div>";
}

echo "</div></div>";

// Test form
if (!empty($teachers)) {
    echo "<div class='card mb-4'>";
    echo "<div class='card-header bg-success text-white'>";
    echo "<h5 class='mb-0'>🧪 ডিউটি অ্যাসাইনমেন্ট টেস্ট</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    
    echo "<form method='POST'>";
    echo "<div class='row'>";
    echo "<div class='col-md-6'>";
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>তারিখ নির্বাচন করুন:</label>";
    echo "<input type='date' name='duty_date' class='form-control' value='" . date('Y-m-d') . "' required>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='mb-3'>";
    echo "<label class='form-label'>শিক্ষক নির্বাচন করুন:</label>";
    echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; padding: 10px; background: #f8f9fa;'>";
    
    foreach ($teachers as $teacher) {
        if ($teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়') {
            $checked = $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'checked' : '';
            $disabled = $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'disabled' : '';
            
            echo "<div class='form-check mb-2'>";
            echo "<input class='form-check-input' type='checkbox' name='selected_teachers[]' value='" . $teacher['id'] . "' id='teacher_" . $teacher['id'] . "' $checked $disabled>";
            echo "<label class='form-check-label' for='teacher_" . $teacher['id'] . "'>";
            echo "<strong>" . htmlspecialchars($teacher['name']) . "</strong> ";
            echo "<small class='text-muted'>(" . htmlspecialchars($teacher['subject']) . " - " . htmlspecialchars($teacher['designation']) . ")</small>";
            if ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') {
                echo " <span class='badge bg-success'>সব সময়</span>";
            }
            echo "</label>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    echo "</div>";
    
    echo "<button type='submit' name='test_assignment' class='btn btn-success'>";
    echo "<i class='fas fa-test-tube me-2'></i>টেস্ট করুন";
    echo "</button>";
    echo "</form>";
    
    echo "</div></div>";
}

// Show existing duty assignments
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5 class='mb-0'>📅 বিদ্যমান ডিউটি বন্টন</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $dutyDates = $teacherManager->getAllDutyDates();
    
    if (empty($dutyDates)) {
        echo "<div class='alert alert-info'>";
        echo "<i class='fas fa-info-circle me-2'></i>কোন ডিউটি বন্টন পাওয়া যায়নি।";
        echo "</div>";
    } else {
        echo "<p><strong>মোট ডিউটি তারিখ:</strong> " . count($dutyDates) . " টি</p>";
        
        foreach ($dutyDates as $date) {
            $assignments = $teacherManager->getDutyAssignments($date);
            echo "<div class='card mb-3'>";
            echo "<div class='card-header'>";
            echo "<h6 class='mb-0'>" . date('d F Y', strtotime($date)) . " (" . count($assignments) . " জন শিক্ষক)</h6>";
            echo "</div>";
            echo "<div class='card-body'>";
            
            if (!empty($assignments)) {
                echo "<div class='row'>";
                foreach ($assignments as $assignment) {
                    echo "<div class='col-md-4 mb-2'>";
                    echo "<div class='border rounded p-2'>";
                    echo "<strong>" . htmlspecialchars($assignment['name']) . "</strong><br>";
                    echo "<small class='text-muted'>" . htmlspecialchars($assignment['subject']) . "</small>";
                    echo "</div>";
                    echo "</div>";
                }
                echo "</div>";
            }
            
            echo "</div>";
            echo "</div>";
        }
    }
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>ডিউটি তালিকা লোড করতে সমস্যা: " . $e->getMessage();
    echo "</div>";
}

echo "</div></div>";

// Navigation
echo "<div class='text-center'>";
echo "<a href='teacher_duty_management.php' class='btn btn-primary me-2'>";
echo "<i class='fas fa-users me-2'></i>শিক্ষক ব্যবস্থাপনা";
echo "</a>";
echo "<a href='check_teacher_data.php' class='btn btn-info'>";
echo "<i class='fas fa-search me-2'></i>ডেটা চেক";
echo "</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
