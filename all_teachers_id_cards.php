<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all teachers from database
$allTeachers = $teacherManager->getAllTeachers();

// Get principal signature
$principalSignature = $teacherManager->getActiveSignature('principal');
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সকল শিক্ষকের আইডি কার্ড - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .teacher-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .teacher-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #dee2e6;
        }
        
        .id-card-preview {
            background: linear-gradient(135deg, #e8f0fe 0%, #f3e5f5 100%);
            color: #000;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            position: relative;
            overflow: hidden;
        }

        .id-card-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="40" fill="none" stroke="rgba(0,0,0,0.05)" stroke-width="1"/></svg>');
            opacity: 0.2;
        }
        
        .card-content {
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-id-card me-3"></i>সকল শিক্ষকের আইডি কার্ড
                    </h1>
                    <p class="text-white-50">সকল শিক্ষকের আইডি কার্ড জেনারেট ও প্রিন্ট করুন</p>
                </div>

                <!-- Navigation -->
                <div class="text-center mb-4">
                    <a href="index.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-home me-2"></i>হোম
                    </a>
                    <a href="teacher_duty_management.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনা
                    </a>
                    <a href="teacher_id_card_generator.php" class="btn btn-outline-light">
                        <i class="fas fa-calendar-alt me-2"></i>তারিখ ভিত্তিক আইডি কার্ড
                    </a>
                </div>

                <!-- Main Content -->
                <div class="main-card">
                    <?php if (!empty($allTeachers)): ?>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4>
                                <i class="fas fa-id-card text-success me-2"></i>
                                সকল শিক্ষকের আইডি কার্ড (<?php echo count($allTeachers); ?> জন)
                            </h4>
                            <div>
                                <a href="print_all_teachers_id_cards.php" 
                                   target="_blank" class="btn btn-success">
                                    <i class="fas fa-print me-2"></i>সব কার্ড প্রিন্ট করুন
                                </a>
                            </div>
                        </div>

                        <!-- ID Card Preview -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="id-card-preview">
                                    <div class="card-content">
                                        <div class="text-center">
                                            <h5 class="mb-2" style="font-size: 16px; font-weight: bold; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h5>
                                            <h6 class="mb-3" style="font-size: 14px; font-weight: 600; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">দামুড়হুদা, চুয়াডাঙ্গা</h6>
                                            <h6 class="mb-2" style="font-size: 13px; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">HSC পরীক্ষা-২০২৫</h6>
                                            <small class="mb-3 d-block" style="font-size: 12px; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">শিক্ষক আইডি কার্ড</small>
                                            <div class="d-flex flex-column align-items-center mb-3">
                                                <div class="mb-2">
                                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                        <i class="fas fa-user fa-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="text-center">
                                                    <h6 class="mb-1" style="font-size: 14px; font-weight: bold; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.4);">নমুনা শিক্ষক</h6>
                                                    <small style="font-size: 12px; font-weight: 600; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">সহকারী অধ্যাপক</small><br>
                                                    <small style="font-size: 12px; font-weight: 600; color: #000; text-shadow: 2px 2px 6px rgba(255,255,255,0.8), 1px 1px 3px rgba(0,0,0,0.3);">বাংলা বিভাগ</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>আইডি কার্ডের বৈশিষ্ট্য:</h6>
                                    <ul class="mb-0">
                                        <li>হেডিং এর বাম পাশে কলেজ লোগো</li>
                                        <li>শিক্ষকের ছবি, নাম, পদবী ও বিষয়</li>
                                        <li>নিচে ডান দিকে অধ্যক্ষের স্বাক্ষর</li>
                                        <li>প্রিন্ট-ফ্রেন্ডলি ডিজাইন</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Teachers List -->
                        <div class="row">
                            <?php foreach ($allTeachers as $index => $teacher): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="teacher-card">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>"
                                                         alt="Teacher Photo" class="teacher-photo">
                                                <?php else: ?>
                                                    <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                        <i class="fas fa-user text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                <small class="text-muted d-block"><?php echo htmlspecialchars($teacher['designation']); ?></small>
                                                <small class="text-muted"><?php echo htmlspecialchars($teacher['subject']); ?></small>
                                            </div>
                                            <div>
                                                <a href="print_all_teachers_id_cards.php?teacher_id=<?php echo $teacher['id']; ?>" 
                                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-print me-1"></i>প্রিন্ট
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">কোন শিক্ষক পাওয়া যায়নি</h5>
                            <p class="text-muted">প্রথমে শিক্ষক যুক্ত করুন</p>
                            <a href="teacher_duty_management.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>শিক্ষক যুক্ত করুন
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
