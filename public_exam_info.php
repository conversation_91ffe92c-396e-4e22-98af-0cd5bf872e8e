<?php
require_once 'includes/exam_data_helper.php';

// Initialize exam data helper
$examHelper = new ExamDataHelper();
$examData = $examHelper->getExamOverview();

// Get specific date details if requested
$selectedDate = $_GET['date'] ?? '';
$dateDetails = [];
if ($selectedDate) {
    $dateDetails = $examHelper->getExamDetailsByDate($selectedDate);
}

// Get student search results if requested
$studentInfo = [];
if (isset($_GET['search_student'])) {
    $roll = $_GET['roll'] ?? '';
    $registration = $_GET['registration'] ?? '';
    $studentInfo = $examHelper->getStudentSeatInfo($roll, $registration);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার বিস্তারিত তথ্য - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Bengali', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .date-badge {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
        }
        
        .teacher-card {
            background: rgba(79, 70, 229, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-left: 4px solid #4f46e5;
        }
        
        .room-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
        }
        
        .search-section {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }

            .search-section {
                padding: 1rem;
            }

            .info-card {
                padding: 1rem;
            }

            .teacher-card {
                padding: 0.75rem;
                font-size: 0.9rem;
            }

            .room-header {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }

            .date-badge {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            h1.display-5 {
                font-size: 1.8rem;
            }

            .row.mb-4 h3 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .search-section {
                padding: 0.75rem;
            }

            .info-card {
                padding: 0.75rem;
            }

            .teacher-card {
                padding: 0.5rem;
                font-size: 0.85rem;
            }

            .room-header h6 {
                font-size: 0.85rem;
            }

            h1.display-5 {
                font-size: 1.5rem;
            }

            .lead {
                font-size: 0.95rem;
            }

            .btn {
                font-size: 0.9rem;
                padding: 0.5rem 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="text-center text-white">
                    <h1 class="display-5 fw-bold mb-2">
                        <i class="fas fa-info-circle me-3"></i>
                        পরীক্ষার বিস্তারিত তথ্য
                    </h1>
                    <p class="lead">শিক্ষার্থী ও অভিভাবকদের জন্য সকল পরীক্ষার তথ্য</p>
                    <a href="index.php" class="btn btn-light btn-sm">
                        <i class="fas fa-home me-2"></i>হোম পেজে ফিরুন
                    </a>
                </div>
            </div>
        </div>

        <!-- Student Search Section -->
        <div class="search-section">
            <h4 class="mb-3"><i class="fas fa-search me-2"></i>শিক্ষার্থীর তথ্য খুঁজুন</h4>
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">রোল নম্বর</label>
                    <input type="text" class="form-control" name="roll" value="<?php echo htmlspecialchars($_GET['roll'] ?? ''); ?>" placeholder="রোল নম্বর লিখুন">
                </div>
                <div class="col-md-4">
                    <label class="form-label">রেজিস্ট্রেশন নম্বর</label>
                    <input type="text" class="form-control" name="registration" value="<?php echo htmlspecialchars($_GET['registration'] ?? ''); ?>" placeholder="রেজিস্ট্রেশন নম্বর লিখুন">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" name="search_student" class="btn btn-primary d-block w-100">
                        <i class="fas fa-search me-2"></i>খুঁজুন
                    </button>
                </div>
            </form>
            
            <?php if (!empty($studentInfo)): ?>
            <div class="mt-4">
                <h5>খোঁজার ফলাফল:</h5>
                <?php foreach ($studentInfo as $student): ?>
                <div class="info-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="fw-bold"><?php echo htmlspecialchars($student['name']); ?></h6>
                            <p class="mb-1">
                                <strong>রোল:</strong> <?php echo htmlspecialchars($student['roll']); ?> |
                                <strong>রেজিস্ট্রেশন:</strong> <?php echo htmlspecialchars($student['registration']); ?>
                            </p>
                            <p class="mb-1">
                                <strong>বিভাগ:</strong> <?php echo htmlspecialchars($student['department']); ?> |
                                <strong>সেশন:</strong> <?php echo htmlspecialchars($student['academic_year']); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="seat_card_generator.php?roll=<?php echo urlencode($student['roll']); ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-id-card me-1"></i>সীট কার্ড
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php elseif (isset($_GET['search_student'])): ?>
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                কোন শিক্ষার্থীর তথ্য পাওয়া যায়নি। অনুগ্রহ করে সঠিক রোল বা রেজিস্ট্রেশন নম্বর দিন।
            </div>
            <?php endif; ?>
        </div>

        <!-- Date Selection -->
        <div class="info-card mb-4">
            <h4 class="mb-3"><i class="fas fa-calendar-alt me-2"></i>তারিখ অনুযায়ী পরীক্ষার তথ্য</h4>
            <form method="GET" class="row g-3">
                <div class="col-md-8">
                    <select name="date" class="form-select">
                        <option value="">তারিখ নির্বাচন করুন</option>
                        <?php foreach ($examData['upcoming_exams'] as $exam): ?>
                        <option value="<?php echo $exam['duty_date']; ?>" <?php echo ($selectedDate == $exam['duty_date']) ? 'selected' : ''; ?>>
                            <?php echo date('d F, Y (l)', strtotime($exam['duty_date'])); ?> - 
                            <?php echo $exam['teacher_count']; ?> জন শিক্ষক, 
                            <?php echo $exam['room_count']; ?> টি রুম
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-eye me-2"></i>বিস্তারিত দেখুন
                    </button>
                </div>
            </form>
        </div>

        <!-- Selected Date Details -->
        <?php if (!empty($dateDetails['assignments'])): ?>
        <div class="info-card">
            <div class="date-badge mb-3">
                <i class="fas fa-calendar-day me-2"></i>
                <?php echo date('d F, Y (l)', strtotime($selectedDate)); ?>
            </div>
            
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="text-center">
                        <h3 class="text-primary"><?php echo $dateDetails['statistics']['total_teachers']; ?></h3>
                        <small class="text-muted">মোট শিক্ষক</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h3 class="text-success"><?php echo $dateDetails['statistics']['total_rooms']; ?></h3>
                        <small class="text-muted">মোট রুম</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h3 class="text-info"><?php echo $dateDetails['statistics']['subjects_involved']; ?></h3>
                        <small class="text-muted">বিষয় সংখ্যা</small>
                    </div>
                </div>
            </div>

            <!-- Room wise breakdown -->
            <h5 class="mb-3"><i class="fas fa-door-open me-2"></i>রুম ভিত্তিক বিন্যাস</h5>
            <div class="row">
                <?php foreach ($dateDetails['room_summary'] as $room => $data): ?>
                <div class="col-md-6 mb-3">
                    <div class="room-header">
                        <h6 class="mb-0">
                            <i class="fas fa-door-closed me-2"></i>
                            <?php echo htmlspecialchars($room); ?>
                            <span class="float-end"><?php echo $data['teacher_count']; ?> জন শিক্ষক</span>
                        </h6>
                    </div>
                    <?php foreach ($data['teachers'] as $teacher): ?>
                    <div class="teacher-card">
                        <strong><?php echo htmlspecialchars($teacher['teacher_name']); ?></strong><br>
                        <small>
                            <?php echo htmlspecialchars($teacher['designation']); ?> | 
                            <?php echo htmlspecialchars($teacher['subject']); ?>
                        </small>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Print Options -->
            <div class="text-center mt-4">
                <a href="print_date_wise_duty.php?date=<?php echo $selectedDate; ?>" class="btn btn-success me-2" target="_blank">
                    <i class="fas fa-print me-2"></i>ডিউটি তালিকা প্রিন্ট
                </a>
                <a href="print_room_duty.php?date=<?php echo $selectedDate; ?>" class="btn btn-info" target="_blank">
                    <i class="fas fa-door-open me-2"></i>রুম ভিত্তিক প্রিন্ট
                </a>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Links -->
        <div class="row mt-4">
            <div class="col-md-3 mb-2">
                <a href="view_students.php" class="btn btn-outline-light w-100">
                    <i class="fas fa-users me-2"></i>সকল শিক্ষার্থী
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="seat_card_generator.php" class="btn btn-outline-light w-100">
                    <i class="fas fa-id-card me-2"></i>সীট কার্ড
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="date_wise_duty.php" class="btn btn-outline-light w-100">
                    <i class="fas fa-calendar me-2"></i>ডিউটি তালিকা
                </a>
            </div>
            <div class="col-md-3 mb-2">
                <a href="room_wise_duty_assignment.php" class="btn btn-outline-light w-100">
                    <i class="fas fa-door-open me-2"></i>রুম বিন্যাস
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
