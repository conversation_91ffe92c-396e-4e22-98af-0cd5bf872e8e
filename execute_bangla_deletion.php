<?php
require_once 'models/Student.php';

// Set content type to <PERSON><PERSON><PERSON> for AJAX response
header('Content-Type: application/json');

try {
    $student = new Student();
    
    // Get Bengali students count before deletion
    $allStudents = $student->getAll();
    $banglaStudents = [];
    
    foreach ($allStudents as $s) {
        $hasCode101 = false;
        for ($i = 1; $i <= 13; $i++) {
            if (trim($s["sub_$i"]) === '101') {
                if (!$hasCode101) {
                    $banglaStudents[] = $s;
                    $hasCode101 = true;
                }
            }
        }
    }
    
    $totalBanglaStudents = count($banglaStudents);
    
    if ($totalBanglaStudents > 0) {
        // Delete students with Bengali subject code 101
        if ($student->deleteBySubjectCode('101')) {
            echo json_encode([
                'success' => true,
                'message' => "সফলভাবে {$totalBanglaStudents} জন বাংলা বিষয়ের শিক্ষার্থী মুছে ফেলা হয়েছে!",
                'deleted_count' => $totalBanglaStudents
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'বাংলা বিষয়ের শিক্ষার্থী মুছতে সমস্যা হয়েছে।'
            ]);
        }
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'কোন বাংলা বিষয়ের শিক্ষার্থী পাওয়া যায়নি।',
            'deleted_count' => 0
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
