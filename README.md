# EXMM - Student Management System

একটি সম্পূর্ণ Student Management System যেখানে Excel file upload করে student তথ্য সংরক্ষণ করা যায়।

## Features

- ✅ Excel file upload (.xlsx, .xls, .csv)
- ✅ Bulk student data import
- ✅ Data validation
- ✅ Search functionality
- ✅ Export to Excel
- ✅ Responsive design
- ✅ Error handling

## Database Structure

Database Name: `exmm`

### Students Table Fields:
- C.Code
- EIIN
- Roll No.
- Reg. No.
- Session
- Type
- Group
- Student's Name (Required)
- Father's Name
- Gender (Male/Female/Other)
- Sub 1-13 (Subject fields)

## Setup Instructions

### 1. Database Setup
```sql
-- Import the database_setup.sql file
mysql -u root -p < database_setup.sql
```

### 2. Install Dependencies
```bash
composer install
```

### 3. Configure Database
Edit `config/database.php` file:
```php
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');
define('DB_PASSWORD', '');
define('DB_NAME', 'exmm');
```

### 4. Create Required Directories
```bash
mkdir uploads
chmod 777 uploads
```

### 5. Web Server Setup
- Place files in your web server directory (e.g., htdocs for XAMPP)
- Start Apache and MySQL
- Access via browser: `http://localhost/exmm/`

## File Structure

```
exmm/
├── config/
│   └── database.php          # Database configuration
├── models/
│   └── Student.php           # Student model class
├── utils/
│   └── ExcelProcessor.php    # Excel processing utility
├── uploads/                  # Upload directory
├── upload.php               # Main upload form
├── process_upload.php       # File processing
├── upload_result.php        # Upload result page
├── view_students.php        # View all students
├── download_template.php    # Download Excel template
├── database_setup.sql       # Database structure
├── composer.json           # Dependencies
└── README.md               # This file
```

## Usage

### 1. Upload Excel File
- Go to `upload.php`
- Select or drag & drop Excel file
- File will be validated and processed
- View results on result page

### 2. Excel File Format
- First row should contain headers
- Column order must match template
- Student's Name is required
- Gender: Male, Female, or Other
- Maximum file size: 10MB

### 3. View Students
- Go to `view_students.php`
- Search by Roll No., Name, or Session
- View all student records in table format

### 4. Download Template
- Click "Download Template" button
- Use the template for proper formatting

## Requirements

- PHP 7.4+
- MySQL 5.7+
- Composer
- PhpSpreadsheet library

## Error Handling

- File validation (size, format)
- Data validation (required fields, format)
- Database error handling
- User-friendly error messages

## Security Features

- File type validation
- SQL injection prevention (PDO)
- XSS protection (htmlspecialchars)
- File upload security

## Browser Support

- Chrome, Firefox, Safari, Edge
- Responsive design for mobile devices

## License

This project is open source and available under the MIT License.
