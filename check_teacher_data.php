<?php
require_once 'includes/teacher_db.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>শিক্ষক ডেটা চেক</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap' rel='stylesheet'>";
echo "<style>body { font-family: 'Hind Siliguri', sans-serif; }</style>";
echo "</head><body class='bg-light'>";

echo "<div class='container py-5'>";
echo "<h1 class='text-center mb-5'>🔍 শিক্ষক ডেটা চেক</h1>";

// Check database structure
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-primary text-white'>";
echo "<h5 class='mb-0'>📋 Database Table Structure</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $stmt = $pdo->query("DESCRIBE teachers");
    $columns = $stmt->fetchAll();
    
    echo "<table class='table table-striped'>";
    echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr></thead>";
    echo "<tbody>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>" . $column['Field'] . "</strong></td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</tbody></table>";
} catch (Exception $e) {
    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Check actual data
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-info text-white'>";
echo "<h5 class='mb-0'>📊 Actual Teacher Data</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    $stmt = $pdo->query("SELECT id, sl_number, name, mobile, subject, designation FROM teachers LIMIT 10");
    $teachers = $stmt->fetchAll();
    
    if (empty($teachers)) {
        echo "<div class='alert alert-warning'>কোন শিক্ষক ডেটা পাওয়া যায়নি!</div>";
    } else {
        echo "<table class='table table-striped'>";
        echo "<thead><tr><th>ID</th><th>SL Number</th><th>Name</th><th>Mobile</th><th>Subject</th><th>Designation</th></tr></thead>";
        echo "<tbody>";
        foreach ($teachers as $teacher) {
            echo "<tr>";
            echo "<td>" . ($teacher['id'] ?? 'NULL') . "</td>";
            echo "<td><strong>" . ($teacher['sl_number'] ?? 'NULL') . "</strong></td>";
            echo "<td>" . ($teacher['name'] ?? 'NULL') . "</td>";
            echo "<td>" . ($teacher['mobile'] ?? 'NULL') . "</td>";
            echo "<td>" . ($teacher['subject'] ?? 'NULL') . "</td>";
            echo "<td>" . ($teacher['designation'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        
        // Count total teachers
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM teachers");
        $total = $stmt->fetch()['total'];
        echo "<p><strong>মোট শিক্ষক:</strong> $total জন</p>";
        
        // Count teachers with NULL sl_number
        $stmt = $pdo->query("SELECT COUNT(*) as null_count FROM teachers WHERE sl_number IS NULL OR sl_number = ''");
        $nullCount = $stmt->fetch()['null_count'];
        echo "<p><strong>SL Number নেই:</strong> $nullCount জন</p>";
        
        // Count teachers with valid sl_number
        $stmt = $pdo->query("SELECT COUNT(*) as valid_count FROM teachers WHERE sl_number IS NOT NULL AND sl_number != ''");
        $validCount = $stmt->fetch()['valid_count'];
        echo "<p><strong>SL Number আছে:</strong> $validCount জন</p>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Check for specific issues
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-warning text-dark'>";
echo "<h5 class='mb-0'>⚠️ Data Issues</h5>";
echo "</div>";
echo "<div class='card-body'>";

try {
    // Check for empty sl_numbers
    $stmt = $pdo->query("SELECT id, name, sl_number FROM teachers WHERE sl_number IS NULL OR sl_number = '' OR sl_number = '0'");
    $problematicTeachers = $stmt->fetchAll();
    
    if (!empty($problematicTeachers)) {
        echo "<div class='alert alert-warning'>";
        echo "<h6>🚨 SL Number সমস্যা আছে এই শিক্ষকদের:</h6>";
        echo "<ul>";
        foreach ($problematicTeachers as $teacher) {
            echo "<li>ID: " . $teacher['id'] . " - " . $teacher['name'] . " (SL: '" . $teacher['sl_number'] . "')</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='mt-3'>";
        echo "<a href='fix_sl_numbers.php' class='btn btn-warning'>";
        echo "<i class='fas fa-wrench me-2'></i>SL Numbers ঠিক করুন";
        echo "</a>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>সব শিক্ষকের SL Number সঠিক আছে!";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
}

echo "</div></div>";

// Sample data for testing
echo "<div class='card mb-4'>";
echo "<div class='card-header bg-success text-white'>";
echo "<h5 class='mb-0'>🧪 Test Data</h5>";
echo "</div>";
echo "<div class='card-body'>";

echo "<p>যদি কোন ডেটা না থাকে, তাহলে নমুনা ডেটা যুক্ত করতে পারেন:</p>";
echo "<a href='setup_database.php' class='btn btn-success me-2'>";
echo "<i class='fas fa-database me-2'></i>নমুনা ডেটা যুক্ত করুন";
echo "</a>";
echo "<a href='teacher_duty_management.php' class='btn btn-primary'>";
echo "<i class='fas fa-users me-2'></i>শিক্ষক ব্যবস্থাপনায় যান";
echo "</a>";

echo "</div></div>";

echo "</div>";
echo "</body></html>";
?>
