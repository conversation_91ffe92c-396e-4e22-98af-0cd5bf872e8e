<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV Upload Test - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .test-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            CSV Upload Complete Test
                        </h3>
                        <small>CSV upload functionality এর সম্পূর্ণ test</small>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        $testResults = [];
                        $allTestsPassed = true;
                        
                        // Test 1: Database Connection
                        try {
                            require_once 'config/database.php';
                            $database = new Database();
                            $pdo = $database->getConnection();
                            $testResults[] = ['✅ Database Connection', 'Database connection successful', 'success'];
                        } catch (Exception $e) {
                            $testResults[] = ['❌ Database Connection', 'Error: ' . $e->getMessage(), 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Test 2: Students Table Structure
                        try {
                            $stmt = $pdo->query("DESCRIBE students");
                            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                            
                            $requiredColumns = ['name', 'father_name', 'gender', 'roll', 'registration', 'department', 'academic_year', 'student_type', 'subjects'];
                            $missingColumns = array_diff($requiredColumns, $columns);
                            
                            if (empty($missingColumns)) {
                                $testResults[] = ['✅ Table Structure', 'All required columns exist: ' . implode(', ', $requiredColumns), 'success'];
                            } else {
                                $testResults[] = ['❌ Table Structure', 'Missing columns: ' . implode(', ', $missingColumns), 'error'];
                                $allTestsPassed = false;
                            }
                        } catch (Exception $e) {
                            $testResults[] = ['❌ Table Structure', 'Error checking table: ' . $e->getMessage(), 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Test 3: Student Model
                        try {
                            require_once 'models/Student.php';
                            $student = new Student();
                            $testResults[] = ['✅ Student Model', 'Student model loaded successfully', 'success'];
                        } catch (Exception $e) {
                            $testResults[] = ['❌ Student Model', 'Error loading Student model: ' . $e->getMessage(), 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Test 4: CSV Processor
                        try {
                            require_once 'utils/SimpleCSVProcessor.php';
                            $testResults[] = ['✅ CSV Processor', 'CSV processor loaded successfully', 'success'];
                        } catch (Exception $e) {
                            $testResults[] = ['❌ CSV Processor', 'Error loading CSV processor: ' . $e->getMessage(), 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Test 5: Uploads Directory
                        $uploadsDir = __DIR__ . '/uploads';
                        if (is_dir($uploadsDir) && is_writable($uploadsDir)) {
                            $testResults[] = ['✅ Uploads Directory', 'Directory exists and is writable: ' . $uploadsDir, 'success'];
                        } else {
                            $testResults[] = ['❌ Uploads Directory', 'Directory missing or not writable: ' . $uploadsDir, 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Test 6: Sample CSV File
                        $sampleCSV = __DIR__ . '/sample_students.csv';
                        if (file_exists($sampleCSV)) {
                            $testResults[] = ['✅ Sample CSV', 'Sample CSV file available for testing', 'success'];
                        } else {
                            $testResults[] = ['⚠️ Sample CSV', 'Sample CSV file not found (optional)', 'warning'];
                        }
                        
                        // Test 7: PHP Configuration
                        $uploadMaxFilesize = ini_get('upload_max_filesize');
                        $postMaxSize = ini_get('post_max_size');
                        $fileUploads = ini_get('file_uploads');
                        
                        if ($fileUploads) {
                            $testResults[] = ['✅ PHP Upload Config', "File uploads enabled. Max file size: $uploadMaxFilesize, Max post size: $postMaxSize", 'success'];
                        } else {
                            $testResults[] = ['❌ PHP Upload Config', 'File uploads are disabled in PHP configuration', 'error'];
                            $allTestsPassed = false;
                        }
                        
                        // Display test results
                        foreach ($testResults as $result) {
                            echo "<div class='test-result test-{$result[2]}'>";
                            echo "<strong>{$result[0]}</strong><br>";
                            echo $result[1];
                            echo "</div>";
                        }
                        ?>
                        
                        <hr>
                        
                        <?php if ($allTestsPassed): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>All Tests Passed!</h5>
                                <p class="mb-0">Your CSV upload system is ready to use. You can now upload CSV files without any issues.</p>
                            </div>
                            
                            <h5><i class="fas fa-rocket me-2"></i>Ready to Use:</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="csv_upload.php" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-upload me-2"></i>Go to CSV Upload
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="sample_students.csv" class="btn btn-info w-100 mb-2" download>
                                        <i class="fas fa-download me-2"></i>Download Sample CSV
                                    </a>
                                </div>
                            </div>
                            
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Some Tests Failed!</h5>
                                <p class="mb-0">Please fix the issues above before using CSV upload.</p>
                            </div>
                            
                            <h5><i class="fas fa-tools me-2"></i>Fix Issues:</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="database_migration.php" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-database me-2"></i>Fix Database
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="system_startup_check.php" class="btn btn-info w-100 mb-2">
                                        <i class="fas fa-check-circle me-2"></i>System Check
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="setup_database.php" class="btn btn-danger w-100 mb-2">
                                        <i class="fas fa-refresh me-2"></i>Reset Database
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <h5><i class="fas fa-info-circle me-2"></i>Sample CSV Format:</h5>
                        <div class="code-block">student_name,father_name,gender,roll_no,reg_no,group_name,session,type,sub_1,sub_2,sub_3,sub_4,sub_5,sub_6,sub_7,sub_8,sub_9,sub_10,sub_11,sub_12,sub_13
আব্দুল করিম,মোঃ আব্দুল হামিদ,Male,100001,REG2024001,Science,2024,Regular,101,102,103,104,105,106,107,108,109,110,111,112,113
ফাতেমা খাতুন,মোঃ আব্দুল কাদের,Female,100002,REG2024002,Science,2024,Regular,101,102,103,104,105,106,107,108,109,110,111,112,113</div>
                        
                        <div class="text-center mt-3">
                            <button onclick="location.reload()" class="btn btn-secondary">
                                <i class="fas fa-refresh me-2"></i>Re-run Tests
                            </button>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
