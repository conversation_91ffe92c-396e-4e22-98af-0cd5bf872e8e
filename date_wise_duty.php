<?php
session_start();
require_once 'includes/teacher_db.php';

// Get all duty dates
$dutyDates = $teacherManager->getAllDutyDates();

// Initialize variables
$selectedDate = $_GET['date'] ?? '';
$assignments = [];

// Get assignments for selected date
if ($selectedDate) {
    $assignments = $teacherManager->getDutyAssignments($selectedDate);
    $dateDetails = $teacherManager->getDutyDateDetails($selectedDate);
}

// Format date in Bengali
function formatDateBengali($date) {
    $months = [
        '01' => 'জানুয়ারি', '02' => 'ফেব্রুয়ারি', '03' => 'মার্চ', '04' => 'এপ্রিল',
        '05' => 'মে', '06' => 'জুন', '07' => 'জুলাই', '08' => 'আগস্ট',
        '09' => 'সেপ্টেম্বর', '10' => 'অক্টোবর', '11' => 'নভেম্বর', '12' => 'ডিসেম্বর'
    ];
    
    $day = date('d', strtotime($date));
    $month = $months[date('m', strtotime($date))];
    $year = date('Y', strtotime($date));
    
    return "$day $month $year";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>তারিখ অনুযায়ী ডিউটি দেখুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .date-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .teacher-photo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .exam-info {
            background: #f0f8ff;
            border-left: 4px solid #007bff;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: 0 5px 5px 0;
        }
        
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .table {
            margin-bottom: 0;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table td, .table th {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="container py-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="text-white mb-3">
                    <i class="fas fa-calendar-day me-3"></i>তারিখ অনুযায়ী ডিউটি
                </h1>
                <p class="text-white-50">নির্দিষ্ট তারিখের ডিউটি তালিকা দেখুন</p>
            </div>

            <!-- Date Selector -->
            <div class="date-selector">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">
                            <i class="fas fa-calendar-day me-2"></i>তারিখ নির্বাচন করুন
                        </h4>
                        <p class="mb-0">যে তারিখের ডিউটি তালিকা দেখতে চান সেই তারিখ নির্বাচন করুন</p>
                    </div>
                    <div class="col-md-4">
                        <form method="GET" class="d-flex">
                            <select name="date" class="form-select me-2" required>
                                <option value="">তারিখ নির্বাচন করুন</option>
                                <?php foreach ($dutyDates as $date): ?>
                                    <option value="<?php echo $date; ?>" <?php echo ($selectedDate == $date) ? 'selected' : ''; ?>>
                                        <?php echo formatDateBengali($date); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-card">
                <?php if ($selectedDate): ?>
                    <h4 class="mb-4">
                        <i class="fas fa-list text-primary me-2"></i>
                        <?php echo formatDateBengali($selectedDate); ?> তারিখের ডিউটি তালিকা
                    </h4>
                    
                    <?php if (!empty($dateDetails)): ?>
                    <div class="exam-info">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <strong><i class="fas fa-book me-2"></i>পরীক্ষার ধরন:</strong>
                                <span class="ms-2"><?php echo $dateDetails['exam_type'] == 'theory' ? 'তত্ত্বীয়' : 'ব্যবহারিক'; ?></span>
                            </div>
                            <div class="col-md-3 mb-2">
                                <strong><i class="fas fa-clock me-2"></i>শিফট:</strong>
                                <span class="ms-2">
                                    <?php 
                                    if ($dateDetails['exam_shift'] == 'Morning') echo 'সকাল';
                                    elseif ($dateDetails['exam_shift'] == 'Afternoon') echo 'বিকাল';
                                    else echo 'উভয়';
                                    ?>
                                </span>
                            </div>
                            <?php if (!empty($dateDetails['exam_subject'])): ?>
                            <div class="col-md-3 mb-2">
                                <strong><i class="fas fa-book-open me-2"></i>বিষয়:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($dateDetails['exam_subject']); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if (!empty($dateDetails['exam_code'])): ?>
                            <div class="col-md-3 mb-2">
                                <strong><i class="fas fa-hashtag me-2"></i>কোড:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($dateDetails['exam_code']); ?></span>
                            </div>
                            <?php endif; ?>
                            <?php if (!empty($dateDetails['notes'])): ?>
                            <div class="col-12 mt-2">
                                <strong><i class="fas fa-sticky-note me-2"></i>নোট:</strong>
                                <span class="ms-2"><?php echo htmlspecialchars($dateDetails['notes']); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (empty($assignments)): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            এই তারিখে কোন ডিউটি বন্টন করা হয়নি!
                        </div>
                        <div class="text-center mt-4">
                            <a href="date_wise_duty_assignment.php?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>এই তারিখে ডিউটি বন্টন করুন
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">ছবি</th>
                                        <th scope="col">নাম</th>
                                        <th scope="col">বিষয়</th>
                                        <th scope="col">পদবি</th>
                                        <th scope="col">মোবাইল</th>
                                        <th scope="col">রুম নং</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $i = 1; foreach ($assignments as $assignment): ?>
                                    <tr>
                                        <td><?php echo $i++; ?></td>
                                        <td>
                                            <?php if (!empty($assignment['photo']) && file_exists($assignment['photo'])): ?>
                                                <img src="<?php echo htmlspecialchars($assignment['photo']); ?>" 
                                                     alt="Teacher Photo" class="teacher-photo">
                                            <?php else: ?>
                                                <div class="teacher-photo d-flex align-items-center justify-content-center bg-light">
                                                    <i class="fas fa-user text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($assignment['teacher_name']); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['subject']); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['designation']); ?></td>
                                        <td><?php echo htmlspecialchars($assignment['mobile']); ?></td>
                                        <td>
                                            <?php if (!empty($assignment['room_number'])): ?>
                                                <span class="badge bg-info"><?php echo htmlspecialchars($assignment['room_number']); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">বরাদ্দ নেই</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="text-center mt-4">
                            <a href="print_duty_list.php?date=<?php echo urlencode($selectedDate); ?>" target="_blank" class="btn btn-success">
                                <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                            </a>
                            <a href="date_wise_duty_assignment.php?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-primary ms-2">
                                <i class="fas fa-edit me-2"></i>ডিউটি সম্পাদনা করুন
                            </a>
                            <a href="duty_letter_generator.php?date=<?php echo urlencode($selectedDate); ?>" class="btn btn-info ms-2">
                                <i class="fas fa-file-alt me-2"></i>ডিউটি লেটার
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-day fa-4x text-muted mb-3"></i>
                        <h4>তারিখ নির্বাচন করুন</h4>
                        <p class="text-muted">ডিউটি তালিকা দেখার জন্য উপরে থেকে একটি তারিখ নির্বাচন করুন।</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>