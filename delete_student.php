<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';
$studentInfo = null;

// Get student ID from URL
$studentId = $_GET['id'] ?? null;

if (!$studentId) {
    header('Location: view_students.php');
    exit;
}

// Get student information
$student = new Student();
$studentInfo = $student->getById($studentId);

if (!$studentInfo) {
    header('Location: view_students.php');
    exit;
}

// Handle delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
        try {
            if ($student->delete($studentId)) {
                $message = 'স্টুডেন্ট সফলভাবে মুছে ফেলা হয়েছে!';
                $messageType = 'success';
                $studentInfo = null; // Clear student info after deletion
            } else {
                $message = 'স্টুডেন্ট মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্টুডেন্ট মুছুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background: rgba(220, 53, 69, 0.1);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        
                        <?php if ($messageType === 'success'): ?>
                            <div class="text-center">
                                <a href="view_students.php" class="btn btn-primary btn-lg">
                                    <i class="fas fa-arrow-left"></i> স্টুডেন্ট লিস্টে ফিরে যান
                                </a>
                            </div>
                            <script>
                                // Auto redirect after 2 seconds
                                setTimeout(function() {
                                    window.location.href = 'view_students.php';
                                }, 2000);
                            </script>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if ($studentInfo): ?>
                        <div class="card danger-zone">
                            <div class="card-header bg-danger text-white text-center">
                                <h3><i class="fas fa-exclamation-triangle"></i> স্টুডেন্ট মুছুন</h3>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <h5><i class="fas fa-info-circle"></i> স্টুডেন্ট তথ্য:</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>নাম:</strong> <?php echo htmlspecialchars($studentInfo['student_name']); ?></p>
                                            <p><strong>রোল নং:</strong> <?php echo htmlspecialchars($studentInfo['roll_no']); ?></p>
                                            <p><strong>রেজি. নং:</strong> <?php echo htmlspecialchars($studentInfo['reg_no']); ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>সেশন:</strong> <?php echo htmlspecialchars($studentInfo['session']); ?></p>
                                            <p><strong>গ্রুপ:</strong> <?php echo htmlspecialchars($studentInfo['group_name']); ?></p>
                                            <p><strong>পিতার নাম:</strong> <?php echo htmlspecialchars($studentInfo['father_name']); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-danger text-center">
                                    <h5><i class="fas fa-exclamation-triangle"></i> সতর্কতা!</h5>
                                    <p class="mb-0">এই স্টুডেন্টের সকল তথ্য স্থায়ীভাবে মুছে যাবে। এই অ্যাকশন আর ফিরিয়ে আনা যাবে না!</p>
                                </div>

                                <div class="text-center">
                                    <form method="POST" onsubmit="return confirmDelete()">
                                        <input type="hidden" name="confirm_delete" value="yes">
                                        <button type="submit" class="btn btn-danger btn-lg me-3">
                                            <i class="fas fa-trash-alt"></i> হ্যাঁ, মুছে ফেলুন
                                        </button>
                                        <a href="view_students.php" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-times"></i> না, বাতিল করুন
                                        </a>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete() {
            return confirm('আপনি কি নিশ্চিত যে আপনি এই স্টুডেন্টকে মুছে ফেলতে চান?\n\nএই অ্যাকশনটি আর ফিরিয়ে আনা যাবে না!');
        }
    </script>
</body>
</html>
