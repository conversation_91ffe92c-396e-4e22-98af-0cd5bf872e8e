<?php
// Direct Debugging Tool for Teacher Honorarium Calculator
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database connection
require_once 'db_connect.php';

// Set default dates for testing
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days')); // Last 30 days by default
$end_date = $_GET['end_date'] ?? date('Y-m-d');

echo '<h2>সন্মানী হিসাব ডিবাগিং টুল</h2>';

// Step 1: Check database connection
echo '<h3>১. ডাটাবেস কানেকশন চেক:</h3>';
if ($conn) {
    echo "<div style='color:green'>✓ কানেকশন সফল</div>";
} else {
    echo "<div style='color:red'>✗ কানেকশন ব্যর্থ</div>";
    echo "<div style='color:red'>Error: " . mysqli_connect_error() . "</div>";
    exit;
}

// Step 2: Check tables existence
echo '<h3>২. টেবিল চেক:</h3>';
$tables = ['teachers', 'duty_assignments'];
$tables_exist = true;

foreach ($tables as $table) {
    $result = $conn->query("SHOW TABLES LIKE '$table'");
    if ($result->num_rows > 0) {
        echo "<div style='color:green'>✓ '$table' টেবিল আছে</div>";
    } else {
        echo "<div style='color:red'>✗ '$table' টেবিল নেই</div>";
        $tables_exist = false;
    }
}

if (!$tables_exist) {
    echo "<div style='color:red;margin-top:10px;'>ERROR: আবশ্যক টেবিল নেই। ডাটাবেস সেটআপ করুন।</div>";
    echo "<p><a href='setup_database.php' style='color:blue;'>ডাটাবেস সেটআপ করুন</a></p>";
    exit;
}

// Step 3: Check records count in both tables
echo '<h3>৩. টেবিলে রেকর্ড চেক:</h3>';

$teachers_count = $conn->query("SELECT COUNT(*) as count FROM teachers")->fetch_assoc()['count'];
echo "<div>শিক্ষকের সংখ্যা: <strong>$teachers_count</strong></div>";

$duties_count = $conn->query("SELECT COUNT(*) as count FROM duty_assignments")->fetch_assoc()['count'];
echo "<div>মোট ডিউটি অ্যাসাইনমেন্ট: <strong>$duties_count</strong></div>";

if ($duties_count == 0) {
    echo "<div style='color:red;margin-top:10px;'>ERROR: কোন ডিউটি অ্যাসাইনমেন্ট নেই! প্রথমে ডিউটি অ্যাসাইন করুন।</div>";
    echo "<p><a href='duty_assignments_management.php' style='color:blue;'>ডিউটি অ্যাসাইন করুন</a></p>";
    exit;
}

// Step 4: Check table structure
echo '<h3>৪. টেবিল স্ট্রাকচার চেক:</h3>';

// Check duty_assignments structure
$duty_columns = $conn->query("SHOW COLUMNS FROM duty_assignments");
$has_duty_date = false;
$has_teacher_id = false;

echo "<p>duty_assignments টেবিল কলাম:</p>";
echo "<ul>";
while ($column = $duty_columns->fetch_assoc()) {
    echo "<li>{$column['Field']} - {$column['Type']}" . ($column['Key'] ? " (Key: {$column['Key']})" : "") . "</li>";
    
    if ($column['Field'] == 'duty_date') {
        $has_duty_date = true;
    }
    if ($column['Field'] == 'teacher_id') {
        $has_teacher_id = true;
    }
}
echo "</ul>";

if (!$has_duty_date || !$has_teacher_id) {
    echo "<div style='color:red'>ERROR: আবশ্যক কলাম নেই ('duty_date' or 'teacher_id')</div>";
    exit;
}

// Step 5: Check date format in duty_assignments
echo '<h3>৫. তারিখ ফরম্যাট চেক:</h3>';

$date_query = $conn->query("SELECT duty_date FROM duty_assignments LIMIT 5");
if ($date_query->num_rows > 0) {
    echo "<p>duty_date কলাম নমুনা:</p>";
    echo "<ul>";
    while ($date_row = $date_query->fetch_assoc()) {
        echo "<li>{$date_row['duty_date']}</li>";
    }
    echo "</ul>";
    
    // Get a sample date
    $date_query->data_seek(0);
    $sample_date = $date_query->fetch_assoc()['duty_date'];
    
    // Analyze date format
    echo "<p>সম্ভাব্য তারিখ ফরম্যাট: ";
    
    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $sample_date)) {
        echo "<strong>Y-m-d</strong> (YYYY-MM-DD)";
    } elseif (preg_match('/^\d{2}-\d{2}-\d{4}$/', $sample_date)) {
        echo "<strong>d-m-Y</strong> (DD-MM-YYYY)";
    } elseif (preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $sample_date)) {
        echo "<strong>d/m/Y</strong> (DD/MM/YYYY)";
    } else {
        echo "<span style='color:orange'>অজ্ঞাত ফরম্যাট</span>";
    }
    
    echo "</p>";
}

// Step 6: Test join query
echo '<h3>৬. জয়েন কুয়েরী টেস্ট:</h3>';

$test_join = $conn->query("
    SELECT a.duty_date, a.room_number, t.teacher_name
    FROM duty_assignments a
    JOIN teachers t ON a.teacher_id = t.id
    LIMIT 3
");

if ($test_join === false) {
    echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
} elseif ($test_join->num_rows == 0) {
    echo "<div style='color:red'>ERROR: জয়েন কুয়েরী কোন রেজাল্ট দেয়নি। টেবিল সম্পর্ক (teacher_id) সঠিক নাও হতে পারে।</div>";
} else {
    echo "<div style='color:green'>✓ টেবিল জয়েন সফল</div>";
    echo "<ul>";
    while ($row = $test_join->fetch_assoc()) {
        echo "<li>তারিখ: {$row['duty_date']}, রুম: {$row['room_number']}, শিক্ষক: {$row['teacher_name']}</li>";
    }
    echo "</ul>";
}

// Step 7: Test direct date filtering with multiple formats
echo '<h3>৭. তারিখ সীমার কুয়েরী টেস্ট:</h3>';

// Testing form
echo "<form method='get'>";
echo "<div style='margin-bottom:10px'>";
echo "<label for='start_date'>শুরুর তারিখ: </label>";
echo "<input type='date' id='start_date' name='start_date' value='$start_date'>";
echo "</div>";

echo "<div style='margin-bottom:10px'>";
echo "<label for='end_date'>শেষের তারিখ: </label>";
echo "<input type='date' id='end_date' name='end_date' value='$end_date'>";
echo "</div>";

echo "<button type='submit' style='background:#007bff;color:white;border:none;padding:5px 10px;'>টেস্ট করুন</button>";
echo "</form>";

echo "<p>টেস্ট করছি: $start_date থেকে $end_date</p>";

// Test with direct format
$sql = "SELECT COUNT(*) as count FROM duty_assignments 
        WHERE duty_date BETWEEN '$start_date' AND '$end_date'";
$result = $conn->query($sql);
$direct_count = $result->fetch_assoc()['count'];

echo "<p>সরাসরি তারিখ কুয়েরী: <strong>$direct_count</strong> টি ফলাফল</p>";

// Test with STR_TO_DATE conversion for different formats
$formats = [
    ['%Y-%m-%d', 'YYYY-MM-DD'],
    ['%d-%m-%Y', 'DD-MM-YYYY'],
    ['%d/%m/%Y', 'DD/MM/YYYY'],
    ['%m/%d/%Y', 'MM/DD/YYYY']
];

foreach ($formats as $format) {
    $sql = "SELECT COUNT(*) as count FROM duty_assignments 
            WHERE STR_TO_DATE(duty_date, '{$format[0]}') 
            BETWEEN STR_TO_DATE('$start_date', '%Y-%m-%d') 
            AND STR_TO_DATE('$end_date', '%Y-%m-%d')";
    
    $result = $conn->query($sql);
    $count = $result ? $result->fetch_assoc()['count'] : 'ERROR';
    
    echo "<p>ফরম্যাট {$format[1]} টেস্ট: <strong>$count</strong> টি ফলাফল</p>";
}

// Step 8: Show results from date range
echo '<h3>৮. তারিখ সীমার সকল ফলাফল:</h3>';

// Create a comprehensive SQL query that handles various date formats
$sql = "SELECT a.*, t.teacher_name, t.designation, t.subject
        FROM duty_assignments a
        JOIN teachers t ON a.teacher_id = t.id
        WHERE 
            duty_date BETWEEN '$start_date' AND '$end_date'
            OR STR_TO_DATE(duty_date, '%Y-%m-%d') BETWEEN '$start_date' AND '$end_date'
            OR STR_TO_DATE(duty_date, '%d-%m-%Y') BETWEEN '$start_date' AND '$end_date'
            OR STR_TO_DATE(duty_date, '%d/%m/%Y') BETWEEN '$start_date' AND '$end_date'
        ORDER BY duty_date
        LIMIT 50";

$result = $conn->query($sql);

if ($result === false) {
    echo "<div style='color:red'>ERROR: " . $conn->error . "</div>";
} elseif ($result->num_rows == 0) {
    echo "<div style='color:orange'>কোন রেজাল্ট পাওয়া যায়নি।</div>";
    
    // Last ditch effort - show ALL data from the table
    echo "<h4>টেবিলে সর্বশেষ ১০ টি ডিউটি অ্যাসাইনমেন্ট:</h4>";
    $all_duties = $conn->query("
        SELECT a.*, t.teacher_name
        FROM duty_assignments a
        JOIN teachers t ON a.teacher_id = t.id
        ORDER BY a.id DESC
        LIMIT 10
    ");
    
    if ($all_duties && $all_duties->num_rows > 0) {
        echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
        echo "<tr style='background:#f0f0f0;'><th>ID</th><th>তারিখ</th><th>শিক্ষকের নাম</th><th>রুম</th></tr>";
        
        while ($row = $all_duties->fetch_assoc()) {
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['duty_date']}</td>";
            echo "<td>{$row['teacher_name']}</td>";
            echo "<td>{$row['room_number']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
} else {
    echo "<table border='1' cellpadding='5' style='border-collapse:collapse;'>";
    echo "<tr style='background:#f0f0f0;'><th>তারিখ</th><th>শিক্ষকের নাম</th><th>রুম</th><th>শিফট</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['duty_date']}</td>";
        echo "<td>{$row['teacher_name']}</td>";
        echo "<td>{$row['room_number']}</td>";
        echo "<td>{$row['duty_shift']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Step 9: Fix suggestions
echo '<h3>৯. সম্ভাব্য সমাধান:</h3>';
echo "<ol>";
echo "<li>ডাটাবেসে তারিখের ফরম্যাট সঠিক আছে কিনা দেখুন</li>";
echo "<li>ডিউটি অ্যাসাইনমেন্ট টেবিলে কোন ডেটা আছে কিনা নিশ্চিত হোন</li>";
echo "<li>টেবিল স্ট্রাকচার সঠিক আছে কিনা যাচাই করুন</li>";
echo "<li>আমাদের কোডের তারিখ ফরম্যাট হ্যান্ডলিং আপডেট করুন</li>";
echo "</ol>";

// Links to other utility pages
echo "<div style='margin-top:20px'>";
echo "<p><a href='teacher_honorarium_calculator.php' style='text-decoration:none;color:#007bff;'>← সন্মানী হিসাব পেজে ফিরে যান</a></p>";
echo "<p><a href='data_query_debug.php' style='text-decoration:none;color:#007bff;'>← ডাটা কুয়েরী ডিবাগ টুল</a></p>";
echo "</div>";
?> 