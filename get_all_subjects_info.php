<?php
header('Content-Type: application/json');
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/models/Student.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get all students
    $stmt = $db->query("SELECT * FROM students");
    $allStudents = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Subject names from subject_filter.php
    $subjectNames = [
        '101' => 'Bangla',
        '107' => 'English',
        '275' => 'Information & Technology',
        '174' => 'Physics',
        '176' => 'Chemistry',
        '178' => 'Biology',
        '265' => 'Higher Math',
        '253' => 'Accounting',
        '292' => 'Finance & Banking',
        '277' => 'Business Organization & Management',
        '286' => 'Production Management & Marketing',
        '109' => 'Economics',
        '121' => 'Logic',
        '117' => 'Sociology',
        '249' => 'Study Of Islam',
        '271' => 'Social Work',
        '273' => 'Home Science',
        '267' => 'Islamic History',
        '269' => 'Civics & Good Governance',
        '304' => 'History',
        '129' => 'Statistics'
    ];
    
    // Get all unique subject codes
    $subjectCodes = [];
    foreach ($allStudents as $student) {
        for ($i = 1; $i <= 13; $i++) {
            $subValue = trim($student["sub_$i"] ?? '');
            if (!empty($subValue)) {
                $subjectCodes[$subValue] = true;
            }
        }
    }
    $subjectCodes = array_keys($subjectCodes);
    sort($subjectCodes);
    
    // Get subject-wise information
    $subjectsInfo = [];
    foreach ($subjectCodes as $code) {
        $subjectStudents = [];
        foreach ($allStudents as $student) {
            for ($i = 1; $i <= 13; $i++) {
                $subValue = trim($student["sub_$i"] ?? '');
                if ($subValue === $code) {
                    $subjectStudents[] = $student;
                    break;
                }
            }
        }
        
        if (!empty($subjectStudents)) {
            $rolls = array_map(function($s) { return (int)$s['roll_no']; }, $subjectStudents);
            $rolls = array_filter($rolls, function($r) { return $r > 0; });
            
            if (!empty($rolls)) {
                sort($rolls);
                $subjectsInfo[] = [
                    'code' => $code,
                    'name' => $subjectNames[$code] ?? 'Unknown Subject',
                    'count' => count($subjectStudents),
                    'min_roll' => min($rolls),
                    'max_roll' => max($rolls),
                    'roll_range' => min($rolls) . ' - ' . max($rolls),
                    'students' => $subjectStudents
                ];
            }
        }
    }
    
    // Sort by subject code
    usort($subjectsInfo, function($a, $b) {
        return (int)$a['code'] - (int)$b['code'];
    });
    
    echo json_encode([
        'success' => true,
        'total_students' => count($allStudents),
        'total_subjects' => count($subjectsInfo),
        'subjects' => $subjectsInfo
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'subjects' => []
    ]);
}
?>
