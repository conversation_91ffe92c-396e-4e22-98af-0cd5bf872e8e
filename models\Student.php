<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../utils/DatabaseHelper.php';

class Student {
    private $db;
    private $table = 'students';
    private $dbHelper;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->dbHelper = new DatabaseHelper($this->db);
    }



    // Bulk insert for Excel upload
    public function bulkInsert($students) {
        $this->db->beginTransaction();

        try {
            $sql = "INSERT INTO {$this->table} (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects)
                    VALUES (:name, :father_name, :gender, :roll, :registration, :department, :academic_year, :student_type, :subjects)";

            $stmt = $this->db->prepare($sql);

            foreach ($students as $student) {
                // Map old field names to new ones
                $mappedData = [
                    'name' => $student['student_name'] ?? $student['name'] ?? '',
                    'father_name' => $student['father_name'] ?? '',
                    'gender' => $student['gender'] ?? 'Male',
                    'roll' => $student['roll_no'] ?? $student['roll'] ?? '',
                    'registration' => $student['reg_no'] ?? $student['registration'] ?? '',
                    'department' => $student['group_name'] ?? $student['department'] ?? '',
                    'academic_year' => $student['session'] ?? $student['academic_year'] ?? '',
                    'student_type' => $student['type'] ?? $student['student_type'] ?? 'Regular'
                ];

                // Handle subjects - combine individual subject fields into subjects field
                $subjects = [];
                for ($i = 1; $i <= 13; $i++) {
                    $subField = "sub_$i";
                    if (!empty($student[$subField])) {
                        $subjects[] = $student[$subField];
                    }
                }
                $mappedData['subjects'] = implode(',', $subjects);

                $stmt->execute($mappedData);
            }

            $this->db->commit();
            return true;
        } catch (PDOException $e) {
            $this->db->rollback();
            throw new Exception("Error in bulk insert: " . $e->getMessage());
        }
    }

    // Get all students
    public function getAll() {
        $sql = "SELECT * FROM {$this->table} ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get student by ID
    public function getById($id) {
        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Search students
    public function search($criteria) {
        $conditions = [];
        $params = [];

        if (!empty($criteria['roll']) || !empty($criteria['roll_no'])) {
            $roll = $criteria['roll'] ?? $criteria['roll_no'];
            $conditions[] = "roll LIKE :roll";
            $params['roll'] = '%' . $roll . '%';
        }

        if (!empty($criteria['name']) || !empty($criteria['student_name'])) {
            $name = $criteria['name'] ?? $criteria['student_name'];
            $conditions[] = "name LIKE :name";
            $params['name'] = '%' . $name . '%';
        }

        if (!empty($criteria['academic_year']) || !empty($criteria['session'])) {
            $year = $criteria['academic_year'] ?? $criteria['session'];
            $conditions[] = "academic_year = :academic_year";
            $params['academic_year'] = $year;
        }

        $sql = "SELECT * FROM {$this->table}";
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(' AND ', $conditions);
        }
        $sql .= " ORDER BY created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }



    // Get students by IDs for confirmation
    public function getByIds($ids) {
        if (empty($ids) || !is_array($ids)) {
            return [];
        }

        try {
            // Sanitize IDs
            $sanitizedIds = array_map('intval', $ids);
            $sanitizedIds = array_filter($sanitizedIds, function($id) {
                return $id > 0;
            });

            if (empty($sanitizedIds)) {
                return [];
            }

            // Create placeholders for prepared statement
            $placeholders = str_repeat('?,', count($sanitizedIds) - 1) . '?';
            $sql = "SELECT * FROM {$this->table} WHERE id IN ($placeholders) " . $this->dbHelper->buildRollOrderBy();

            $stmt = $this->db->prepare($sql);
            $stmt->execute($sanitizedIds);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Get by IDs error: " . $e->getMessage());
            return [];
        }
    }

    // Get total count
    public function getTotalCount() {
        try {
            $sql = "SELECT COUNT(*) FROM {$this->table}";
            $stmt = $this->db->query($sql);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            error_log("Count error: " . $e->getMessage());
            return 0;
        }
    }

    // Get students who have specific subject code in any subject
    public function getStudentsWithSubjectCode($subjectCode) {
        try {
            // Use helper to build subject filter
            $subjectFilter = $this->dbHelper->buildSubjectFilterSQL($subjectCode, $this->table);
            $sql = $subjectFilter['sql'] . " " . $this->dbHelper->buildRollOrderBy();

            $stmt = $this->db->prepare($sql);
            foreach ($subjectFilter['params'] as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }

            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("Get students with subject code error: " . $e->getMessage());
            return [];
        }
    }

    // Get students who have "101" in any subject (backward compatibility)
    public function getStudentsWithSubject101() {
        return $this->getStudentsWithSubjectCode('101');
    }

    // Get students who have "107" in any subject
    public function getStudentsWithSubject107() {
        return $this->getStudentsWithSubjectCode('107');
    }

    // Get students who have "275" in any subject
    public function getStudentsWithSubject275() {
        return $this->getStudentsWithSubjectCode('275');
    }

    // Get students who have "174" in any subject
    public function getStudentsWithSubject174() {
        return $this->getStudentsWithSubjectCode('174');
    }

    // Get students who have "176" in any subject
    public function getStudentsWithSubject176() {
        return $this->getStudentsWithSubjectCode('176');
    }

    // Get students who have "178" in any subject
    public function getStudentsWithSubject178() {
        return $this->getStudentsWithSubjectCode('178');
    }

    // Get students who have "265" in any subject
    public function getStudentsWithSubject265() {
        return $this->getStudentsWithSubjectCode('265');
    }

    // New subject codes methods
    public function getStudentsWithSubject277() {
        return $this->getStudentsWithSubjectCode('277');
    }

    public function getStudentsWithSubject292() {
        return $this->getStudentsWithSubjectCode('292');
    }

    public function getStudentsWithSubject253() {
        return $this->getStudentsWithSubjectCode('253');
    }

    public function getStudentsWithSubject286() {
        return $this->getStudentsWithSubjectCode('286');
    }

    public function getStudentsWithSubject109() {
        return $this->getStudentsWithSubjectCode('109');
    }

    public function getStudentsWithSubject121() {
        return $this->getStudentsWithSubjectCode('121');
    }

    public function getStudentsWithSubject117() {
        return $this->getStudentsWithSubjectCode('117');
    }

    public function getStudentsWithSubject249() {
        return $this->getStudentsWithSubjectCode('249');
    }

    public function getStudentsWithSubject271() {
        return $this->getStudentsWithSubjectCode('271');
    }

    public function getStudentsWithSubject273() {
        return $this->getStudentsWithSubjectCode('273');
    }

    public function getStudentsWithSubject267() {
        return $this->getStudentsWithSubjectCode('267');
    }

    public function getStudentsWithSubject269() {
        return $this->getStudentsWithSubjectCode('269');
    }

    public function getStudentsWithSubject304() {
        return $this->getStudentsWithSubjectCode('304');
    }

    public function getStudentsWithSubject129() {
        return $this->getStudentsWithSubjectCode('129');
    }



    // Create student
    public function create($data) {
        // Map old field names to new ones
        $mappedData = [
            'name' => $data['student_name'] ?? $data['name'] ?? '',
            'father_name' => $data['father_name'] ?? '',
            'gender' => $data['gender'] ?? 'Male',
            'roll' => $data['roll_no'] ?? $data['roll'] ?? '',
            'registration' => $data['reg_no'] ?? $data['registration'] ?? '',
            'department' => $data['group_name'] ?? $data['department'] ?? '',
            'academic_year' => $data['session'] ?? $data['academic_year'] ?? '',
            'student_type' => $data['type'] ?? $data['student_type'] ?? 'Regular'
        ];

        // Handle subjects - combine individual subject fields into subjects field
        $subjects = [];
        for ($i = 1; $i <= 13; $i++) {
            $subField = "sub_$i";
            if (!empty($data[$subField])) {
                $subjects[] = $data[$subField];
            }
        }
        $mappedData['subjects'] = implode(',', $subjects);

        $sql = "INSERT INTO {$this->table} (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects)
                VALUES (:name, :father_name, :gender, :roll, :registration, :department, :academic_year, :student_type, :subjects)";

        try {
            $stmt = $this->db->prepare($sql);
            return $stmt->execute($mappedData);
        } catch (PDOException $e) {
            throw new Exception("Error creating student: " . $e->getMessage());
        }
    }

    // Update student
    public function update($id, $data) {
        // Map old field names to new ones
        $mappedData = [
            'name' => $data['student_name'] ?? $data['name'] ?? '',
            'father_name' => $data['father_name'] ?? '',
            'gender' => $data['gender'] ?? 'Male',
            'roll' => $data['roll_no'] ?? $data['roll'] ?? '',
            'registration' => $data['reg_no'] ?? $data['registration'] ?? '',
            'department' => $data['group_name'] ?? $data['department'] ?? '',
            'academic_year' => $data['session'] ?? $data['academic_year'] ?? '',
            'student_type' => $data['type'] ?? $data['student_type'] ?? 'Regular'
        ];

        // Handle subjects - combine individual subject fields into subjects field
        $subjects = [];
        for ($i = 1; $i <= 13; $i++) {
            $subField = "sub_$i";
            if (!empty($data[$subField])) {
                $subjects[] = $data[$subField];
            }
        }
        $mappedData['subjects'] = implode(',', $subjects);

        $sql = "UPDATE {$this->table} SET
            name = :name, father_name = :father_name, gender = :gender,
            roll = :roll, registration = :registration,
            department = :department, academic_year = :academic_year,
            student_type = :student_type, subjects = :subjects
            WHERE id = :id";

        try {
            $stmt = $this->db->prepare($sql);
            $mappedData['id'] = $id;
            return $stmt->execute($mappedData);
        } catch (PDOException $e) {
            throw new Exception("Error updating student: " . $e->getMessage());
        }
    }

    // Delete student
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE id = :id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }

    // Delete all students
    public function deleteAll() {
        try {
            $sql = "DELETE FROM {$this->table}";
            $stmt = $this->db->prepare($sql);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Delete all error: " . $e->getMessage());
            return false;
        }
    }

    // Delete students with specific subject code
    public function deleteBySubjectCode($subjectCode) {
        try {
            // Use helper to build subject filter for DELETE
            $subjectFilter = $this->dbHelper->buildSubjectFilterSQL($subjectCode, $this->table);
            $sql = str_replace('SELECT * FROM', 'DELETE FROM', $subjectFilter['sql']);

            $stmt = $this->db->prepare($sql);
            foreach ($subjectFilter['params'] as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }

            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Delete by subject code error: " . $e->getMessage());
            return false;
        }
    }

    // Bulk delete selected students by IDs
    public function bulkDelete($ids) {
        if (empty($ids) || !is_array($ids)) {
            return false;
        }

        try {
            // Sanitize IDs to ensure they are integers
            $sanitizedIds = array_map('intval', $ids);
            $sanitizedIds = array_filter($sanitizedIds, function($id) {
                return $id > 0;
            });

            if (empty($sanitizedIds)) {
                return false;
            }

            // Create placeholders for prepared statement
            $placeholders = str_repeat('?,', count($sanitizedIds) - 1) . '?';
            $sql = "DELETE FROM {$this->table} WHERE id IN ($placeholders)";

            $stmt = $this->db->prepare($sql);
            return $stmt->execute($sanitizedIds);
        } catch (PDOException $e) {
            error_log("Bulk delete error: " . $e->getMessage());
            return false;
        }
    }
}
?>
