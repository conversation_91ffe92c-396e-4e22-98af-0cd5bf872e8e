<?php
// Database Migration Script - Complete Fix for CSV Upload Issues
// This script will ensure database is properly set up for CSV uploads

$host = 'localhost';
$username = 'root';
$password = '';
$dbname = 'exmm';

$results = [];
$errors = [];

function logResult($message, $type = 'info') {
    global $results;
    $results[] = ['message' => $message, 'type' => $type];
}

function logError($message) {
    global $errors;
    $errors[] = $message;
    logResult($message, 'error');
}

try {
    // Step 1: Connect to MySQL server
    logResult("🔌 Connecting to MySQL server...", 'info');
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logResult("✅ Connected to MySQL server successfully", 'success');
    
    // Step 2: Create database if not exists
    logResult("🗄️ Checking database existence...", 'info');
    $stmt = $pdo->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbname'");
    if (!$stmt->fetch()) {
        $pdo->exec("CREATE DATABASE `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        logResult("✅ Database '$dbname' created successfully", 'success');
    } else {
        logResult("✅ Database '$dbname' already exists", 'success');
    }
    
    // Step 3: Connect to specific database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logResult("✅ Connected to database '$dbname'", 'success');
    
    // Step 4: Check if students table exists
    logResult("📋 Checking students table...", 'info');
    $stmt = $pdo->query("SHOW TABLES LIKE 'students'");
    $tableExists = $stmt->fetch();
    
    if (!$tableExists) {
        // Create new students table with all required columns
        logResult("🔨 Creating students table...", 'info');
        $createTableSQL = "
            CREATE TABLE `students` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `father_name` varchar(255) DEFAULT NULL,
                `gender` enum('Male','Female','Other') DEFAULT 'Male',
                `roll` varchar(50) NOT NULL,
                `registration` varchar(100) DEFAULT NULL,
                `department` varchar(100) DEFAULT NULL,
                `academic_year` varchar(20) DEFAULT NULL,
                `student_type` enum('Regular','Irregular','Improvement') DEFAULT 'Regular',
                `subjects` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_roll` (`roll`),
                INDEX `idx_department` (`department`),
                INDEX `idx_student_type` (`student_type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        logResult("✅ Students table created successfully", 'success');
        
    } else {
        logResult("✅ Students table exists", 'success');
        
        // Step 5: Check and add missing columns
        logResult("🔍 Checking table columns...", 'info');
        $stmt = $pdo->query("DESCRIBE students");
        $currentColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = [
            'father_name' => "varchar(255) DEFAULT NULL",
            'gender' => "enum('Male','Female','Other') DEFAULT 'Male'"
        ];
        
        foreach ($requiredColumns as $columnName => $columnDefinition) {
            if (!in_array($columnName, $currentColumns)) {
                logResult("➕ Adding missing column: $columnName", 'warning');
                $alterSQL = "ALTER TABLE students ADD COLUMN `$columnName` $columnDefinition";
                $pdo->exec($alterSQL);
                logResult("✅ Added column: $columnName", 'success');
            } else {
                logResult("✅ Column exists: $columnName", 'success');
            }
        }
    }
    
    // Step 6: Test CSV upload functionality
    logResult("🧪 Testing CSV upload functionality...", 'info');
    
    // Test data insertion
    $testData = [
        'name' => 'Migration Test Student',
        'father_name' => 'Test Father',
        'gender' => 'Male',
        'roll' => 'MIGRATE_TEST_001',
        'registration' => 'REG_TEST_001',
        'department' => 'Test Department',
        'academic_year' => '2024',
        'student_type' => 'Regular',
        'subjects' => '101,102,103'
    ];
    
    $sql = "INSERT INTO students (name, father_name, gender, roll, registration, department, academic_year, student_type, subjects)
            VALUES (:name, :father_name, :gender, :roll, :registration, :department, :academic_year, :student_type, :subjects)";
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($testData);
    
    if ($result) {
        logResult("✅ Test insertion successful", 'success');
        
        // Clean up test data
        $pdo->exec("DELETE FROM students WHERE roll = 'MIGRATE_TEST_001'");
        logResult("🧹 Test data cleaned up", 'info');
    }
    
    // Step 7: Create uploads directory
    logResult("📁 Checking uploads directory...", 'info');
    $uploadsDir = __DIR__ . '/uploads';
    if (!is_dir($uploadsDir)) {
        if (mkdir($uploadsDir, 0777, true)) {
            logResult("✅ Created uploads directory", 'success');
        } else {
            logError("❌ Failed to create uploads directory");
        }
    } else {
        if (is_writable($uploadsDir)) {
            logResult("✅ Uploads directory exists and is writable", 'success');
        } else {
            if (chmod($uploadsDir, 0777)) {
                logResult("✅ Fixed uploads directory permissions", 'success');
            } else {
                logError("❌ Uploads directory is not writable");
            }
        }
    }
    
    logResult("🎉 Database migration completed successfully!", 'success');
    
} catch (PDOException $e) {
    logError("❌ Database error: " . $e->getMessage());
} catch (Exception $e) {
    logError("❌ General error: " . $e->getMessage());
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .result-item {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .result-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .result-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .result-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .result-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Database Migration Results
                        </h3>
                        <small>CSV Upload সমস্যার সম্পূর্ণ সমাধান</small>
                    </div>
                    <div class="card-body">
                        
                        <?php if (empty($errors)): ?>
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Migration Successful!</h5>
                                <p class="mb-0">Database has been successfully configured for CSV uploads.</p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Migration Issues Found</h5>
                                <p class="mb-0">Some issues were encountered during migration.</p>
                            </div>
                        <?php endif; ?>
                        
                        <h5><i class="fas fa-list me-2"></i>Migration Steps:</h5>
                        <div class="migration-results">
                            <?php foreach ($results as $result): ?>
                                <div class="result-item result-<?php echo $result['type']; ?>">
                                    <?php echo htmlspecialchars($result['message']); ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <hr>
                        
                        <h5><i class="fas fa-tools me-2"></i>Next Steps:</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <a href="csv_upload.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-upload me-2"></i>Test CSV Upload
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="system_startup_check.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-check-circle me-2"></i>System Check
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="index.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-home me-2"></i>Go to Home
                                </a>
                            </div>
                        </div>
                        
                        <?php if (empty($errors)): ?>
                            <div class="alert alert-info mt-3">
                                <h6><i class="fas fa-info-circle me-2"></i>Success! Your system is now ready:</h6>
                                <ul class="mb-0">
                                    <li>✅ Database properly configured</li>
                                    <li>✅ All required columns exist</li>
                                    <li>✅ CSV upload functionality tested</li>
                                    <li>✅ File permissions set correctly</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
