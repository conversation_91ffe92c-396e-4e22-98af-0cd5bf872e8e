<?php
// This script is designed to be run once to update the database schema.
// It adds the 'duty_shift' column to the 'duty_assignments' table if it doesn't already exist.

require_once 'includes/teacher_db.php'; // This connects to the database

echo "<!DOCTYPE html><html lang='en'><head><meta charset='UTF-8'><title>Database Schema Fix</title>";
echo "<style>body { font-family: sans-serif; padding: 20px; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>";
echo "</head><body>";

echo "<h1>Database Schema Update</h1>";

try {
    // Database name is 'exmm' based on the project files
    $dbname = 'exmm';
    $tableName = 'duty_assignments';
    $columnName = 'duty_shift';

    // Check if the column already exists
    $stmt = $pdo->prepare("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = :dbname 
        AND TABLE_NAME = :tableName 
        AND COLUMN_NAME = :columnName
    ");
    $stmt->execute([':dbname' => $dbname, ':tableName' => $tableName, ':columnName' => $columnName]);
    $columnExists = $stmt->fetchColumn();

    if ($columnExists) {
        echo "<p class='info'>The '{$columnName}' column already exists in the '{$tableName}' table. No changes were needed.</p>";
    } else {
        echo "<p>The '{$columnName}' column was not found. Attempting to add it...</p>";
        
        // The SQL command to add the new column
        $sql = "
            ALTER TABLE `{$tableName}` 
            ADD COLUMN `{$columnName}` ENUM('Morning', 'Afternoon') NOT NULL DEFAULT 'Morning' AFTER `room_number`;
        ";

        // Execute the SQL command
        $pdo->exec($sql);

        echo "<p class='success'>Success! The '{$tableName}' table has been updated with the '{$columnName}' column.</p>";
    }

    echo "<p>The database issue should now be resolved.</p>";
    echo "<a href='duty_assignments_management.php'>Click here to go back to the Duty Management page.</a>";
    echo "<p style='margin-top: 20px; color: #555;'>For security, it is safe to delete this file (<code>fix_database_schema.php</code>) now.</p>";

} catch (PDOException $e) {
    // Handle any errors
    echo "<p class='error'>An error occurred while updating the database schema: " . $e->getMessage() . "</p>";
    echo "<p>Please ensure your database server is running and the connection details in `includes/teacher_db.php` are correct.</p>";
}

echo "</body></html>";
?> 