<?php
session_start();
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

// Handle signature upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['upload_signature'])) {
    $signatureType = $_POST['signature_type'];
    
    if (isset($_FILES['signature_file']) && $_FILES['signature_file']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'uploads/signatures/';
        
        // Create directory if it doesn't exist
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = $_FILES['signature_file']['name'];
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        
        if (in_array($fileExtension, $allowedExtensions)) {
            $newFileName = $signatureType . '_' . time() . '.' . $fileExtension;
            $uploadPath = $uploadDir . $newFileName;
            
            if (move_uploaded_file($_FILES['signature_file']['tmp_name'], $uploadPath)) {
                try {
                    $teacherManager->uploadSignature($signatureType, $uploadPath, $fileName);
                    $message = 'স্বাক্ষর সফলভাবে আপলোড হয়েছে!';
                    $messageType = 'success';
                } catch (Exception $e) {
                    $message = 'স্বাক্ষর সংরক্ষণে সমস্যা হয়েছে: ' . $e->getMessage();
                    $messageType = 'danger';
                }
            } else {
                $message = 'ফাইল আপলোডে সমস্যা হয়েছে!';
                $messageType = 'danger';
            }
        } else {
            $message = 'শুধুমাত্র JPG, JPEG, PNG, GIF ফাইল আপলোড করা যাবে!';
            $messageType = 'warning';
        }
    } else {
        $message = 'ফাইল নির্বাচন করুন!';
        $messageType = 'warning';
    }
}

// Get current signatures
$principalSignature = $teacherManager->getActiveSignature('principal');
$convenerSignature = $teacherManager->getActiveSignature('convener');

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['convener_name'] ?? '';
    $signature = $_FILES['signature'] ?? null;

    if ($name && $signature && $signature['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($signature['name'], PATHINFO_EXTENSION);
        $target = "uploads/signature." . $ext;
        if (move_uploaded_file($signature['tmp_name'], $target)) {
            // Save name and signature path (you can save to DB or a file)
            file_put_contents('uploads/convener_name.txt', $name);
            $success = "স্বাক্ষর ও নাম সফলভাবে সংরক্ষণ হয়েছে!";
        } else {
            $error = "স্বাক্ষর আপলোড ব্যর্থ হয়েছে!";
        }
    } else {
        $error = "সব তথ্য দিন এবং সঠিক ফাইল নির্বাচন করুন!";
    }
}

// Load existing name and signature if available
$saved_name = file_exists('uploads/convener_name.txt') ? file_get_contents('uploads/convener_name.txt') : '';
$saved_signature = '';
foreach (['png', 'jpg', 'jpeg'] as $ext) {
    if (file_exists("uploads/signature.$ext")) {
        $saved_signature = "uploads/signature.$ext";
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্বাক্ষর আপলোড - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .signature-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .signature-preview {
            max-width: 200px;
            max-height: 100px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 10px;
        }
        
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e9ecef;
            border-color: #0056b3;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .preview-signature { max-width: 200px; margin-bottom: 10px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">
                        <i class="fas fa-signature me-3"></i>স্বাক্ষর আপলোড ব্যবস্থাপনা
                    </h1>
                    <p class="text-white-50">প্রিন্সিপাল ও কনভেনার এর স্বাক্ষর আপলোড করুন</p>
                </div>

                <!-- Message Display -->
                <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                        <strong><?php echo $message; ?></strong>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Navigation -->
                <div class="text-center mb-4">
                    <a href="index.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-home me-2"></i>হোম
                    </a>
                    <a href="teacher_duty_management.php" class="btn btn-outline-light me-2">
                        <i class="fas fa-users me-2"></i>শিক্ষক ব্যবস্থাপনা
                    </a>
                    <a href="duty_letter_generator.php" class="btn btn-outline-light">
                        <i class="fas fa-file-alt me-2"></i>ডিউটি লেটার
                    </a>
                </div>

                <div class="row">
                    <!-- Principal Signature -->
                    <div class="col-md-6">
                        <div class="signature-card">
                            <h4 class="text-center mb-4">
                                <i class="fas fa-user-tie text-primary me-2"></i>প্রিন্সিপাল স্বাক্ষর
                            </h4>
                            
                            <!-- Current Signature Display -->
                            <?php if ($principalSignature): ?>
                                <div class="text-center mb-4">
                                    <h6>বর্তমান স্বাক্ষর:</h6>
                                    <img src="<?php echo htmlspecialchars($principalSignature['signature_path']); ?>" 
                                         alt="Principal Signature" class="signature-preview">
                                    <p class="small text-muted mt-2">
                                        আপলোড: <?php echo date('d/m/Y H:i', strtotime($principalSignature['uploaded_at'])); ?>
                                    </p>
                                </div>
                            <?php else: ?>
                                <div class="text-center mb-4">
                                    <div class="signature-preview mx-auto d-flex align-items-center justify-content-center">
                                        <span class="text-muted">কোন স্বাক্ষর নেই</span>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Upload Form -->
                            <form method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="signature_type" value="principal">
                                <div class="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                    <h6>নতুন স্বাক্ষর আপলোড করুন</h6>
                                    <input type="file" name="signature_file" class="form-control mt-3" 
                                           accept="image/*" required>
                                    <small class="text-muted">JPG, JPEG, PNG, GIF (সর্বোচ্চ 2MB)</small>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="submit" name="upload_signature" class="btn btn-custom">
                                        <i class="fas fa-upload me-2"></i>আপলোড করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Convener Signature -->
                    <div class="col-md-6">
                        <div class="signature-card">
                            <h4 class="text-center mb-4">
                                <i class="fas fa-user-check text-success me-2"></i>কনভেনার স্বাক্ষর
                            </h4>
                            
                            <!-- Current Signature Display -->
                            <?php if ($convenerSignature): ?>
                                <div class="text-center mb-4">
                                    <h6>বর্তমান স্বাক্ষর:</h6>
                                    <img src="<?php echo htmlspecialchars($convenerSignature['signature_path']); ?>" 
                                         alt="Convener Signature" class="signature-preview">
                                    <p class="small text-muted mt-2">
                                        আপলোড: <?php echo date('d/m/Y H:i', strtotime($convenerSignature['uploaded_at'])); ?>
                                    </p>
                                </div>
                            <?php else: ?>
                                <div class="text-center mb-4">
                                    <div class="signature-preview mx-auto d-flex align-items-center justify-content-center">
                                        <span class="text-muted">কোন স্বাক্ষর নেই</span>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Upload Form -->
                            <form method="POST" enctype="multipart/form-data">
                                <input type="hidden" name="signature_type" value="convener">
                                <div class="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                                    <h6>নতুন স্বাক্ষর আপলোড করুন</h6>
                                    <input type="file" name="signature_file" class="form-control mt-3" 
                                           accept="image/*" required>
                                    <small class="text-muted">JPG, JPEG, PNG, GIF (সর্বোচ্চ 2MB)</small>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="submit" name="upload_signature" class="btn btn-success">
                                        <i class="fas fa-upload me-2"></i>আপলোড করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="signature-card">
                    <h5 class="text-center mb-4">
                        <i class="fas fa-info-circle text-info me-2"></i>নির্দেশনা
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>স্বাক্ষরের ছবি পরিষ্কার ও স্পষ্ট হতে হবে</li>
                                <li><i class="fas fa-check text-success me-2"></i>ব্যাকগ্রাউন্ড সাদা বা স্বচ্ছ হলে ভাল</li>
                                <li><i class="fas fa-check text-success me-2"></i>ফাইলের সাইজ ২MB এর কম হতে হবে</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>JPG, JPEG, PNG, GIF ফরম্যাট সাপোর্ট করে</li>
                                <li><i class="fas fa-check text-success me-2"></i>নতুন স্বাক্ষর আপলোড করলে পুরানো স্বাক্ষর প্রতিস্থাপিত হবে</li>
                                <li><i class="fas fa-check text-success me-2"></i>ডিউটি লেটারে স্বয়ংক্রিয়ভাবে যুক্ত হবে</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Convener Name and Signature Customization -->
                <div class="signature-card">
                    <h5 class="text-center mb-4">
                        <i class="fas fa-user-edit text-info me-2"></i>আহবায়কের স্বাক্ষর ও নাম কাস্টমাইজেশন
                    </h5>
                    <?php if ($success): ?><div class="success"><?= $success ?></div><?php endif; ?>
                    <?php if ($error): ?><div class="error"><?= $error ?></div><?php endif; ?>

                    <form method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label class="form-label">আহবায়কের নাম:</label>
                            <input type="text" name="convener_name" class="form-control" value="<?= htmlspecialchars($saved_name) ?>" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">স্বাক্ষর (ছবি):</label>
                            <input type="file" name="signature" class="form-control" accept="image/*" required>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-save me-2"></i>সংরক্ষণ করুন
                            </button>
                        </div>
                    </form>

                    <?php if ($saved_signature): ?>
                        <h6 class="mt-4">বর্তমান স্বাক্ষর ও নাম:</h6>
                        <img src="<?= $saved_signature ?>" class="preview-signature"><br>
                        <b><?= htmlspecialchars($saved_name) ?></b>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
