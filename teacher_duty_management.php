<?php
session_start();
require_once 'includes/teacher_db.php';

// Initialize variables
$message = '';
$messageType = '';
$dutyAssignments = [];

// Get teachers from database
$uploadedTeachers = $teacherManager->getAllTeachers();

// Add sorting logic for serial
$sortOrder = isset($_GET['sort']) && $_GET['sort'] === 'desc' ? 'desc' : 'asc';
$sortedTeachers = $uploadedTeachers;
if ($sortOrder === 'asc') {
    usort($sortedTeachers, function($a, $b) {
        return ($a['sl_number'] ?? 0) <=> ($b['sl_number'] ?? 0);
    });
} else {
    usort($sortedTeachers, function($a, $b) {
        return ($b['sl_number'] ?? 0) <=> ($a['sl_number'] ?? 0);
    });
}

// Handle CSV upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    try {
        $file = $_FILES['csv_file'];

        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('ফাইল আপলোড এরর হয়েছে।');
        }

        // Check file extension
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if ($fileExtension !== 'csv') {
            throw new Exception('শুধুমাত্র CSV ফাইল আপলোড করুন।');
        }

        // Process CSV file with UTF-8 encoding
        if (($handle = fopen($file['tmp_name'], 'r')) !== FALSE) {
            // Set internal encoding to UTF-8
            mb_internal_encoding('UTF-8');
            $header = fgetcsv($handle); // Skip header
            $teachers = [];

            while (($row = fgetcsv($handle)) !== FALSE) {
                if (!empty($row[0])) { // Assuming first column is SL
                    $teachers[] = [
                        'sl' => trim($row[0]),
                        'name' => isset($row[1]) ? trim($row[1]) : '',
                        'mobile' => isset($row[2]) ? trim($row[2]) : '',
                        'subject' => isset($row[3]) ? trim($row[3]) : '',
                        'designation' => isset($row[4]) ? trim($row[4]) : '',
                        'college' => isset($row[5]) ? trim($row[5]) : '',
                        'duty_status' => 'সাধারন' // Default status
                    ];
                }
            }
            fclose($handle);

            // Save to database using bulk insert
            $teacherManager->bulkInsertTeachers($teachers);

            // Refresh teachers list from database
            $uploadedTeachers = $teacherManager->getAllTeachers();
            $message = count($teachers) . ' জন শিক্ষকের তথ্য সফলভাবে ডাটাবেসে সংরক্ষিত হয়েছে!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'danger';
    }
}

// Get uploaded teachers from session
if (empty($uploadedTeachers) && isset($_SESSION['uploaded_teachers'])) {
    $uploadedTeachers = $_SESSION['uploaded_teachers'];
}

// Handle teacher edit
if (isset($_POST['edit_teacher'])) {
    try {
        $teacherId = $_POST['teacher_id'];
        $teacher = $teacherManager->getTeacherById($teacherId);

        if ($teacher) {
            $teacherData = [
                'sl_number' => $_POST['teacher_sl'],
                'name' => $_POST['teacher_name'],
                'mobile' => $_POST['teacher_mobile'],
                'subject' => $_POST['teacher_subject'],
                'designation' => $_POST['teacher_designation'],
                'college' => $_POST['teacher_college'],
                'duty_status' => $teacher['duty_status'],
                'photo' => $teacher['photo']
            ];

            // Handle photo upload if new photo is provided
            if (isset($_FILES['teacher_photo']) && $_FILES['teacher_photo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/teachers/';
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                $fileExtension = strtolower(pathinfo($_FILES['teacher_photo']['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];

                if (in_array($fileExtension, $allowedExtensions)) {
                    // Delete old photo if exists
                    if (!empty($teacherData['photo']) && file_exists($teacherData['photo'])) {
                        unlink($teacherData['photo']);
                    }

                    $fileName = 'teacher_' . time() . '_' . uniqid() . '.' . $fileExtension;
                    $uploadPath = $uploadDir . $fileName;

                    if (move_uploaded_file($_FILES['teacher_photo']['tmp_name'], $uploadPath)) {
                        $teacherData['photo'] = $uploadPath;
                    }
                }
            }

            // Update in database
            $teacherManager->updateTeacher($teacherId, $teacherData);

            // Refresh teachers list from database
            $uploadedTeachers = $teacherManager->getAllTeachers();
            $message = 'শিক্ষকের তথ্য সফলভাবে ডাটাবেসে আপডেট হয়েছে!';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = 'শিক্ষকের তথ্য আপডেট করতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle teacher delete
if (isset($_POST['delete_teacher'])) {
    try {
        $teacherId = $_POST['teacher_id'];

        // Delete from database (this will also handle photo deletion)
        $teacherManager->deleteTeacher($teacherId);

        // Refresh teachers list from database
        $uploadedTeachers = $teacherManager->getAllTeachers();
        $message = 'শিক্ষক সফলভাবে ডাটাবেস থেকে মুছে ফেলা হয়েছে!';
        $messageType = 'info';
    } catch (Exception $e) {
        $message = 'শিক্ষক মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle bulk delete teachers
if (isset($_POST['bulk_delete_teachers'])) {
    try {
        $teacherIds = $_POST['teacher_ids'] ?? [];

        if (empty($teacherIds)) {
            $message = 'কোন শিক্ষক নির্বাচিত নেই।';
            $messageType = 'warning';
        } else {
            // Delete selected teachers from database
            $teacherManager->bulkDeleteTeachers($teacherIds);

            // Refresh teachers list from database
            $uploadedTeachers = $teacherManager->getAllTeachers();
            $count = count($teacherIds);
            $message = "{$count} জন শিক্ষক সফলভাবে ডাটাবেস থেকে মুছে ফেলা হয়েছে!";
            $messageType = 'info';
        }
    } catch (Exception $e) {
        $message = 'শিক্ষক মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle delete all teachers
if (isset($_POST['delete_all_teachers'])) {
    try {
        if (isset($_POST['confirm_delete_all']) && $_POST['confirm_delete_all'] === 'yes') {
            // Delete all teachers from database
            $teacherManager->deleteAllTeachers();

            // Refresh teachers list from database
            $uploadedTeachers = $teacherManager->getAllTeachers();
            $message = 'সকল শিক্ষক সফলভাবে ডাটাবেস থেকে মুছে ফেলা হয়েছে!';
            $messageType = 'info';
        } else {
            $message = 'সকল শিক্ষক মুছে ফেলার জন্য নিশ্চিতকরণ প্রয়োজন।';
            $messageType = 'warning';
        }
    } catch (Exception $e) {
        $message = 'সকল শিক্ষক মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle teacher status update
if (isset($_POST['update_status'])) {
    try {
        $teacherId = $_POST['teacher_id'];
        $newStatus = $_POST['duty_status'];

        // Update status in database
        $teacherManager->updateDutyStatus($teacherId, $newStatus);

        // Refresh teachers list from database
        $uploadedTeachers = $teacherManager->getAllTeachers();
        $message = 'শিক্ষকের ডিউটি স্ট্যাটাস ডাটাবেসে আপডেট হয়েছে!';
        $messageType = 'success';
    } catch (Exception $e) {
        $message = 'স্ট্যাটাস আপডেট করতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle add teacher
if (isset($_POST['add_teacher'])) {
    try {
        $teacherData = [
            'sl_number' => $_POST['teacher_sl'],
            'name' => $_POST['teacher_name'],
            'mobile' => $_POST['teacher_mobile'],
            'subject' => $_POST['teacher_subject'],
            'designation' => $_POST['teacher_designation'],
            'college' => $_POST['teacher_college'],
            'duty_status' => 'সাধারন',
            'photo' => ''
        ];

        // Handle photo upload
        if (isset($_FILES['teacher_photo']) && $_FILES['teacher_photo']['error'] === UPLOAD_ERR_OK) {
            $uploadDir = 'uploads/teachers/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $fileExtension = strtolower(pathinfo($_FILES['teacher_photo']['name'], PATHINFO_EXTENSION));
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif'];

            if (in_array($fileExtension, $allowedExtensions)) {
                $fileName = 'teacher_' . time() . '_' . uniqid() . '.' . $fileExtension;
                $uploadPath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['teacher_photo']['tmp_name'], $uploadPath)) {
                    $teacherData['photo'] = $uploadPath;
                }
            }
        }

        // Save to database
        $teacherManager->addTeacher($teacherData);

        // Refresh teachers list from database
        $uploadedTeachers = $teacherManager->getAllTeachers();

        $message = 'নতুন শিক্ষক সফলভাবে ডাটাবেসে সংরক্ষিত হয়েছে!';
        $messageType = 'success';
    } catch (Exception $e) {
        $message = 'শিক্ষক যুক্ত করতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Handle duty assignment
if (isset($_POST['assign_duty'])) {
    $dutyDate = $_POST['duty_date'];
    $selectedTeachers = $_POST['selected_teachers'] ?? [];

    if (!empty($dutyDate) && !empty($selectedTeachers)) {
        try {
            $teacherManager->assignDuty($dutyDate, $selectedTeachers);

            // Update session data for room-wise assignment compatibility
            if (!isset($_SESSION['duty_assignments'])) {
                $_SESSION['duty_assignments'] = [];
            }

            // Get all teachers from database to ensure we have the latest data
            $allTeachers = $teacherManager->getAllTeachers();

            // Update uploaded_teachers session with database data
            $_SESSION['uploaded_teachers'] = [];
            foreach ($allTeachers as $index => $teacher) {
                $_SESSION['uploaded_teachers'][$index] = $teacher;
            }

            // Store teacher indices in session for room assignment page
            $teacherIndices = [];
            foreach ($selectedTeachers as $teacherId) {
                // Find teacher index in uploaded teachers array
                foreach ($_SESSION['uploaded_teachers'] as $index => $teacher) {
                    if (isset($teacher['id']) && $teacher['id'] == $teacherId) {
                        $teacherIndices[] = $index;
                        break;
                    }
                }
            }
            $_SESSION['duty_assignments'][$dutyDate] = $teacherIndices;

            $message = 'ডিউটি সফলভাবে বন্টন করা হয়েছে!';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'ডিউটি বন্টনে সমস্যা হয়েছে: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'তারিখ এবং শিক্ষক নির্বাচন করুন!';
        $messageType = 'warning';
    }
}

// Get duty assignments from database
$dutyDates = $teacherManager->getAllDutyDates();
$dutyAssignments = [];
foreach ($dutyDates as $date) {
    $assignments = $teacherManager->getDutyAssignments($date);
    if (!empty($assignments)) {
        $dutyAssignments[$date] = array_column($assignments, 'teacher_id');
    }
}

// Remove duty assignment
if (isset($_POST['remove_duty'])) {
    $dateToRemove = $_POST['date_to_remove'];
    try {
        $teacherManager->removeDutyAssignment($dateToRemove);

        // Remove from session as well
        if (isset($_SESSION['duty_assignments'][$dateToRemove])) {
            unset($_SESSION['duty_assignments'][$dateToRemove]);
        }
        if (isset($_SESSION['room_assignments'][$dateToRemove])) {
            unset($_SESSION['room_assignments'][$dateToRemove]);
        }

        // Refresh duty assignments
        $dutyDates = $teacherManager->getAllDutyDates();
        $dutyAssignments = [];
        foreach ($dutyDates as $date) {
            $assignments = $teacherManager->getDutyAssignments($date);
            if (!empty($assignments)) {
                $dutyAssignments[$date] = array_column($assignments, 'teacher_id');
            }
        }
        $message = 'ডিউটি বন্টন মুছে ফেলা হয়েছে!';
        $messageType = 'info';
    } catch (Exception $e) {
        $message = 'ডিউটি মুছতে সমস্যা হয়েছে: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক ডিউটি ব্যবস্থাপনা - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .main-content {
            margin-left: 0;
            padding: 20px;
        }
        .management-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 600;
        }
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            background: #e9ecef;
        }
        .upload-area.dragover {
            border-color: #495057;
            background: #e9ecef;
        }
        .teacher-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .teacher-card:hover {
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-normal { background: #e3f2fd; color: #1976d2; }
        .status-always { background: #e8f5e8; color: #2e7d32; }
        .status-never { background: #ffebee; color: #c62828; }
        .duty-date-card {
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            background: white;
        }
        .teacher-selection {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: #f8f9fa;
        }
        .selected-teacher {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .photo-upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .photo-upload-area:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .upload-placeholder {
            padding: 20px;
        }
        .photo-preview {
            text-align: center;
        }
        .nav-tabs .nav-link {
            color: #495057;
            border: 1px solid transparent;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .teacher-photo {
            width: 60px;
            height: 60px;
            flex-shrink: 0;
        }
        .teacher-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        .teacher-placeholder {
            width: 60px;
            height: 60px;
            background: #f8f9fa;
            border: 2px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 24px;
        }
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .management-card { box-shadow: none !important; border: 1px solid #ddd !important; }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="management-card">
                        <div class="text-center">
                            <h1><i class="fas fa-chalkboard-teacher text-primary"></i> শিক্ষক ডিউটি ব্যবস্থাপনা</h1>
                            <p class="text-muted mb-0">CSV আপলোড, ডিউটি স্ট্যাটাস ও তারিখ ভিত্তিক ডিউটি বন্টন</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Step 1: Add Teacher or CSV Upload -->
            <div class="management-card">
                <div class="section-header">
                    <h4><i class="fas fa-user-plus me-2"></i> ধাপ ১: শিক্ষক যুক্ত করুন</h4>
                </div>

                <!-- Tab Navigation -->
                <ul class="nav nav-tabs mb-3" id="teacherTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="add-teacher-tab" data-bs-toggle="tab" data-bs-target="#add-teacher" type="button" role="tab">
                            <i class="fas fa-user-plus me-2"></i>একজন শিক্ষক যুক্ত করুন
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="csv-upload-tab" data-bs-toggle="tab" data-bs-target="#csv-upload" type="button" role="tab">
                            <i class="fas fa-upload me-2"></i>CSV ফাইল আপলোড করুন
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content" id="teacherTabsContent">
                    <!-- Add Single Teacher Tab -->
                    <div class="tab-pane fade show active" id="add-teacher" role="tabpanel">
                        <form method="POST" enctype="multipart/form-data" id="addTeacherForm" accept-charset="utf-8">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">ক্রমিক নং</label>
                                        <input type="number" class="form-control" name="teacher_sl" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">শিক্ষকের নাম</label>
                                        <input type="text" class="form-control" name="teacher_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">মোবাইল নম্বর</label>
                                        <input type="text" class="form-control" name="teacher_mobile" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">বিষয়</label>
                                        <input type="text" class="form-control" name="teacher_subject" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">পদবী</label>
                                        <input type="text" class="form-control" name="teacher_designation" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">কলেজ</label>
                                        <input type="text" class="form-control" name="teacher_college" value="আব্দুল ওদুদ শাহ ডিগ্রি কলেজ" required>
                                    </div>
                                </div>
                            </div>

                            <!-- Photo Upload Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">শিক্ষকের ছবি (ঐচ্ছিক)</label>
                                        <div class="photo-upload-area" id="photoUploadArea">
                                            <input type="file" class="form-control" name="teacher_photo" id="teacherPhoto" accept="image/*" style="display: none;">
                                            <div class="upload-placeholder" onclick="document.getElementById('teacherPhoto').click()">
                                                <i class="fas fa-camera fa-3x text-muted mb-2"></i>
                                                <p class="text-muted">ছবি আপলোড করতে ক্লিক করুন</p>
                                                <small class="text-muted">JPG, PNG, GIF ফরম্যাট সাপোর্ট করে</small>
                                            </div>
                                            <div class="photo-preview" id="photoPreview" style="display: none;">
                                                <img id="previewImage" src="" alt="Preview" style="max-width: 150px; max-height: 150px; border-radius: 10px;">
                                                <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removePhoto()">
                                                    <i class="fas fa-trash me-1"></i>ছবি মুছুন
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-end">
                                <button type="submit" name="add_teacher" class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>শিক্ষক যুক্ত করুন
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- CSV Upload Tab -->
                    <div class="tab-pane fade" id="csv-upload" role="tabpanel">

                        <form method="POST" enctype="multipart/form-data" id="uploadForm" accept-charset="utf-8">
                            <div class="upload-area" id="uploadArea">
                                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                                <h5>CSV ফাইল এখানে ড্রাগ করুন অথবা ক্লিক করে নির্বাচন করুন</h5>
                                <p class="text-muted">ফাইল ফরম্যাট: SL, Teachers Name, Mobile, Subject, Designation, College</p>
                                <input type="file" name="csv_file" id="csvFile" accept=".csv" style="display: none;" required>
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('csvFile').click()">
                                    <i class="fas fa-file-csv me-2"></i> ফাইল নির্বাচন করুন
                                </button>
                            </div>

                            <div id="fileInfo" style="display: none;" class="mt-3">
                                <div class="alert alert-info">
                                    <strong>নির্বাচিত ফাইল:</strong> <span id="fileName"></span><br>
                                    <strong>ফাইল সাইজ:</strong> <span id="fileSize"></span>
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-upload me-2"></i> আপলোড করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (!empty($uploadedTeachers)): ?>
                    <div class="mt-4">
                        <h6><i class="fas fa-check-circle text-success me-2"></i> আপলোড সম্পন্ন: <?php echo count($uploadedTeachers); ?> জন শিক্ষক</h6>
                        <div class="table-responsive" style="max-height: 200px; overflow-y: auto;">
                            <table class="table table-sm table-striped">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>ক্রমিক</th>
                                        <th>নাম</th>
                                        <th>মোবাইল</th>
                                        <th>বিষয়</th>
                                        <th>পদবী</th>
                                        <th>কলেজ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($uploadedTeachers, 0, 10) as $teacher): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($teacher['sl_number'] ?? $teacher['sl'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['mobile']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                            <td><?php echo htmlspecialchars($teacher['college']); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                    <?php if (count($uploadedTeachers) > 10): ?>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted">
                                                ... আরো <?php echo count($uploadedTeachers) - 10; ?> জন শিক্ষক
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Step 2: Teacher Status Management -->
            <?php if (!empty($uploadedTeachers)): ?>
                <div class="management-card">
                    <div class="section-header d-flex justify-content-between align-items-center flex-wrap">
                        <h4 class="mb-0"><i class="fas fa-users-cog me-2"></i> ধাপ ২: শিক্ষকদের ডিউটি স্ট্যাটাস নির্ধারণ</h4>
                        <div class="d-flex align-items-center gap-2 flex-wrap">
                            <div class="btn-group me-2" role="group" aria-label="View Switcher">
                                <button type="button" class="btn btn-outline-primary active" id="cardViewBtn" onclick="switchView('card')"><i class="fas fa-th-large me-1"></i> কার্ড</button>
                                <button type="button" class="btn btn-outline-primary" id="tableViewBtn" onclick="switchView('table')"><i class="fas fa-table me-1"></i> টেবিল</button>
                                <button type="button" class="btn btn-outline-primary" id="listViewBtn" onclick="switchView('list')"><i class="fas fa-list me-1"></i> লিস্ট</button>
                            </div>
                            <form method="get" class="d-inline">
                                <select name="sort" class="form-select form-select-sm" onchange="this.form.submit()" style="min-width:120px;">
                                    <option value="asc" <?php if($sortOrder==='asc') echo 'selected'; ?>>ক্রমিক: ASC</option>
                                    <option value="desc" <?php if($sortOrder==='desc') echo 'selected'; ?>>ক্রমিক: DESC</option>
                                </select>
                            </form>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>ডিউটি স্ট্যাটাস:</strong><br>
                                <span class="status-badge status-normal me-2">সাধারন</span> - নিয়মিত ডিউটি<br>
                                <span class="status-badge status-always me-2">সম সময় অন্তর্ভুক্ত</span> - সব ডিউটিতে অন্তর্ভুক্ত<br>
                                <span class="status-badge status-never me-2">কখনো অন্তর্ভুক্ত নয়</span> - কোনো ডিউটিতে অন্তর্ভুক্ত নয়
                            </div>
                        </div>
                    </div>
                    <!-- Card View -->
                    <div id="cardView" class="row" style="">
                        <h5 class="mb-3">শিক্ষক তালিকা (কার্ড ভিউ)</h5>
                        <?php foreach ($sortedTeachers as $i => $teacher): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="teacher-card">
                                    <div class="d-flex align-items-start mb-2">
                                        <div class="teacher-photo me-3">
                                            <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" class="teacher-img">
                                            <?php else: ?>
                                                <div class="teacher-placeholder">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-1">#<?php echo $i+1; ?>. <?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                <span class="status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal'); ?>">
                                                    <?php echo $teacher['duty_status']; ?>
                                                </span>
                                            </div>
                                            <p class="text-muted small mb-2">
                                                <i class="fas fa-book me-1"></i> <?php echo htmlspecialchars($teacher['subject']); ?><br>
                                                <i class="fas fa-id-badge me-1"></i> <?php echo htmlspecialchars($teacher['designation']); ?><br>
                                                <i class="fas fa-phone me-1"></i> <?php echo htmlspecialchars($teacher['mobile']); ?><br>
                                                <i class="fas fa-hashtag me-1"></i> SL: <?php echo htmlspecialchars($teacher['sl_number'] ?? 'N/A'); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <form method="POST" class="d-inline mb-2">
                                        <input type="hidden" name="teacher_id" value="<?php echo $teacher['id'] ?? ''; ?>">
                                        <select name="duty_status" class="form-select form-select-sm" onchange="this.form.submit()">
                                            <option value="সাধারন" <?php echo ($teacher['duty_status'] === 'সাধারন') ? 'selected' : ''; ?>>সাধারন</option>
                                            <option value="সম সময় অন্তর্ভুক্ত" <?php echo ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') ? 'selected' : ''; ?>>সম সময় অন্তর্ভুক্ত</option>
                                            <option value="কখনো অন্তর্ভুক্ত নয়" <?php echo ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়') ? 'selected' : ''; ?>>কখনো অন্তর্ভুক্ত নয়</option>
                                        </select>
                                        <input type="hidden" name="update_status" value="1">
                                    </form>
                                    <div class="d-flex gap-1">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editTeacher(<?php echo $teacher['id'] ?? ''; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" class="d-inline" onsubmit="return confirm('এই শিক্ষককে মুছে ফেলতে চান?')">
                                            <input type="hidden" name="teacher_id" value="<?php echo $teacher['id'] ?? ''; ?>">
                                            <button type="submit" name="delete_teacher" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <!-- Table View -->
                    <div id="tableView" class="table-responsive" style="display:none;">
                        <h5 class="mb-3">শিক্ষক তালিকা (টেবিল ভিউ)</h5>
                        <table class="table table-bordered align-middle bg-white">
                            <thead class="table-light">
                                <tr>
                                    <th>ক্রমিক</th>
                                    <th>নাম</th>
                                    <th>পদবী</th>
                                    <th>বিষয়</th>
                                    <th>SL</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>ডিটেইলস</th>
                                    <th>এডিট</th>
                                    <th>ডিলিট</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sortedTeachers as $i => $teacher): ?>
                                <tr>
                                    <td><?php echo $i+1; ?></td>
                                    <td><?php echo htmlspecialchars($teacher['name']); ?></td>
                                    <td><?php echo htmlspecialchars($teacher['designation']); ?></td>
                                    <td><?php echo htmlspecialchars($teacher['subject']); ?></td>
                                    <td><?php echo htmlspecialchars($teacher['sl_number'] ?? 'N/A'); ?></td>
                                    <td>
                                        <form method="POST" class="d-inline mb-2">
                                            <input type="hidden" name="teacher_id" value="<?php echo $teacher['id'] ?? ''; ?>">
                                            <select name="duty_status" class="form-select form-select-sm" onchange="this.form.submit()">
                                                <option value="সাধারন" <?php echo ($teacher['duty_status'] === 'সাধারন') ? 'selected' : ''; ?>>সাধারন</option>
                                                <option value="সম সময় অন্তর্ভুক্ত" <?php echo ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') ? 'selected' : ''; ?>>সম সময় অন্তর্ভুক্ত</option>
                                                <option value="কখনো অন্তর্ভুক্ত নয়" <?php echo ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়') ? 'selected' : ''; ?>>কখনো অন্তর্ভুক্ত নয়</option>
                                            </select>
                                            <input type="hidden" name="update_status" value="1">
                                        </form>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showTeacherDetails(<?php echo htmlspecialchars(json_encode($teacher), ENT_QUOTES, 'UTF-8'); ?>)">
                                            <i class="fas fa-eye"></i> বিস্তারিত
                                        </button>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editTeacher(<?php echo $teacher['id'] ?? ''; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline" onsubmit="return confirm('এই শিক্ষককে মুছে ফেলতে চান?')">
                                            <input type="hidden" name="teacher_id" value="<?php echo $teacher['id'] ?? ''; ?>">
                                            <button type="submit" name="delete_teacher" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- List View -->
                    <div id="listView" style="display:none;">
                        <h5 class="mb-3">শিক্ষক তালিকা (লিস্ট ভিউ)</h5>
                        <ul class="list-group">
                            <?php foreach ($sortedTeachers as $i => $teacher): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>
                                    <strong>#<?php echo $i+1; ?>. <?php echo htmlspecialchars($teacher['name']); ?></strong> - <?php echo htmlspecialchars($teacher['subject']); ?>
                                    <span class="badge status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : ($teacher['duty_status'] === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal'); ?> ms-2"><?php echo $teacher['duty_status']; ?></span>
                                </span>
                                <span>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showTeacherDetails(<?php echo htmlspecialchars(json_encode($teacher), ENT_QUOTES, 'UTF-8'); ?>)">
                                        <i class="fas fa-eye"></i> বিস্তারিত
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editTeacher(<?php echo $teacher['id'] ?? ''; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form method="POST" class="d-inline" onsubmit="return confirm('এই শিক্ষককে মুছে ফেলতে চান?')">
                                        <input type="hidden" name="teacher_id" value="<?php echo $teacher['id'] ?? ''; ?>">
                                        <button type="submit" name="delete_teacher" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Bulk Delete Section -->
            <?php if (!empty($uploadedTeachers)): ?>
                <div class="management-card">
                    <div class="section-header">
                        <h4><i class="fas fa-trash-alt me-2"></i> শিক্ষক মুছে ফেলার অপশন</h4>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0"><i class="fas fa-check-square me-2"></i>নির্বাচিত শিক্ষক মুছুন</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">নির্দিষ্ট শিক্ষকদের নির্বাচন করে মুছে ফেলুন</p>

                                    <form method="POST" id="bulkDeleteForm" onsubmit="return confirmBulkDelete()">
                                        <div class="teacher-selection-bulk" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px; padding: 10px; background: #f8f9fa;">
                                            <div class="mb-2">
                                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllForDelete()">
                                                    <i class="fas fa-check-double me-1"></i>সব নির্বাচন
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="clearAllSelection()">
                                                    <i class="fas fa-times me-1"></i>নির্বাচন মুছুন
                                                </button>
                                            </div>

                                            <?php foreach ($uploadedTeachers as $teacher): ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="teacher_ids[]" value="<?php echo $teacher['id'] ?? ''; ?>" id="bulk_teacher_<?php echo $teacher['id'] ?? ''; ?>">
                                                    <label class="form-check-label" for="bulk_teacher_<?php echo $teacher['id'] ?? ''; ?>">
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-2">
                                                                <?php if (!empty($teacher['photo']) && file_exists($teacher['photo'])): ?>
                                                                    <img src="<?php echo htmlspecialchars($teacher['photo']); ?>" alt="<?php echo htmlspecialchars($teacher['name']); ?>" style="width: 30px; height: 30px; object-fit: cover; border-radius: 50%;">
                                                                <?php else: ?>
                                                                    <div style="width: 30px; height: 30px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                                                        <i class="fas fa-user text-muted" style="font-size: 12px;"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div>
                                                                <strong><?php echo htmlspecialchars($teacher['name']); ?></strong><br>
                                                                <small class="text-muted">
                                                                    <?php echo htmlspecialchars($teacher['subject']); ?> -
                                                                    <?php echo htmlspecialchars($teacher['designation']); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>

                                        <div class="mt-3">
                                            <button type="submit" name="bulk_delete_teachers" class="btn btn-warning">
                                                <i class="fas fa-trash me-2"></i>নির্বাচিত শিক্ষক মুছুন
                                            </button>
                                            <small class="text-muted d-block mt-1">
                                                <i class="fas fa-info-circle me-1"></i>নির্বাচিত শিক্ষকদের ছবিসহ সব তথ্য মুছে যাবে
                                            </small>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>সকল শিক্ষক মুছুন</h6>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">ডাটাবেস থেকে সকল শিক্ষকের তথ্য মুছে ফেলুন</p>

                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>সতর্কতা:</strong> এই অপশনটি ব্যবহার করলে সকল শিক্ষকের তথ্য, ছবি এবং ডিউটি বন্টন স্থায়ীভাবে মুছে যাবে।
                                    </div>

                                    <div class="text-center">
                                        <p><strong>মোট শিক্ষক:</strong> <span class="badge bg-primary"><?php echo count($uploadedTeachers); ?> জন</span></p>

                                        <button type="button" class="btn btn-danger" onclick="showDeleteAllModal()">
                                            <i class="fas fa-trash-alt me-2"></i>সকল শিক্ষক মুছুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Step 3: Duty Assignment -->
            <?php if (!empty($uploadedTeachers)): ?>
                <div class="management-card">
                    <div class="section-header">
                        <h4><i class="fas fa-calendar-alt me-2"></i> ধাপ ৩: তারিখ ভিত্তিক ডিউটি বন্টন</h4>
                    </div>

                    <form method="POST" id="dutyAssignmentForm">
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">ডিউটির তারিখ</label>
                                <input type="date" class="form-control" name="duty_date" required>
                            </div>
                            <div class="col-md-8">
                                <label class="form-label">শিক্ষক নির্বাচন</label>
                                <div class="teacher-selection">
                                    <?php
                                    $availableTeachers = array_filter($uploadedTeachers, function($teacher) {
                                        return $teacher['duty_status'] !== 'কখনো অন্তর্ভুক্ত নয়';
                                    });

                                    $alwaysIncluded = array_filter($uploadedTeachers, function($teacher) {
                                        return $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত';
                                    });
                                    ?>

                                    <?php foreach ($availableTeachers as $index => $teacher): ?>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_teachers[]"
                                                   value="<?php echo $teacher['id'] ?? ''; ?>"
                                                   id="teacher_<?php echo $teacher['id'] ?? $index; ?>"
                                                   <?php echo ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত') ? 'checked disabled' : ''; ?>>
                                            <label class="form-check-label" for="teacher_<?php echo $teacher['id'] ?? $index; ?>">
                                                <strong><?php echo htmlspecialchars($teacher['name']); ?></strong>
                                                <small class="text-muted">
                                                    (<?php echo htmlspecialchars($teacher['subject']); ?> - <?php echo htmlspecialchars($teacher['designation']); ?>)
                                                </small>
                                                <?php if ($teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত'): ?>
                                                    <span class="status-badge status-always ms-2">সব সময়</span>
                                                <?php endif; ?>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>

                                    <!-- Hidden inputs for always included teachers -->
                                    <?php foreach ($alwaysIncluded as $index => $teacher): ?>
                                        <input type="hidden" name="selected_teachers[]" value="<?php echo $teacher['id'] ?? ''; ?>">
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <button type="submit" name="assign_duty" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i> ডিউটি বন্টন করুন
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" onclick="selectAllTeachers()">
                                <i class="fas fa-check-double me-2"></i> সব নির্বাচন
                            </button>
                            <button type="button" class="btn btn-outline-secondary ms-2" onclick="clearSelection()">
                                <i class="fas fa-times me-2"></i> নির্বাচন মুছুন
                            </button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Step 4: Duty Assignments View -->
            <?php if (!empty($dutyAssignments)): ?>
                <div class="management-card">
                    <div class="section-header">
                        <h4><i class="fas fa-list-alt me-2"></i> ধাপ ৪: বন্টনকৃত ডিউটি তালিকা</h4>
                    </div>

                    <?php
                    // Sort duty assignments by date
                    ksort($dutyAssignments);
                    ?>

                    <?php foreach ($dutyAssignments as $date => $teacherIndices): ?>
                        <div class="duty-date-card" data-date="<?php echo $date; ?>">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">
                                    <i class="fas fa-calendar me-2"></i>
                                    <?php echo date('d F Y', strtotime($date)); ?>
                                    <span class="badge bg-primary ms-2"><?php echo count($teacherIndices); ?> জন</span>
                                </h5>
                                <div>
                                    <a href="print_duty_list.php?date=<?php echo urlencode($date); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-print me-1"></i> প্রিন্ট
                                    </a>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="date_to_remove" value="<?php echo $date; ?>">
                                        <button type="submit" name="remove_duty" class="btn btn-sm btn-outline-danger"
                                                onclick="return confirm('এই তারিখের ডিউটি মুছে ফেলতে চান?')">
                                            <i class="fas fa-trash me-1"></i> মুছুন
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <div class="row">
                                <?php foreach ($teacherIndices as $teacherIndex): ?>
                                    <?php if (isset($uploadedTeachers[$teacherIndex])): ?>
                                        <?php $teacher = $uploadedTeachers[$teacherIndex]; ?>
                                        <div class="col-md-6 col-lg-4 mb-2">
                                            <div class="card card-body py-2">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($teacher['name']); ?></h6>
                                                        <small class="text-muted">
                                                            <?php echo htmlspecialchars($teacher['subject']); ?> -
                                                            <?php echo htmlspecialchars($teacher['designation']); ?>
                                                        </small>
                                                    </div>
                                                    <span class="status-badge status-<?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'always' : 'normal'; ?>">
                                                        <?php echo $teacher['duty_status'] === 'সম সময় অন্তর্ভুক্ত' ? 'সব সময়' : 'নিয়মিত'; ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <!-- Print Template (Hidden) -->
            <div id="printTemplate" style="display: none;">
                <div class="print-header text-center mb-4">
                    <h2>আব্দুল ওদুদ শাহ ডিগ্রি কলেজ</h2>
                    <p>দামুড়হুদা, চুয়াডাঙ্গা</p>
                    <hr>
                    <h4 id="printDate"></h4>
                    <h5>পরীক্ষা ডিউটি তালিকা</h5>
                </div>
                <div id="printContent"></div>
            </div>
        </div>
    </div>

    <!-- Edit Teacher Modal -->
    <div class="modal fade" id="editTeacherModal" tabindex="-1" aria-labelledby="editTeacherModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editTeacherModalLabel">
                        <i class="fas fa-edit me-2"></i>শিক্ষকের তথ্য সম্পাদনা
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="editTeacherForm">
                    <div class="modal-body">
                        <input type="hidden" name="teacher_id" id="editTeacherId">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ক্রমিক নং</label>
                                    <input type="number" class="form-control" name="teacher_sl" id="editTeacherSl" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">শিক্ষকের নাম</label>
                                    <input type="text" class="form-control" name="teacher_name" id="editTeacherName" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">মোবাইল নম্বর</label>
                                    <input type="text" class="form-control" name="teacher_mobile" id="editTeacherMobile" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">বিষয়</label>
                                    <input type="text" class="form-control" name="teacher_subject" id="editTeacherSubject" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">পদবী</label>
                                    <input type="text" class="form-control" name="teacher_designation" id="editTeacherDesignation" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">কলেজ</label>
                                    <input type="text" class="form-control" name="teacher_college" id="editTeacherCollege" required>
                                </div>
                            </div>
                        </div>

                        <!-- Current Photo Display -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">বর্তমান ছবি</label>
                                    <div id="currentPhotoDisplay" class="mb-2">
                                        <!-- Current photo will be displayed here -->
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">নতুন ছবি আপলোড করুন (ঐচ্ছিক)</label>
                                    <div class="photo-upload-area" id="editPhotoUploadArea">
                                        <input type="file" class="form-control" name="teacher_photo" id="editTeacherPhoto" accept="image/*" style="display: none;">
                                        <div class="upload-placeholder" onclick="document.getElementById('editTeacherPhoto').click()">
                                            <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                                            <p class="text-muted">নতুন ছবি আপলোড করতে ক্লিক করুন</p>
                                            <small class="text-muted">JPG, PNG, GIF ফরম্যাট সাপোর্ট করে</small>
                                        </div>
                                        <div class="photo-preview" id="editPhotoPreview" style="display: none;">
                                            <img id="editPreviewImage" src="" alt="Preview" style="max-width: 150px; max-height: 150px; border-radius: 10px;">
                                            <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeEditPhoto()">
                                                <i class="fas fa-trash me-1"></i>ছবি মুছুন
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>বাতিল
                        </button>
                        <button type="submit" name="edit_teacher" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>আপডেট করুন
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete All Teachers Confirmation Modal -->
    <div class="modal fade" id="deleteAllModal" tabindex="-1" aria-labelledby="deleteAllModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteAllModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>সকল শিক্ষক মুছে ফেলার নিশ্চিতকরণ
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!</strong>
                    </div>

                    <p>আপনি কি নিশ্চিত যে সকল শিক্ষকের তথ্য মুছে ফেলতে চান?</p>

                    <ul class="text-muted">
                        <li>সকল শিক্ষকের ব্যক্তিগত তথ্য মুছে যাবে</li>
                        <li>সকল শিক্ষকের ছবি মুছে যাবে</li>
                        <li>সকল ডিউটি বন্টন তথ্য মুছে যাবে</li>
                        <li>এই কাজটি স্থায়ী এবং পূর্বাবস্থায় ফেরানো যাবে না</li>
                    </ul>

                    <div class="form-check mt-3">
                        <input class="form-check-input" type="checkbox" id="confirmDeleteAll" required>
                        <label class="form-check-label" for="confirmDeleteAll">
                            আমি নিশ্চিত যে সকল শিক্ষকের তথ্য মুছে ফেলতে চাই
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>বাতিল
                    </button>
                    <form method="POST" class="d-inline" id="deleteAllForm">
                        <input type="hidden" name="confirm_delete_all" value="yes">
                        <button type="submit" name="delete_all_teachers" class="btn btn-danger" id="confirmDeleteAllBtn" disabled>
                            <i class="fas fa-trash-alt me-2"></i>সকল শিক্ষক মুছুন
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Details Modal -->
    <div class="modal fade" id="teacherDetailsModal" tabindex="-1" aria-labelledby="teacherDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="teacherDetailsModalLabel">
                        <i class="fas fa-user me-2"></i>শিক্ষকের বিস্তারিত তথ্য
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="teacherDetailsContent">
                    <!-- Teacher details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>বন্ধ করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // File upload handling
        document.getElementById('csvFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = (file.size / 1024).toFixed(2) + ' KB';
                document.getElementById('fileInfo').style.display = 'block';
            }
        });

        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('csvFile').files = files;
                document.getElementById('csvFile').dispatchEvent(new Event('change'));
            }
        });

        // Teacher selection functions
        function selectAllTeachers() {
            const checkboxes = document.querySelectorAll('input[name="selected_teachers[]"]:not([disabled])');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function clearSelection() {
            const checkboxes = document.querySelectorAll('input[name="selected_teachers[]"]:not([disabled])');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        // Photo upload handling
        document.getElementById('teacherPhoto').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('শুধুমাত্র JPG, PNG, GIF ফরম্যাট সাপোর্ট করে!');
                    e.target.value = '';
                    return;
                }

                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('ফাইল সাইজ ৫MB এর কম হতে হবে!');
                    e.target.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('previewImage').src = e.target.result;
                    document.querySelector('.upload-placeholder').style.display = 'none';
                    document.getElementById('photoPreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove photo function
        function removePhoto() {
            document.getElementById('teacherPhoto').value = '';
            document.getElementById('previewImage').src = '';
            document.querySelector('.upload-placeholder').style.display = 'block';
            document.getElementById('photoPreview').style.display = 'none';
        }

        // Bulk delete functions
        function selectAllForDelete() {
            const checkboxes = document.querySelectorAll('input[name="teacher_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        }

        function clearAllSelection() {
            const checkboxes = document.querySelectorAll('input[name="teacher_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        }

        function confirmBulkDelete() {
            const checkedBoxes = document.querySelectorAll('input[name="teacher_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                alert('অনুগ্রহ করে কমপক্ষে একজন শিক্ষক নির্বাচন করুন।');
                return false;
            }

            const count = checkedBoxes.length;
            const message = `আপনি কি নিশ্চিত যে ${count} জন শিক্ষককে মুছে ফেলতে চান?\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না।`;
            return confirm(message);
        }

        function showDeleteAllModal() {
            const modal = new bootstrap.Modal(document.getElementById('deleteAllModal'));
            modal.show();
        }

        // Delete all confirmation checkbox handler
        document.getElementById('confirmDeleteAll').addEventListener('change', function() {
            const confirmBtn = document.getElementById('confirmDeleteAllBtn');
            confirmBtn.disabled = !this.checked;
        });

        // Edit teacher function
        function editTeacher(teacherId) {
            const teachers = <?php echo json_encode($uploadedTeachers); ?>;
            const teacher = teachers.find(t => t.id == teacherId);

            if (teacher) {
                // Populate form fields
                document.getElementById('editTeacherId').value = teacher.id || '';
                document.getElementById('editTeacherSl').value = teacher.sl_number || '';
                document.getElementById('editTeacherName').value = teacher.name;
                document.getElementById('editTeacherMobile').value = teacher.mobile;
                document.getElementById('editTeacherSubject').value = teacher.subject;
                document.getElementById('editTeacherDesignation').value = teacher.designation;
                document.getElementById('editTeacherCollege').value = teacher.college;

                // Display current photo
                const currentPhotoDisplay = document.getElementById('currentPhotoDisplay');
                if (teacher.photo && teacher.photo !== '') {
                    currentPhotoDisplay.innerHTML = `
                        <div class="current-photo">
                            <img src="${teacher.photo}" alt="Current Photo" style="width: 100px; height: 100px; object-fit: cover; border-radius: 10px; border: 2px solid #ddd;">
                            <p class="text-muted mt-1">বর্তমান ছবি</p>
                        </div>
                    `;
                } else {
                    currentPhotoDisplay.innerHTML = `
                        <div class="no-photo">
                            <div style="width: 100px; height: 100px; background: #f8f9fa; border: 2px solid #ddd; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user fa-2x text-muted"></i>
                            </div>
                            <p class="text-muted mt-1">কোনো ছবি নেই</p>
                        </div>
                    `;
                }

                // Reset photo upload area
                document.getElementById('editTeacherPhoto').value = '';
                document.querySelector('#editPhotoUploadArea .upload-placeholder').style.display = 'block';
                document.getElementById('editPhotoPreview').style.display = 'none';

                // Show modal
                const modal = new bootstrap.Modal(document.getElementById('editTeacherModal'));
                modal.show();
            }
        }

        // Edit photo upload handling
        document.getElementById('editTeacherPhoto').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                if (!allowedTypes.includes(file.type)) {
                    alert('শুধুমাত্র JPG, PNG, GIF ফরম্যাট সাপোর্ট করে!');
                    e.target.value = '';
                    return;
                }

                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('ফাইল সাইজ ৫MB এর কম হতে হবে!');
                    e.target.value = '';
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('editPreviewImage').src = e.target.result;
                    document.querySelector('#editPhotoUploadArea .upload-placeholder').style.display = 'none';
                    document.getElementById('editPhotoPreview').style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });

        // Remove edit photo function
        function removeEditPhoto() {
            document.getElementById('editTeacherPhoto').value = '';
            document.getElementById('editPreviewImage').src = '';
            document.querySelector('#editPhotoUploadArea .upload-placeholder').style.display = 'block';
            document.getElementById('editPhotoPreview').style.display = 'none';
        }

        function switchView(view) {
            document.getElementById('cardView').style.display = (view === 'card') ? '' : 'none';
            document.getElementById('tableView').style.display = (view === 'table') ? '' : 'none';
            document.getElementById('listView').style.display = (view === 'list') ? '' : 'none';
            document.getElementById('cardViewBtn').classList.toggle('active', view === 'card');
            document.getElementById('tableViewBtn').classList.toggle('active', view === 'table');
            document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
        }
        window.addEventListener('DOMContentLoaded', function() {
            switchView('card'); // Ensure default view is card
        });

        function showTeacherDetails(teacher) {
            let html = `<div class=\"row\">`
                + `<div class=\"col-md-4 text-center\">`
                + (teacher.photo && teacher.photo !== '' ?
                    `<img src=\"${teacher.photo}\" alt=\"Teacher Photo\" style=\"width: 200px; height: 200px; object-fit: cover; border-radius: 10px; border: 3px solid #ddd;\">` :
                    `<div style=\"width: 200px; height: 200px; background: #f8f9fa; border: 3px solid #ddd; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin: 0 auto;\">`
                + `<i class='fas fa-user fa-4x text-muted'></i></div>`)
                + `<h5 class=\"mt-3\">${teacher.name}</h5>`
                + `<span class=\"status-badge status-${teacher.duty_status === 'সম সময় অন্তর্ভুক্ত' ? 'always' : (teacher.duty_status === 'কখনো অন্তর্ভুক্ত নয়' ? 'never' : 'normal')}\">${teacher.duty_status}</span>`
                + `</div>`
                + `<div class=\"col-md-8\">`
                + `<table class=\"table table-bordered\">`
                + `<tr><th>ক্রমিক নং</th><td>${teacher.sl_number || 'N/A'}</td></tr>`
                + `<tr><th>নাম</th><td>${teacher.name}</td></tr>`
                + `<tr><th>মোবাইল</th><td>${teacher.mobile}</td></tr>`
                + `<tr><th>বিষয়</th><td>${teacher.subject}</td></tr>`
                + `<tr><th>পদবী</th><td>${teacher.designation}</td></tr>`
                + `<tr><th>কলেজ</th><td>${teacher.college}</td></tr>`
                + `</table>`
                + `</div>`
                + `</div>`;
            document.getElementById('teacherDetailsContent').innerHTML = html;
            var modal = new bootstrap.Modal(document.getElementById('teacherDetailsModal'));
            modal.show();
        }
    </script>
</body>
</html>
