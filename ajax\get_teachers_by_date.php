<?php
require_once '../includes/teacher_db.php';
header('Content-Type: application/json');
if (!isset($_GET['date'])) {
    echo json_encode([]);
    exit;
}
$date = $_GET['date'];
$teacherManager = new TeacherManager($pdo);
// Get teacher IDs assigned on this date
$stmt = $pdo->prepare('SELECT teacher_id FROM duty_assignments WHERE duty_date = ?');
$stmt->execute([$date]);
$ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
if (!$ids) {
    echo json_encode([]);
    exit;
}
// Get teacher info
$in = str_repeat('?,', count($ids) - 1) . '?';
$sql = "SELECT id, name, subject, designation FROM teachers WHERE id IN ($in) ORDER BY name ASC";
$stmt = $pdo->prepare($sql);
$stmt->execute($ids);
$teachers = $stmt->fetchAll();
echo json_encode($teachers); 