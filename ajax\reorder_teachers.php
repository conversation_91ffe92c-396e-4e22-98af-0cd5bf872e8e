<?php
session_start();
require_once '../includes/teacher_db.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['date']) || !isset($input['room_number']) || !isset($input['teacher_orders'])) {
        throw new Exception('Missing required parameters');
    }
    
    $date = $input['date'];
    $roomNumber = $input['room_number'];
    $teacherOrders = $input['teacher_orders']; // Array of teacher IDs in new order
    
    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        throw new Exception('Invalid date format');
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    // Update the order of teachers in the duty_assignments table
    // We'll use a temporary order field or update based on position
    $position = 1;
    foreach ($teacherOrders as $teacherId) {
        $sql = "UPDATE duty_assignments 
                SET teacher_order = :position 
                WHERE teacher_id = :teacher_id 
                AND duty_date = :duty_date 
                AND (room_number = :room_number OR (room_number IS NULL AND :room_number = 'অনির্দিষ্ট'))";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':position' => $position,
            ':teacher_id' => $teacherId,
            ':duty_date' => $date,
            ':room_number' => $roomNumber === 'অনির্দিষ্ট' ? null : $roomNumber
        ]);
        
        $position++;
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'শিক্ষকদের ক্রম সফলভাবে আপডেট হয়েছে'
    ]);
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
