<?php
require_once __DIR__ . '/models/Student.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $student = new Student();
        
        $data = [
            'c_code' => $_POST['c_code'] ?? '',
            'eiin' => $_POST['eiin'] ?? '',
            'roll_no' => $_POST['roll_no'] ?? '',
            'reg_no' => $_POST['reg_no'] ?? '',
            'session' => $_POST['session'] ?? '',
            'type' => $_POST['type'] ?? '',
            'group_name' => $_POST['group_name'] ?? '',
            'student_name' => $_POST['student_name'] ?? '',
            'father_name' => $_POST['father_name'] ?? '',
            'gender' => $_POST['gender'] ?? '',
            'sub_1' => $_POST['sub_1'] ?? '',
            'sub_2' => $_POST['sub_2'] ?? '',
            'sub_3' => $_POST['sub_3'] ?? '',
            'sub_4' => $_POST['sub_4'] ?? '',
            'sub_5' => $_POST['sub_5'] ?? '',
            'sub_6' => $_POST['sub_6'] ?? '',
            'sub_7' => $_POST['sub_7'] ?? '',
            'sub_8' => $_POST['sub_8'] ?? '',
            'sub_9' => $_POST['sub_9'] ?? '',
            'sub_10' => $_POST['sub_10'] ?? '',
            'sub_11' => $_POST['sub_11'] ?? '',
            'sub_12' => $_POST['sub_12'] ?? '',
            'sub_13' => $_POST['sub_13'] ?? ''
        ];
        
        if (empty($data['student_name'])) {
            throw new Exception('Student name is required');
        }
        
        if ($student->create($data)) {
            $message = 'Student added successfully!';
            $messageType = 'success';
        } else {
            $message = 'Failed to add student';
            $messageType = 'danger';
        }
        
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h3 class="text-center">Add New Student</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="c_code" class="form-label">C.Code</label>
                                    <input type="text" class="form-control" id="c_code" name="c_code">
                                </div>
                                <div class="col-md-3">
                                    <label for="eiin" class="form-label">EIIN</label>
                                    <input type="text" class="form-control" id="eiin" name="eiin">
                                </div>
                                <div class="col-md-3">
                                    <label for="roll_no" class="form-label">Roll No.</label>
                                    <input type="text" class="form-control" id="roll_no" name="roll_no">
                                </div>
                                <div class="col-md-3">
                                    <label for="reg_no" class="form-label">Reg. No.</label>
                                    <input type="text" class="form-control" id="reg_no" name="reg_no">
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-md-4">
                                    <label for="session" class="form-label">Session</label>
                                    <input type="text" class="form-control" id="session" name="session" placeholder="2023-24">
                                </div>
                                <div class="col-md-4">
                                    <label for="type" class="form-label">Type</label>
                                    <select class="form-control" id="type" name="type">
                                        <option value="">Select Type</option>
                                        <option value="Regular">Regular</option>
                                        <option value="Irregular">Irregular</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="group_name" class="form-label">Group</label>
                                    <select class="form-control" id="group_name" name="group_name">
                                        <option value="">Select Group</option>
                                        <option value="Science">Science</option>
                                        <option value="Commerce">Commerce</option>
                                        <option value="Business Studies">Business Studies</option>
                                        <option value="Arts">Arts</option>
                                        <option value="Humanities">Humanities</option>
                                    </select>
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-md-6">
                                    <label for="student_name" class="form-label">Student Name *</label>
                                    <input type="text" class="form-control" id="student_name" name="student_name" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="father_name" class="form-label">Father's Name</label>
                                    <input type="text" class="form-control" id="father_name" name="father_name">
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-md-4">
                                    <label for="gender" class="form-label">Gender</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">Select Gender</option>
                                        <option value="Male">Male</option>
                                        <option value="Female">Female</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>
                            </div>

                            <h5 class="mt-4">Subjects</h5>
                            <div class="row g-3">
                                <?php for ($i = 1; $i <= 13; $i++): ?>
                                    <div class="col-md-4">
                                        <label for="sub_<?php echo $i; ?>" class="form-label">Subject <?php echo $i; ?></label>
                                        <input type="text" class="form-control" id="sub_<?php echo $i; ?>" name="sub_<?php echo $i; ?>">
                                    </div>
                                <?php endfor; ?>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> Add Student
                                </button>
                                <a href="view_students.php" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-list"></i> View Students
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://kit.fontawesome.com/a076d05399.js"></script>
</body>
</html>
