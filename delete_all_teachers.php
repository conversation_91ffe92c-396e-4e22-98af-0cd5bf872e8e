<?php
require_once 'includes/teacher_db.php';

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete_all']) && $_POST['confirm_delete_all'] === 'DELETE_ALL_TEACHERS') {
        try {
            if ($teacherManager->deleteAllTeachers()) {
                $message = 'সকল শিক্ষক সফলভাবে মুছে ফেলা হয়েছে!';
                $messageType = 'success';
            } else {
                $message = 'সকল শিক্ষক মুছতে সমস্যা হয়েছে।';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } else {
        $message = 'নিশ্চিতকরণ টেক্সট সঠিক নয়।';
        $messageType = 'warning';
    }
}

// Get current teachers for display
$teachers = $teacherManager->getAllTeachers();
$totalTeachers = count($teachers);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>সকল শিক্ষক মুছুন - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Hind+Siliguri:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Hind Siliguri', sans-serif;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 800px;
            margin: 0 auto;
        }
        .danger-zone {
            border: 3px solid #dc3545;
            border-radius: 10px;
            padding: 20px;
            background: #fff5f5;
        }
        .warning-icon {
            font-size: 4rem;
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        .confirmation-input {
            font-family: monospace;
            font-weight: bold;
            color: #dc3545;
        }
        .stats-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center mb-4">
            <i class="fas fa-exclamation-triangle warning-icon"></i>
            <h1 class="text-danger mt-3">সকল শিক্ষক মুছে ফেলুন</h1>
            <p class="text-muted">এই অপশনটি সকল শিক্ষকের তথ্য স্থায়ীভাবে মুছে ফেলবে</p>
        </div>

        <!-- Message Display -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Navigation -->
        <div class="mb-4 text-center">
            <a href="teacher_duty_management.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>শিক্ষক ব্যবস্থাপনায় ফিরুন
            </a>
            <a href="bulk_delete_teachers.php" class="btn btn-warning ms-2">
                <i class="fas fa-check-square me-2"></i>নির্বাচিত শিক্ষক মুছুন
            </a>
        </div>

        <?php if ($totalTeachers > 0): ?>
            <!-- Current Statistics -->
            <div class="stats-card">
                <h4><i class="fas fa-users text-primary me-2"></i>বর্তমান পরিসংখ্যান</h4>
                <div class="row mt-3">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h2 class="text-primary"><?php echo $totalTeachers; ?></h2>
                            <p class="text-muted">মোট শিক্ষক</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <?php 
                            $teachersWithPhotos = array_filter($teachers, function($t) { 
                                return !empty($t['photo']) && file_exists($t['photo']); 
                            });
                            ?>
                            <h2 class="text-info"><?php echo count($teachersWithPhotos); ?></h2>
                            <p class="text-muted">ছবিসহ শিক্ষক</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <?php 
                            $subjects = array_unique(array_column($teachers, 'subject'));
                            ?>
                            <h2 class="text-success"><?php echo count($subjects); ?></h2>
                            <p class="text-muted">বিষয়</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="danger-zone">
                <h3 class="text-danger text-center mb-4">
                    <i class="fas fa-skull-crossbones me-2"></i>বিপদজনক এলাকা
                </h3>
                
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা!</h5>
                    <p class="mb-2">এই কাজটি করলে নিম্নলিখিত সব কিছু <strong>স্থায়ীভাবে</strong> মুছে যাবে:</p>
                    <ul class="mb-0">
                        <li><strong><?php echo $totalTeachers; ?> জন</strong> শিক্ষকের সকল ব্যক্তিগত তথ্য</li>
                        <li><strong><?php echo count($teachersWithPhotos); ?> টি</strong> শিক্ষকের ছবি</li>
                        <li>সকল ডিউটি বন্টন এবং অ্যাসাইনমেন্ট তথ্য</li>
                        <li>শিক্ষকদের সাথে সম্পর্কিত সকল রেকর্ড</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>বিকল্প:</strong> যদি আপনি শুধু কিছু শিক্ষক মুছতে চান, তাহলে 
                    <a href="bulk_delete_teachers.php" class="alert-link">নির্বাচিত শিক্ষক মুছুন</a> অপশন ব্যবহার করুন।
                </div>

                <form method="POST" id="deleteAllForm" onsubmit="return confirmDeleteAll()">
                    <div class="mb-3">
                        <label for="confirmText" class="form-label">
                            <strong>নিশ্চিতকরণের জন্য নিচের টেক্সটটি টাইপ করুন:</strong>
                        </label>
                        <div class="text-center mb-2">
                            <code class="fs-5 text-danger">DELETE_ALL_TEACHERS</code>
                        </div>
                        <input type="text" class="form-control confirmation-input text-center" 
                               id="confirmText" name="confirm_delete_all" 
                               placeholder="DELETE_ALL_TEACHERS" 
                               autocomplete="off" required>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="finalConfirm" required>
                        <label class="form-check-label" for="finalConfirm">
                            আমি বুঝতে পারছি যে এই কাজটি পূর্বাবস্থায় ফেরানো যাবে না এবং সকল শিক্ষকের তথ্য স্থায়ীভাবে মুছে যাবে।
                        </label>
                    </div>

                    <div class="text-center">
                        <button type="submit" class="btn btn-danger btn-lg" id="deleteAllBtn" disabled>
                            <i class="fas fa-trash-alt me-2"></i>সকল শিক্ষক স্থায়ীভাবে মুছুন
                        </button>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-users fa-5x text-muted mb-3"></i>
                <h3>কোন শিক্ষক পাওয়া যায়নি</h3>
                <p class="text-muted">ডাটাবেসে কোন শিক্ষকের তথ্য নেই।</p>
                <a href="teacher_duty_management.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>শিক্ষক যুক্ত করুন
                </a>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const confirmText = document.getElementById('confirmText');
        const finalConfirm = document.getElementById('finalConfirm');
        const deleteAllBtn = document.getElementById('deleteAllBtn');

        function checkFormValidity() {
            const textValid = confirmText.value === 'DELETE_ALL_TEACHERS';
            const checkboxValid = finalConfirm.checked;
            deleteAllBtn.disabled = !(textValid && checkboxValid);
            
            if (textValid) {
                confirmText.classList.remove('is-invalid');
                confirmText.classList.add('is-valid');
            } else {
                confirmText.classList.remove('is-valid');
                if (confirmText.value.length > 0) {
                    confirmText.classList.add('is-invalid');
                }
            }
        }

        confirmText.addEventListener('input', checkFormValidity);
        finalConfirm.addEventListener('change', checkFormValidity);

        function confirmDeleteAll() {
            if (confirmText.value !== 'DELETE_ALL_TEACHERS') {
                alert('অনুগ্রহ করে সঠিক নিশ্চিতকরণ টেক্সট টাইপ করুন।');
                return false;
            }
            
            if (!finalConfirm.checked) {
                alert('অনুগ্রহ করে নিশ্চিতকরণ চেকবক্স টিক দিন।');
                return false;
            }
            
            const finalMessage = `⚠️ চূড়ান্ত সতর্কতা! ⚠️\n\nআপনি <?php echo $totalTeachers; ?> জন শিক্ষকের সকল তথ্য স্থায়ীভাবে মুছে ফেলতে চলেছেন।\n\nএই কাজটি পূর্বাবস্থায় ফেরানো যাবে না!\n\nআপনি কি সম্পূর্ণ নিশ্চিত?`;
            
            return confirm(finalMessage);
        }

        // Initialize form validation
        checkFormValidity();
    </script>
</body>
</html>
