<?php
require_once __DIR__ . '/models/Student.php';

// Get parameters
$cardsPerPage = (int)($_GET['cards_per_page'] ?? 12);
$searchName = $_GET['search'] ?? '';
$searchRoll = $_GET['search_roll'] ?? '';
$subjectCode = $_GET['subject'] ?? '';
$groupName = $_GET['group'] ?? '';
$designType = $_GET['design'] ?? 'original';

// Validate cards per page - allow any reasonable number
if ($cardsPerPage < 1 || $cardsPerPage > 30) {
    $cardsPerPage = 12;
}

// Get students
$students = [];
$totalStudents = 0;
$error = '';

try {
    $student = new Student();
    $allStudents = $student->getAll();
    
    // Apply filters
    foreach ($allStudents as $s) {
        $include = true;
        
        // Search by name
        $studentName = $s['name'] ?? $s['student_name'] ?? '';
        if (!empty($searchName) && stripos($studentName, $searchName) === false) {
            $include = false;
        }
        
        // Search by roll number
        $studentRoll = $s['roll'] ?? $s['roll_no'] ?? '';
        if (!empty($searchRoll) && stripos($studentRoll, $searchRoll) === false) {
            $include = false;
        }
        
        // Filter by subject code
        if (!empty($subjectCode)) {
            $hasSubject = false;

            // Check new subjects field format
            if (!empty($s['subjects'])) {
                $subjects = explode(',', $s['subjects']);
                foreach ($subjects as $subject) {
                    if (trim($subject) === $subjectCode) {
                        $hasSubject = true;
                        break;
                    }
                }
            }

            // Also check old format for backward compatibility
            if (!$hasSubject) {
                for ($i = 1; $i <= 13; $i++) {
                    if (trim($s["sub_$i"] ?? '') === $subjectCode) {
                        $hasSubject = true;
                        break;
                    }
                }
            }

            if (!$hasSubject) {
                $include = false;
            }
        }
        
        // Filter by group
        $groupField = $s['department'] ?? $s['group_name'] ?? '';
        if (!empty($groupName) && stripos($groupField, $groupName) === false) {
            $include = false;
        }
        
        if ($include) {
            $students[] = $s;
        }
    }

    // Sort students by roll number in ascending order
    usort($students, function($a, $b) {
        $rollA = (int)($a['roll'] ?? $a['roll_no'] ?? 0);
        $rollB = (int)($b['roll'] ?? $b['roll_no'] ?? 0);
        return $rollA - $rollB;
    });

    $totalStudents = count($students);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

// Calculate layout
$totalPages = ceil($totalStudents / $cardsPerPage);

// Subject names for display
$subjectNames = [
    '101' => 'Bangla', '107' => 'English', '275' => 'ICT', '174' => 'Physics',
    '176' => 'Chemistry', '178' => 'Biology', '265' => 'Higher Math',
    '253' => 'Accounting', '292' => 'Finance & Banking', '277' => 'Business Org',
    '286' => 'Production Management', '109' => 'Economics', '121' => 'Logic',
    '117' => 'Sociology', '249' => 'Study Of Islam', '271' => 'Social Work',
    '273' => 'Home Science', '267' => 'Islamic History', '269' => 'Civics',
    '304' => 'History', '129' => 'Statistics'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seat Card Generator - EXMM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <div class="container-fluid mt-4">
            <!-- Header -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <h2>🎫 Seat Card Generator</h2>
                    <p class="text-muted">Generate printable seat cards for students</p>
                </div>
                <div class="col-md-4 text-end">
                    <?php if ($totalStudents > 0): ?>
                        <?php
                        $designFiles = [
                            'original' => 'print_seat_cards.php',
                            'modern' => 'new_seat_card_design.php',
                            'minimalist' => 'minimalist_seat_card.php',
                            'classic' => 'classic_seat_card.php'
                        ];
                        $printFile = $designFiles[$designType] ?? 'print_seat_cards.php';
                        ?>
                        <a href="working_seat_cards.php?<?php echo http_build_query($_GET); ?>"
                           class="btn btn-success btn-lg" target="_blank">
                            🎫 Generate Seat Cards
                        </a>
                        <br><small class="text-muted">Click to generate and print</small>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>🔧 Customize Seat Cards</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3">
                                <div class="col-md-2">
                                    <label class="form-label">Design Type</label>
                                    <select name="design" class="form-select">
                                        <option value="original" <?php echo $designType == 'original' ? 'selected' : ''; ?>>📋 Original</option>
                                        <option value="modern" <?php echo $designType == 'modern' ? 'selected' : ''; ?>>🌈 Modern Light</option>
                                        <option value="minimalist" <?php echo $designType == 'minimalist' ? 'selected' : ''; ?>>✨ Minimalist</option>
                                        <option value="classic" <?php echo $designType == 'classic' ? 'selected' : ''; ?>>🏛️ Classic</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Cards Per Page</label>
                                    <input type="number" name="cards_per_page" class="form-control"
                                           value="<?php echo $cardsPerPage; ?>" min="1" max="30"
                                           placeholder="1-30">
                                    <small class="text-muted">1-30 cards</small>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Search by Name</label>
                                    <input type="text" name="search" class="form-control" 
                                           placeholder="Student name..." value="<?php echo htmlspecialchars($searchName); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Search by Roll</label>
                                    <input type="text" name="search_roll" class="form-control" 
                                           placeholder="Roll number..." value="<?php echo htmlspecialchars($searchRoll); ?>">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Subject Code</label>
                                    <select name="subject" class="form-select">
                                        <option value="">All Subjects</option>
                                        <?php foreach ($subjectNames as $code => $name): ?>
                                            <option value="<?php echo $code; ?>" <?php echo $subjectCode == $code ? 'selected' : ''; ?>>
                                                <?php echo $code; ?> - <?php echo $name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Group</label>
                                    <input type="text" name="group" class="form-control"
                                           placeholder="Science, Commerce, Business Studies..." value="<?php echo htmlspecialchars($groupName); ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">🔄 Update Cards</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body text-center">
                            <h4 class="text-success"><?php echo $totalStudents; ?></h4>
                            <p class="mb-0">Total Students</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-info">
                        <div class="card-body text-center">
                            <h4 class="text-info"><?php echo $cardsPerPage; ?></h4>
                            <p class="mb-0">Cards Per Page</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-warning">
                        <div class="card-body text-center">
                            <h4 class="text-warning"><?php echo $totalPages; ?></h4>
                            <p class="mb-0">Total Pages</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <?php
                            // Determine which design file to use
                            $designFiles = [
                                'original' => 'print_seat_cards.php',
                                'modern' => 'new_seat_card_design.php',
                                'minimalist' => 'minimalist_seat_card.php',
                                'classic' => 'classic_seat_card.php'
                            ];
                            $printFile = $designFiles[$designType] ?? 'print_seat_cards.php';
                            ?>
                            <?php if ($totalStudents > 0): ?>
                                <a href="working_seat_cards.php?<?php echo http_build_query($_GET); ?>"
                                   class="btn btn-primary btn-lg" target="_blank">
                                    🎫 Generate & Print Cards
                                </a>
                                <small class="d-block text-muted mt-1">Click to generate seat cards</small>
                            <?php else: ?>
                                <a href="<?php echo $printFile; ?>?<?php echo http_build_query($_GET); ?>"
                                   class="btn btn-secondary" target="_blank">
                                    🖨️ Generate Cards
                                </a>
                                <small class="d-block text-muted mt-1">No students found</small>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error Display -->
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <strong>Error:</strong> <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <?php if ($totalStudents == 0): ?>
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> কোন শিক্ষার্থী পাওয়া যায়নি!</h5>
                            <p class="mb-3">সীটকার্ড তৈরী করার জন্য প্রথমে শিক্ষার্থী ডেটা যোগ করুন।</p>
                            <div class="d-flex gap-2 flex-wrap">
                                <a href="add_sample_data.php" class="btn btn-warning">
                                    <i class="fas fa-flask"></i> নমুনা ডেটা যোগ করুন
                                </a>
                                <a href="add_student.php" class="btn btn-primary">
                                    <i class="fas fa-user-plus"></i> শিক্ষার্থী যোগ করুন
                                </a>
                                <a href="upload.php" class="btn btn-success">
                                    <i class="fas fa-upload"></i> Excel আপলোড করুন
                                </a>
                                <a href="simple_csv_upload.php" class="btn btn-info">
                                    <i class="fas fa-file-csv"></i> CSV আপলোড করুন
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Preview -->
            <?php if ($totalStudents > 0): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5>👁️ Preview (First Page)</h5>
                                <div>
                                    <?php
                                    $designFiles = [
                                        'original' => 'print_seat_cards.php',
                                        'modern' => 'new_seat_card_design.php',
                                        'minimalist' => 'minimalist_seat_card.php',
                                        'classic' => 'classic_seat_card.php'
                                    ];
                                    $printFile = $designFiles[$designType] ?? 'print_seat_cards.php';
                                    ?>
                                    <a href="working_seat_cards.php?<?php echo http_build_query($_GET); ?>"
                                       class="btn btn-success btn-lg" target="_blank">
                                        🎫 Generate All Seat Cards
                                    </a>
                                    <a href="add_sample_data.php" class="btn btn-warning">
                                        🧪 Add Sample Data
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <strong>Layout:</strong> <?php echo $cardsPerPage; ?> cards per page | 
                                    <strong>Total:</strong> <?php echo $totalStudents; ?> students | 
                                    <strong>Pages:</strong> <?php echo $totalPages; ?>
                                </div>
                                
                                <!-- Preview Grid -->
                                <div class="row">
                                    <?php 
                                    $previewCount = min($cardsPerPage, $totalStudents);
                                    $colClass = '';
                                    switch($cardsPerPage) {
                                        case 6: $colClass = 'col-md-4'; break;  // 3x2
                                        case 9: $colClass = 'col-md-4'; break;  // 3x3
                                        case 12: $colClass = 'col-md-3'; break; // 4x3
                                        case 16: $colClass = 'col-md-3'; break; // 4x4
                                        case 20: $colClass = 'col-md-2'; break; // 5x4
                                    }
                                    
                                    for ($i = 0; $i < $previewCount; $i++): 
                                        $s = $students[$i];
                                    ?>
                                        <div class="<?php echo $colClass; ?> mb-3">
                                            <div class="card border-secondary" style="font-size: 0.8em;">
                                                <div class="card-body p-1">
                                                    <div class="text-center">
                                                        <div style="font-weight: bold; font-size: 0.7em; border-bottom: 1px solid #333; margin-bottom: 3px; padding-bottom: 1px;">
                                                            HSC Exam-2025
                                                        </div>
                                                        <div style="font-size: 0.6em; color: #666; margin-bottom: 2px;">
                                                            দামুড়হুদা, কোড. 295
                                                        </div>
                                                        <h6 class="card-title mb-1" style="font-size: 0.9em; line-height: 1.1;">
                                                            <?php echo htmlspecialchars($s['name'] ?? $s['student_name'] ?? ''); ?>
                                                        </h6>
                                                        <div style="background: #f8f9fa; border: 1px solid #333; border-radius: 4px; padding: 3px; margin: 3px 0; font-weight: bold; font-size: 1.2em;">
                                                            <?php echo htmlspecialchars($s['roll'] ?? $s['roll_no'] ?? ''); ?>
                                                        </div>
                                                        <p class="mb-0" style="font-size: 0.65em; line-height: 1.1;"><strong>রেজিঃ</strong> <?php echo htmlspecialchars($s['registration'] ?? $s['reg_no'] ?? ''); ?></p>
                                                        <p class="mb-0" style="font-size: 0.65em; line-height: 1.1;"><strong>বিভাগ:</strong> <?php echo htmlspecialchars($s['department'] ?? $s['group_name'] ?? ''); ?></p>
                                                        <p class="mb-0" style="font-size: 0.65em; line-height: 1.1;"><strong>শিক্ষাবর্ষ:</strong> <?php echo htmlspecialchars($s['academic_year'] ?? $s['session'] ?? ''); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endfor; ?>
                                </div>
                                
                                <?php if ($totalStudents > $cardsPerPage): ?>
                                    <div class="alert alert-light text-center">
                                        <p class="mb-0">
                                            Showing first <?php echo $previewCount; ?> cards. 
                                            <strong><?php echo $totalStudents - $previewCount; ?> more cards</strong> 
                                            will be printed on <?php echo $totalPages - 1; ?> additional pages.
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>🎨 Design Preview</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <strong>Preview:</strong> এটি একটি নমুনা সীটকার্ড ডিজাইন। আসল ডেটা যোগ করার পর সীটকার্ড তৈরী করুন।
                                </div>

                                <!-- Demo Card Preview -->
                                <div class="row justify-content-center">
                                    <div class="col-md-4">
                                        <div class="card border-secondary" style="font-size: 0.9em;">
                                            <div class="card-body p-2">
                                                <div class="text-center">
                                                    <div style="font-weight: bold; font-size: 0.8em; border-bottom: 1px solid #333; margin-bottom: 4px; padding-bottom: 2px;">
                                                        HSC Exam-2025
                                                    </div>
                                                    <div style="font-size: 0.7em; color: #666; margin-bottom: 4px;">
                                                        দামুড়হুদা, কোড. 295
                                                    </div>
                                                    <h6 class="card-title mb-2" style="font-size: 1em; line-height: 1.2;">
                                                        [শিক্ষার্থীর নাম]
                                                    </h6>
                                                    <div style="background: #f8f9fa; border: 1px solid #333; border-radius: 4px; padding: 6px; margin: 6px 0; font-weight: bold; font-size: 1.4em;">
                                                        [রোল]
                                                    </div>
                                                    <p class="mb-1" style="font-size: 0.75em; line-height: 1.2;"><strong>রেজিঃ</strong> [রেজিস্ট্রেশন]</p>
                                                    <p class="mb-1" style="font-size: 0.75em; line-height: 1.2;"><strong>বিভাগ:</strong> [বিভাগ]</p>
                                                    <p class="mb-0" style="font-size: 0.75em; line-height: 1.2;"><strong>শিক্ষাবর্ষ:</strong> [সেশন]</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-center mt-4">
                                    <div class="alert alert-light">
                                        <h6>📋 সীটকার্ড তৈরী করতে:</h6>
                                        <p class="mb-2">১. প্রথমে শিক্ষার্থী ডেটা যোগ করুন</p>
                                        <p class="mb-2">২. তারপর উপরের "Generate Cards" বাটনে ক্লিক করুন</p>
                                        <p class="mb-0">৩. প্রিন্ট করুন বা PDF সেভ করুন</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
