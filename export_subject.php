<?php
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/utils/DatabaseHelper.php';

// Get parameters from URL
$subjectCode = $_GET['code'] ?? '';
$searchGroup = $_GET['group'] ?? '';
$searchType = $_GET['type'] ?? '';
$searchGender = $_GET['gender'] ?? '';
$rollNumbers = $_GET['rolls'] ?? '';

if (empty($subjectCode)) {
    die('Subject code is required');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    $dbHelper = new DatabaseHelper($db);
    
    // Get subject name
    $subjectNames = [
        '101' => 'Bangla',
        '107' => 'English',
        '109' => 'ICT',
        '117' => 'Civics',
        '121' => 'Economics',
        '129' => 'Mathematics',
        '174' => 'Physics',
        '176' => 'Chemistry',
        '178' => 'Biology',
        '249' => 'Geography',
        '253' => 'Accounting',
        '265' => 'Higher Mathematics',
        '271' => 'History',
        '275' => 'Statistics',
        '277' => 'Finance',
        '286' => 'Business Studies',
        '292' => 'Management',
        '304' => 'Islamic Studies'
    ];
    
    $subjectName = $subjectNames[$subjectCode] ?? 'Unknown Subject';
    
    // Build query to find students with this subject
    $sql = "SELECT * FROM students WHERE ";
    $conditions = [];
    $params = [];

    // Handle specific roll numbers
    if (!empty($rollNumbers)) {
        $rollArray = explode(',', $rollNumbers);
        $rollArray = array_map('trim', $rollArray);
        $rollArray = array_filter($rollArray);

        if (!empty($rollArray)) {
            $rollConditions = [];
            foreach ($rollArray as $index => $roll) {
                $placeholder = 'roll' . $index;
                $rollConditions[] = ':' . $placeholder;
                $params[$placeholder] = $roll;
            }
            // Use correct roll field name based on what exists
            $rollColumn = $dbHelper->getRollColumnName();
            $sql .= "$rollColumn IN (" . implode(',', $rollConditions) . ")";
        }
    } else {
        // Use helper to build subject filter
        $subjectFilter = $dbHelper->buildSubjectFilterSQL($subjectCode);
        $conditions = explode(' WHERE ', $subjectFilter['sql']);
        $sql .= "(" . $conditions[1] . ")";
        $params = array_merge($params, $subjectFilter['params']);
    }

    // Add additional filters
    if (!empty($searchGroup)) {
        $sql .= " AND (group_name = :group_name OR department = :group_name_alt)";
        $params['group_name'] = $searchGroup;
        $params['group_name_alt'] = $searchGroup;
    }

    if (!empty($searchType)) {
        $sql .= " AND (type = :type OR student_type = :type_alt)";
        $params['type'] = $searchType;
        $params['type_alt'] = $searchType;
    }

    if (!empty($searchGender)) {
        $sql .= " AND gender = :gender";
        $params['gender'] = $searchGender;
    }

    // Add ORDER BY clause using helper
    $sql .= " " . $dbHelper->buildRollOrderBy();
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Set headers for CSV download
    $filenameParts = ["subject", $subjectCode, $subjectName];

    if (!empty($searchGroup)) {
        $filenameParts[] = $searchGroup;
    }
    if (!empty($searchType)) {
        $filenameParts[] = $searchType;
    }
    if (!empty($searchGender)) {
        $filenameParts[] = $searchGender;
    }
    if (!empty($rollNumbers)) {
        $filenameParts[] = "selected";
    }

    $filenameParts[] = date('Y-m-d_H-i-s');
    $filename = implode('_', $filenameParts) . ".csv";
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Create output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8 (helps with Bengali characters in Excel)
    fwrite($output, "\xEF\xBB\xBF");

    // Write export info as comments
    fputcsv($output, ["# Export Information"]);
    fputcsv($output, ["# Subject Code: $subjectCode"]);
    fputcsv($output, ["# Subject Name: $subjectName"]);
    fputcsv($output, ["# Export Date: " . date('Y-m-d H:i:s')]);
    fputcsv($output, ["# Total Students: " . count($students)]);

    if (!empty($searchGroup)) {
        fputcsv($output, ["# Filtered by Group: $searchGroup"]);
    }
    if (!empty($searchType)) {
        fputcsv($output, ["# Filtered by Type: $searchType"]);
    }
    if (!empty($searchGender)) {
        fputcsv($output, ["# Filtered by Gender: $searchGender"]);
    }
    if (!empty($rollNumbers)) {
        fputcsv($output, ["# Selected Roll Numbers: $rollNumbers"]);
    }

    fputcsv($output, []); // Empty line

    // Write CSV header
    $headers = [
        'ID',
        'Student Name',
        'Father Name',
        'Mother Name',
        'Roll No',
        'Registration',
        'Session',
        'Group',
        'Type',
        'Gender',
        'Date of Birth',
        'Mobile',
        'Email',
        'Address',
        'Subject Found In',
        'All Subjects'
    ];

    fputcsv($output, $headers);
    
    // Write student data
    foreach ($students as $student) {
        // Use helper to extract subjects
        $allSubjects = $dbHelper->extractSubjects($student);
        $foundInColumns = [];

        // Check if student has the subject
        if ($dbHelper->studentHasSubject($student, $subjectCode)) {
            if (!empty($student['subjects'])) {
                $foundInColumns[] = 'subjects';
            } else {
                // Find which old columns contain this subject
                for ($i = 1; $i <= 13; $i++) {
                    if (($student["sub_$i"] ?? '') == $subjectCode) {
                        $foundInColumns[] = "sub_$i";
                    }
                }
            }
        }

        // Use helper methods for field mapping
        $row = [
            $student['id'] ?? '',
            $dbHelper->getStudentName($student),
            $student['father_name'] ?? '',
            $student['mother_name'] ?? '',
            $dbHelper->getRollNumber($student),
            $dbHelper->getRegistrationNumber($student),
            $dbHelper->getAcademicYear($student),
            $dbHelper->getDepartment($student),
            $dbHelper->getStudentType($student),
            $student['gender'] ?? '',
            $student['date_of_birth'] ?? '',
            $student['mobile'] ?? '',
            $student['email'] ?? '',
            $student['address'] ?? '',
            implode(', ', $foundInColumns),
            implode(', ', $allSubjects)
        ];

        fputcsv($output, $row);
    }
    
    fclose($output);

    // Log the export (optional - you can store this in database if needed)
    error_log("CSV Export: Subject $subjectCode exported " . count($students) . " students at " . date('Y-m-d H:i:s'));

    exit;
    
} catch (Exception $e) {
    // Log the error
    error_log("CSV Export Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    // If debug mode is enabled, show detailed error
    if (isset($_GET['debug'])) {
        echo "<!DOCTYPE html><html><head><title>CSV Export Debug</title>";
        echo "<style>body{font-family:Arial,sans-serif;margin:20px;} pre{background:#f5f5f5;padding:10px;border-radius:5px;}</style></head><body>";
        echo "<h3>🐛 Debug Information</h3>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>Subject Code:</strong> " . htmlspecialchars($subjectCode ?? 'N/A') . "</p>";
        echo "<p><strong>SQL Query:</strong></p><pre>" . htmlspecialchars($sql ?? 'N/A') . "</pre>";
        echo "<p><strong>Parameters:</strong></p><pre>" . print_r($params ?? [], true) . "</pre>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<p><strong>Stack Trace:</strong></p><pre>" . $e->getTraceAsString() . "</pre>";

        // Check database connection and structure
        try {
            $testDb = new Database();
            $testConn = $testDb->getConnection();
            $testHelper = new DatabaseHelper($testConn);
            echo "<p><strong>Database Connection:</strong> ✅ OK</p>";

            // Check if students table exists
            $stmt = $testConn->query("SHOW TABLES LIKE 'students'");
            if ($stmt->rowCount() > 0) {
                echo "<p><strong>Students Table:</strong> ✅ Exists</p>";

                // Get debug info from helper
                $debugInfo = $testHelper->getDebugInfo();
                echo "<p><strong>Database Structure Analysis:</strong></p>";
                echo "<pre>" . print_r($debugInfo, true) . "</pre>";

                // Show recommended approach
                if ($debugInfo['has_new_subjects_field'] && !$debugInfo['has_old_columns']) {
                    echo "<p><strong>✅ Recommended:</strong> Using new 'subjects' field format</p>";
                } elseif ($debugInfo['has_old_columns'] && !$debugInfo['has_new_subjects_field']) {
                    echo "<p><strong>⚠️ Legacy:</strong> Using old 'sub_1' to 'sub_13' columns</p>";
                } elseif ($debugInfo['has_both']) {
                    echo "<p><strong>🔄 Hybrid:</strong> Both formats available, using new format primarily</p>";
                } else {
                    echo "<p><strong>❌ Problem:</strong> No subject columns found!</p>";
                }
            } else {
                echo "<p><strong>Students Table:</strong> ❌ Not Found</p>";
            }
        } catch (Exception $dbError) {
            echo "<p><strong>Database Connection:</strong> ❌ " . htmlspecialchars($dbError->getMessage()) . "</p>";
        }

        echo "<hr><p><a href='subject_filter.php'>← Back to Subject Filter</a></p>";
        echo "</body></html>";
    } else {
        // User-friendly error message
        echo "<!DOCTYPE html><html><head><title>Export Error</title>";
        echo "<style>body{font-family:Arial,sans-serif;margin:20px;text-align:center;} .error{color:#d32f2f;background:#ffebee;padding:20px;border-radius:10px;display:inline-block;}</style></head><body>";
        echo "<div class='error'>";
        echo "<h3>❌ CSV Export Error</h3>";
        echo "<p>দুঃখিত, CSV export করতে সমস্যা হয়েছে।</p>";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><a href='?debug=1&" . htmlspecialchars($_SERVER['QUERY_STRING'] ?? '') . "'>🐛 Debug Mode</a> | ";
        echo "<a href='subject_filter.php'>← Back to Subject Filter</a></p>";
        echo "</div></body></html>";
    }
}
?>
