<?php
require_once '../models/Student.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ids']) && is_array($_POST['ids'])) {
    $studentIds = array_map('intval', $_POST['ids']);
    $studentIds = array_filter($studentIds, function($id) {
        return $id > 0;
    });
    
    if (empty($studentIds)) {
        echo json_encode([
            'success' => false,
            'message' => 'কোন বৈধ শিক্ষার্থী ID পাওয়া যায়নি।'
        ]);
        exit;
    }
    
    try {
        $student = new Student();
        $successCount = 0;
        $failedCount = 0;
        $errors = [];
        
        foreach ($studentIds as $studentId) {
            try {
                // Check if student exists
                $studentData = $student->getById($studentId);
                if (!$studentData) {
                    $failedCount++;
                    $errors[] = "ID $studentId: শিক্ষার্থী পাওয়া যায়নি।";
                    continue;
                }
                
                // Delete the student
                $result = $student->delete($studentId);
                
                if ($result) {
                    $successCount++;
                } else {
                    $failedCount++;
                    $errors[] = "ID $studentId: মুছে ফেলতে সমস্যা হয়েছে।";
                }
            } catch (Exception $e) {
                $failedCount++;
                $errors[] = "ID $studentId: " . $e->getMessage();
            }
        }
        
        $totalRequested = count($studentIds);
        
        if ($successCount === $totalRequested) {
            echo json_encode([
                'success' => true,
                'message' => "$successCount জন শিক্ষার্থী সফলভাবে মুছে ফেলা হয়েছে।"
            ]);
        } else if ($successCount > 0) {
            echo json_encode([
                'success' => true,
                'message' => "$successCount জন শিক্ষার্থী মুছে ফেলা হয়েছে, $failedCount জন ব্যর্থ হয়েছে।",
                'errors' => $errors
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => "কোন শিক্ষার্থী মুছে ফেলা যায়নি।",
                'errors' => $errors
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'সার্ভার এরর: ' . $e->getMessage()
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'অবৈধ অনুরোধ। কোন শিক্ষার্থী ID পাওয়া যায়নি।'
    ]);
}
?>
